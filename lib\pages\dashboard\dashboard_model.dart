import 'dart:async';
import 'dart:io';

import 'package:cal_counti_a_i/auth/firebase_auth/auth_util.dart';

import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/componentes/circular_progress/circular_progress_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dashboard_widget.dart' show DashboardWidget;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';

class DashboardModel extends FlutterFlowModel<DashboardWidget> {
  ///  Local state fields for this page.

  DateTime? selectedDate;
  Map<String, bool> mealUploadStatus = {};
  Map<String, bool> mealRetryAvailable = {};

  DashboardDataStruct? dashboardData;

  void updateDashboardDataStruct(Function(DashboardDataStruct) updateFn) {
    updateFn(dashboardData ??= DashboardDataStruct());
  }

  bool isInProgress = false;
  bool imageUploading = false;

  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - API (Dashboard Data)] action in dashboard widget.
  ApiCallResponse? dashboardApiResult;

  // Stores action output result for [Backend Call - API (Dashboard Data)] action in CalendarHeaderUI widget.
  ApiCallResponse? apiResultDashboardData;

  // Model for circular_progress component.
  late CircularProgressModel circularProgressModel;

  // Stores action output result for [Backend Call - API (Dashboard Data)] action in FloatingActionButton widget.
  ApiCallResponse? dashboardApiResult1;

  @override
  void initState(BuildContext context) {
    circularProgressModel = createModel(context, () => CircularProgressModel());
  }

  @override
  void dispose() {
    circularProgressModel.dispose();
  }

  Future<bool> _checkNetworkConnectivity() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return false;
    }
    return true;
  }

  Future<void> updateTotalScannedCount() async {
    if (currentUserReference == null) {
      return;
    }
    var userData = await UsersRecord.getDocumentOnce(currentUserReference!);
    int totalScannedCount = userData.totalScannedCount;
    await currentUserReference!.update(createUsersRecordData(
      totalScannedCount: (totalScannedCount + 1),
    ));
  }

  Future<bool> uploadMealImage(
    BuildContext context, {
    required String? imagePath,
    required String token,
    required bool isEnglish,
    String? barcodeNumber,
  }) async {
    final url = Uri.parse('$baseUrl/meal/add');
    final request = http.MultipartRequest('POST', url);
    request.headers.addAll({
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
      'Connection': 'keep-alive',
    });

    print('Uploading to $url');
    print('Headers: ${request.headers}');

    request.fields['type'] = 'barcode';
    if (barcodeNumber != null) {
      request.fields['barcode'] = barcodeNumber;
      print('Fields: ${request.fields}');
    } else {
      print('Fields: ${request.fields}');
    }

    imageUploading = true;
    if (imagePath != null && imagePath.trim().isNotEmpty) {
      final file = await http.MultipartFile.fromPath(
        'image',
        imagePath,
      );
      request.files.add(file);
    }
    print('Files included: image ($imagePath)');

    try {
      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 60),
        onTimeout: () {
          throw TimeoutException('Upload timed out');
        },
      );
      final response = await http.Response.fromStream(streamedResponse);
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        await updateTotalScannedCount();
        print('Upload successful: ${response.body}');
        imageUploading = false;
        return true;
      } else {
        imageUploading = false;
        try {
          final responseJson = jsonDecode(response.body);
          final errorMessage = responseJson['message'] ?? 'Unknown error';
          if (errorMessage == 'Invalid barcode') {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(isEnglish
                    ? 'The barcode is not recognized by the server. Please try a different barcode.'
                    : 'הגרקוד אינו מזוהה על ידי השר.'),
              ),
            );
          }
          throw HttpException(
              'Upload failed: ${response.statusCode} - $errorMessage');
        } catch (e) {
          throw HttpException(
              'Upload failed: ${response.statusCode} - ${response.body}');
        }
      }
    } catch (e) {
      imageUploading = false;
      throw Exception('Failed to upload: $e');
    }
  }

  Future<bool> uploadWithRetry({
    required BuildContext context,
    String? imagePath,
    required String token,
    required bool isEnglish,
    String? barcodeNumber,
  }) async {
    const int maxRetries = 3;
    int attempt = 0;

    bool isConnected = await _checkNetworkConnectivity();
    if (!isConnected) {
      throw Exception(isEnglish
          ? 'No internet connection. Please check your network and try again.'
          : 'אין חיבור לאינטרנט. אנא בדוק את הרשת שלך ונסה שוב.');
    }

    while (attempt < maxRetries) {
      attempt++;
      print('Upload attempt $attempt of $maxRetries');
      try {
        return await uploadMealImage(
          context,
          imagePath: imagePath,
          token: token,
          isEnglish: isEnglish,
          barcodeNumber: barcodeNumber,
        );
      } catch (e) {
        if (e.toString().contains('TimeoutException') && attempt < maxRetries) {
          print('Timeout on attempt $attempt. Retrying...');
          await Future.delayed(Duration(seconds: 3));
          continue;
        } else if (e.toString().contains('Invalid barcode') &&
            attempt < maxRetries) {
          print('Invalid barcode on attempt $attempt. Retrying...');
          await Future.delayed(Duration(seconds: 1));
          continue;
        }
        throw e;
      }
    }

    throw Exception('Failed to upload after $maxRetries retries');
  }
}

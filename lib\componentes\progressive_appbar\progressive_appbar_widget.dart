import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';
import 'progressive_appbar_model.dart';
export 'progressive_appbar_model.dart';

class ProgressiveAppbarWidget extends StatefulWidget {
  const ProgressiveAppbarWidget({
    super.key,
    required this.pageIndex,
    required this.onBackTap,
  });

  final int? pageIndex;
  final Future Function()? onBackTap;

  @override
  State<ProgressiveAppbarWidget> createState() =>
      _ProgressiveAppbarWidgetState();
}

class _ProgressiveAppbarWidgetState extends State<ProgressiveAppbarWidget> {
  late ProgressiveAppbarModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ProgressiveAppbarModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        if (widget.pageIndex! <= 9)
          Container(
            width: double.infinity,
            height: valueOrDefault<double>(
              FFAppState().topPadding,
              45.0,
            ),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).primaryBackground,
            ),
          ),
        if (widget.pageIndex! <= 9)
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    logFirebaseEvent('Image_wait__delay');
                    await Future.delayed(const Duration(milliseconds: 1));
                    logFirebaseEvent('Image_haptic_feedback');
                    HapticFeedback.lightImpact();
                    logFirebaseEvent('Image_execute_callback');
                    await widget.onBackTap?.call();
                  },
                  child: Image.asset(
                    'assets/images/back.png',
                    width: 30.0,
                    height: 30.0,
                    fit: BoxFit.contain,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                    child: LinearPercentIndicator(
                      percent: valueOrDefault<double>(
                        (((widget.pageIndex!) + 1) / 10)
                            .toDouble()
                            .clamp(0.0, 1.0),
                        0.1,
                      ),
                      width: MediaQuery.sizeOf(context).width * 0.8,
                      lineHeight: 12.0,
                      animation: true,
                      animateFromLastPercent: true,
                      progressColor: FlutterFlowTheme.of(context).primary,
                      backgroundColor: FlutterFlowTheme.of(context).alternate,
                      barRadius: Radius.circular(24.0),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:webviewx_plus/webviewx_plus.dart';

import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/componentes/ingredient_item/ingredient_item_widget.dart';
import '/componentes/no_ingredient/no_ingredient_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'dart:math';
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';

// import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:provider/provider.dart';
import 'meal_details_model.dart';
export 'meal_details_model.dart';

class MealDetailsWidget extends StatefulWidget {
  /// Create a detailed recipe screen in Flutter using a SliverAppBar for a
  /// collapsible app bar effect. The SliverAppBar should feature a full-screen
  /// image of the dish (Garlic Butter Shrimp) as its expanded background.
  /// Include a back arrow icon aligned to the top-left corner, along with a
  /// dynamic title ('Breakfast') that appears prominently in the toolbar as the
  /// app bar collapses during scrolling. Below the SliverAppBar, integrate a
  /// CustomScrollView to handle the scrollable content. The main dish image
  /// should overlay the bottom portion of the app bar with a slight gradient
  /// effect to ensure readability of overlaid text.  Display the recipe name
  /// ('Garlic Butter Shrimp') in bold white font on the image background,
  /// centered for emphasis. Below this, add a horizontal row showcasing key
  /// nutritional information: '20g Proteins,' '340 Calories,' '1 Net Carbs,'
  /// and '29 Fats,' each with a corresponding icon for visual clarity. Use
  /// consistent padding and alignment to maintain a clean look.  Below the
  /// nutritional information, include a section labeled 'Health Score' inside a
  /// styled card. Use a heart icon in red, followed by the score '7/10,'
  /// ensuring proper spacing and alignment for a professional appearance.
  /// Beneath the health score, add an 'Ingredients List' section with a bold
  /// header. For each ingredient, provide its name in bold text (e.g.,
  /// 'Shrimp'), the quantity (e.g., '100 g') aligned to the right in a slightly
  /// lighter font weight, and a smaller, detailed description below the
  /// ingredient name (e.g., 'For 6 prawns, approximately 100 grams of prawns
  /// would be used. This can vary depending on the size of the prawns').
  /// Ensure the CustomScrollView is smooth and responsive, with proper padding
  /// between all sections and consistent use of fonts, colors, and spacing
  /// throughout the screen. The design should prioritize readability, aesthetic
  /// balance, and a seamless scrolling experience."
  const MealDetailsWidget({
    super.key,
    required this.mMealItem,
    required this.imageUrl,
  });

  final MealDetailStruct? mMealItem;
  final String? imageUrl;

  @override
  State<MealDetailsWidget> createState() => _MealDetailsWidgetState();
}

class _MealDetailsWidgetState extends State<MealDetailsWidget>
    with TickerProviderStateMixin {
  late MealDetailsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => MealDetailsModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('meal_details_backend_call');
      _model.apiResultMealDetail = await MealDetailCall.call(
        accessToken: FFAppState().authToken,
        mealId: widget.mMealItem?.id,
      );

      if ((_model.apiResultMealDetail?.succeeded ?? true)) {
        logFirebaseEvent('meal_details_update_page_state');
        _model.mealDetail = MealDetailCall.data(
          (_model.apiResultMealDetail?.jsonBody ?? ''),
        );
        _model.isInProgress = false;
        safeSetState(() {});
      } else {
        logFirebaseEvent('meal_details_update_page_state');
        _model.isInProgress = false;
        safeSetState(() {});
      }
    });

    animationsMap.addAll({
      'progressBarOnPageLoadAnimation1': AnimationInfo(
        loop: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
      'progressBarOnPageLoadAnimation2': AnimationInfo(
        loop: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'MealDetails',
        builder: (context) => GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            body: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                    child: Container(
                      width: MediaQuery.sizeOf(context).width * 1.0,
                      height: 340.0,
                      child: Stack(
                        children: [
                          Hero(
                            tag: ValueKey(widget.mMealItem!.id),
                            key: ValueKey(widget.mMealItem!.id),
                            transitionOnUserGestures: true,
                            child: CachedNetworkImage(
                              key: ValueKey(widget.imageUrl!),
                              fadeInDuration: Duration(milliseconds: 600),
                              fadeOutDuration: Duration(milliseconds: 600),
                              imageUrl: '${widget.imageUrl}',
                              width: double.infinity,
                              height: 340.0,
                              fit: BoxFit.cover,
                              errorWidget: (context, error, stackTrace) =>
                                  Image.asset(
                                'assets/images/error_image.png',
                                width: double.infinity,
                                height: 340.0,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Container(
                            width: double.infinity,
                            height: 340.0,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.transparent, Color(0xAA000000)],
                                stops: [0.0, 0.5],
                                begin: AlignmentDirectional(0.0, -1.0),
                                end: AlignmentDirectional(0, 1.0),
                              ),
                            ),
                          ),
                          Builder(
                            builder: (context) {
                              return Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 16.0, 16.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0,
                                          valueOrDefault<double>(
                                            FFAppState().topPadding,
                                            0.0,
                                          ),
                                          0.0,
                                          0.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          FlutterFlowIconButton(
                                            borderRadius: 30.0,
                                            borderWidth: 1.0,
                                            buttonSize: 40.0,
                                            icon: Icon(
                                              Icons.chevron_left_rounded,
                                              color: Colors.white,
                                              size: 24.0,
                                            ),
                                            onPressed: () async {
                                              logFirebaseEvent(
                                                  'IconButton_navigate_back');
                                              context.safePop();
                                            },
                                          ),
                                          Expanded(
                                            child: Opacity(
                                              opacity: 0.0,
                                              child: Text(
                                                FFLocalizations.of(context).getText(
                                                  '2toygafq' /* Breakfast */,
                                                ),
                                                style: FlutterFlowTheme.of(context)
                                                    .labelLarge
                                                    .override(
                                                      fontFamily: 'SFHebrew',
                                                      color: Colors.white,
                                                      letterSpacing: 0.0,
                                                    ),
                                              ),
                                            ),
                                          ),
                                          FlutterFlowIconButton(
                                            borderRadius: 30.0,
                                            borderWidth: 1.0,
                                            buttonSize: 40.0,
                                            icon: Icon(
                                              Icons.delete_forever_outlined,
                                              color: Colors.red,
                                              size: 24.0,
                                            ),
                                            onPressed: () async {
                                              var confirmDialogResponse =
                                                  await showDialog<bool>(
                                                        context: context,
                                                        builder:
                                                            (alertDialogContext) {
                                                          return WebViewAware(
                                                            child: AlertDialog(
                                                              title: Text(isEnglish
                                                                  ? 'Delete Meal?'
                                                                  : 'למחוק ארוחה?'),
                                                              content: Text(isEnglish
                                                                  ? 'Are you sure you want to delete meal data? '
                                                                      'This action cannot be undone, and all your calories, carbs, fiber, and related data will be permanently removed.'
                                                                  : 'האם אתה בטוח שברצונך למחוק את נתוני הארוחה? '
                                                                      'פעולה זו אינה ניתנת לביטול וכל הקלוריות, הפחמימות, הסיבים התזונתיים והנתונים הקשורים יימחקו לצמיתות.'),
                                                              actions: [
                                                                TextButton(
                                                                  onPressed: () =>
                                                                      Navigator.pop(
                                                                          alertDialogContext,
                                                                          false),
                                                                  child: Text(
                                                                      FFLocalizations.of(
                                                                              context)
                                                                          .getText(
                                                                    '5efik18d' /* Cancel */,
                                                                  )),
                                                                ),
                                                                TextButton(
                                                                  onPressed: () =>
                                                                      Navigator.pop(
                                                                          alertDialogContext,
                                                                          true),
                                                                  child: Text(
                                                                      FFLocalizations.of(
                                                                              context)
                                                                          .getText(
                                                                    '4efik18d' /* Confirm */,
                                                                  )),
                                                                ),
                                                              ],
                                                            ),
                                                          );
                                                        },
                                                      ) ??
                                                      false;

                                              if (confirmDialogResponse) {
                                                logFirebaseEvent(
                                                    'IconButton_delete_meal');
                                                showDialog(
                                                  context: context,
                                                  builder: (dialogContext) {
                                                    return Dialog(
                                                      elevation: 0,
                                                      insetPadding: EdgeInsets.zero,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      alignment:
                                                          AlignmentDirectional(
                                                                  0.0, 0.0)
                                                              .resolve(
                                                                  Directionality.of(
                                                                      context)),
                                                      child: WebViewAware(
                                                        child: GestureDetector(
                                                          onTap: () {
                                                            FocusScope.of(
                                                                    dialogContext)
                                                                .unfocus();
                                                            FocusManager.instance
                                                                .primaryFocus
                                                                ?.unfocus();
                                                          },
                                                          child: Container(
                                                            height: 80.0,
                                                            width: 80.0,
                                                            child:
                                                                CircularProgressWidget(),
                                                          ),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );

                                                _model.deleteMealResponse =
                                                    await DeleteMealCall.call(
                                                  accessToken:
                                                      FFAppState().authToken,
                                                  jsonJson: <String, dynamic>{
                                                    'meal_id': widget.mMealItem?.id
                                                  },
                                                );
                                                await Future.delayed(const Duration(
                                                    milliseconds: 50));
                                                Navigator.pop(context);

                                                if ((_model.deleteMealResponse
                                                        ?.succeeded ??
                                                    true)) {
                                                  logFirebaseEvent(
                                                      'Container_navigate_back');
                                                  await Future.delayed(
                                                      const Duration(
                                                          milliseconds: 50));
                                                  Navigator.of(context).pop(true);
                                                }
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          valueOrDefault<String>(
                                            _model.mealDetail?.name,
                                            '-',
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .headlineLarge
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                color: Colors.white,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  valueOrDefault<String>(
                                                    _model
                                                        .mealDetail?.totalProteins,
                                                    '0',
                                                  ),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .override(
                                                        fontFamily: 'SFHebrew',
                                                        color: FlutterFlowTheme.of(
                                                                context)
                                                            .alternate,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                                Text(
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    'ggf2jdx6' /* Protein */,
                                                  ),
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .labelSmall
                                                          .override(
                                                            fontFamily: 'SFHebrew',
                                                            color:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .alternate,
                                                            letterSpacing: 0.0,
                                                          ),
                                                ),
                                              ].divide(SizedBox(height: 4.0)),
                                            ),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  valueOrDefault<String>(
                                                    _model
                                                        .mealDetail?.totalCalories,
                                                    '0',
                                                  ),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .override(
                                                        fontFamily: 'SFHebrew',
                                                        color: FlutterFlowTheme.of(
                                                                context)
                                                            .alternate,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                                Text(
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    'vpquyai3' /* Calories */,
                                                  ),
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .labelSmall
                                                          .override(
                                                            fontFamily: 'SFHebrew',
                                                            color:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .alternate,
                                                            letterSpacing: 0.0,
                                                          ),
                                                ),
                                              ].divide(SizedBox(height: 4.0)),
                                            ),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  valueOrDefault<String>(
                                                    _model.mealDetail?.totalCarbs,
                                                    '0',
                                                  ),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .override(
                                                        fontFamily: 'SFHebrew',
                                                        color: FlutterFlowTheme.of(
                                                                context)
                                                            .alternate,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                                Text(
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    'yt9jxb1i' /* Carbs */,
                                                  ),
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .labelSmall
                                                          .override(
                                                            fontFamily: 'SFHebrew',
                                                            color:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .alternate,
                                                            letterSpacing: 0.0,
                                                          ),
                                                ),
                                              ].divide(SizedBox(height: 4.0)),
                                            ),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Text(
                                                  valueOrDefault<String>(
                                                    _model.mealDetail?.totalFats,
                                                    '0',
                                                  ),
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .override(
                                                        fontFamily: 'SFHebrew',
                                                        color: FlutterFlowTheme.of(
                                                                context)
                                                            .alternate,
                                                        letterSpacing: 0.0,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                ),
                                                Text(
                                                  FFLocalizations.of(context)
                                                      .getText(
                                                    'gwkjxrgf' /* Fats */,
                                                  ),
                                                  style:
                                                      FlutterFlowTheme.of(context)
                                                          .labelSmall
                                                          .override(
                                                            fontFamily: 'SFHebrew',
                                                            color:
                                                                FlutterFlowTheme.of(
                                                                        context)
                                                                    .alternate,
                                                            letterSpacing: 0.0,
                                                          ),
                                                ),
                                              ].divide(SizedBox(height: 4.0)),
                                            ),
                                          ].divide(SizedBox(width: 8.0)),
                                        ),
                                        Container(
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .secondaryBackground,
                                            borderRadius:
                                                BorderRadius.circular(12.0),
                                            border: Border.all(
                                              color: FlutterFlowTheme.of(context)
                                                  .alternate,
                                              width: 1.0,
                                            ),
                                          ),
                                          child: Padding(
                                            padding: EdgeInsets.all(4.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 40.0,
                                                  height: 40.0,
                                                  decoration: BoxDecoration(),
                                                  child: SvgPicture.asset(
                                                    'assets/images/health_svgrepo.com.svg',
                                                    width: 200.0,
                                                    height: 200.0,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Row(
                                                    mainAxisSize: MainAxisSize.max,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        FFLocalizations.of(context)
                                                            .getText(
                                                          'oi81tg2x' /* Health Score */,
                                                        ),
                                                        style: FlutterFlowTheme.of(
                                                                context)
                                                            .headlineSmall
                                                            .override(
                                                              fontFamily:
                                                                  'SFHebrew',
                                                              fontSize: 18.0,
                                                              letterSpacing: 0.0,
                                                            ),
                                                      ),
                                                      Text(
                                                        (String var1) {
                                                          return "${var1}/10";
                                                        }(valueOrDefault<String>(
                                                          _model.mealDetail
                                                              ?.healthScore
                                                              .toString(),
                                                          '0',
                                                        )),
                                                        style: FlutterFlowTheme.of(
                                                                context)
                                                            .displaySmall
                                                            .override(
                                                              fontFamily:
                                                                  'SFHebrew',
                                                              color: FlutterFlowTheme
                                                                      .of(context)
                                                                  .primary,
                                                              fontSize: 16.0,
                                                              letterSpacing: 0.0,
                                                            ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ].divide(SizedBox(width: 16.0)),
                                            ),
                                          ),
                                        ),
                                      ].divide(SizedBox(height: 8.0)),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                    child: Builder(
                      builder: (context) {
                        return Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              16.0, 16.0, 16.0, 16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                FFLocalizations.of(context).getText(
                                  'yil1xe7g' /* Ingredients List */,
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .headlineSmall
                                    .override(
                                  fontFamily: 'SFHebrew',
                                  letterSpacing: 0.0,
                                ),
                              ),
                              Builder(
                                builder: (context) {
                                  final ingredientsItems =
                                      _model.mealDetail?.items.toList() ?? [];
                                  if (ingredientsItems.isEmpty) {
                                    return NoIngredientWidget();
                                  }

                                  return ListView.separated(
                                    padding: EdgeInsets.zero,
                                    primary: false,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemCount: ingredientsItems.length,
                                    separatorBuilder: (_, __) =>
                                        SizedBox(height: 16.0),
                                    itemBuilder:
                                        (context, ingredientsItemsIndex) {
                                      final ingredientsItemsItem =
                                      ingredientsItems[ingredientsItemsIndex];
                                      return wrapWithModel(
                                        model:
                                        _model.ingredientItemModels.getModel(
                                          ingredientsItemsIndex.toString(),
                                          ingredientsItemsIndex,
                                        ),
                                        updateCallback: () => safeSetState(() {}),
                                        child: IngredientItemWidget(
                                          key: Key(
                                            'Keys93_${ingredientsItemsIndex.toString()}',
                                          ),
                                          itemData: ingredientsItemsItem,
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            ].divide(SizedBox(height: 10.0)),
                          ),
                        );
                      },
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                  ),
                ].divide(SizedBox(height: 10.0)),
              ),
            ),
          ),
        )
    );
  }
}

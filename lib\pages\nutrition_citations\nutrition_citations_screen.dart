import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:ui' as ui;

class NutritionCitationsScreen extends StatefulWidget {
  const NutritionCitationsScreen({Key? key}) : super(key: key);

  @override
  _NutritionCitationsScreenState createState() =>
      _NutritionCitationsScreenState();
}

class _NutritionCitationsScreenState extends State<NutritionCitationsScreen> {
  late bool isHebrewLanguage;

  @override
  void initState() {
    super.initState();
    isHebrewLanguage = FFLocalizations.getStoredLocale() == Locale('he');
  }

  final List<Map<String, dynamic>> nutritionSources = [
    {
      'category': {'en': 'Nutritional Foundations', 'he': 'יסודות תזונתיים'},
      'color': Color(0xFF4A90E2),
      'sources': [
        {
          'name': {
            'en': 'USDA Nutrient Database',
            'he': 'מאגר נתוני תזונה של משרד החקלאות האמריקאי'
          },
          'description': {
            'en': 'Comprehensive food composition research platform',
            'he': 'פלטפורמת מחקר מקיפה של הרכב מזון'
          },
          'impact': {
            'en': 'Global Standard in Nutritional Data',
            'he': 'תקן עולמי בנתוני תזונה'
          },
          'accuracy': 0.95,
          'url': 'https://fdc.nal.usda.gov/',
          'icon': Icons.food_bank_outlined
        },
        {
          'name': {
            'en': 'NIH Nutrition Research',
            'he': 'מחקר התזונה של המכון הלאומי לבריאות'
          },
          'description': {
            'en': 'Advanced nutritional science and dietary insights',
            'he': 'מדע תזונתי מתקדם ותובנות תזונתיות'
          },
          'impact': {
            'en': 'Leading Health Research Institution',
            'he': 'מוסד מוביל במחקר בריאותי'
          },
          'accuracy': 0.98,
          'url':
              'https://www.fda.gov/food/nutrition-food-labeling-and-critical-foods/fdas-nutrition-initiatives',
          'icon': Icons.science_outlined
        }
      ]
    },
    {
      'category': {'en': 'Advanced Food Recognition', 'he': 'זיהוי מזון מתקדם'},
      'color': Color(0xFF2ECC71),
      'sources': [
        {
          'name': {
            'en': 'FDA Food Composition',
            'he': 'הרכב מזון של מנהל המזון והתרופות'
          },
          'description': {
            'en': 'Precise food ingredient and nutritional mapping',
            'he': 'מיפוי מדויק של רכיבי מזון ותזונה'
          },
          'impact': {
            'en': 'National Food Safety Standard',
            'he': 'תקן לאומי לבטיחות מזון'
          },
          'accuracy': 0.92,
          'url':
              'https://www.fda.gov/food/food-additives-and-gras-ingredients-information-consumers/types-food-ingredients',
          'icon': Icons.camera_outlined
        }
      ]
    }
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                isHebrewLanguage ? 'מקורות תזונה' : 'Nutrition Citations',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF4A90E2),
                      Color(0xFF2ECC71),
                    ],
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.source_outlined,
                    size: 100,
                    color: Colors.white.withOpacity(0.5),
                  ),
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, categoryIndex) {
                  final category = nutritionSources[categoryIndex];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Text(
                          category['category'][isHebrewLanguage ? 'he' : 'en'],
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w800,
                            color: category['color'],
                          ),
                        ),
                      ),
                      ...category['sources'].map<Widget>((source) {
                        return _buildSourceCard(source, category['color']);
                      }).toList(),
                    ],
                  );
                },
                childCount: nutritionSources.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceCard(Map<String, dynamic> source, Color categoryColor) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _launchURL(source['url']),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: categoryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      source['icon'],
                      color: categoryColor,
                      size: 30,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          source['name'][isHebrewLanguage ? 'he' : 'en'],
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          source['description'][isHebrewLanguage ? 'he' : 'en'],
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                        SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildAccuracyIndicator(source['accuracy']),
                            Text(
                              source['impact'][isHebrewLanguage ? 'he' : 'en'],
                              style: TextStyle(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                                color: categoryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAccuracyIndicator(double accuracy) {
    return Row(
      children: [
        Text(
          isHebrewLanguage ? 'דִיוּק:' : 'Accuracy: ',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Stack(
          children: [
            Container(
              width: 50,
              height: 6,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            Container(
              width: 50 * accuracy,
              height: 6,
              decoration: BoxDecoration(
                color: _getAccuracyColor(accuracy),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ],
        ),
        SizedBox(width: 8),
        Text(
          '${(accuracy * 100).toStringAsFixed(0)}%',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: _getAccuracyColor(accuracy),
          ),
        ),
      ],
    );
  }

  Color _getAccuracyColor(double accuracy) {
    if (accuracy > 0.9) return Colors.green;
    if (accuracy > 0.7) return Colors.orange;
    return Colors.red;
  }

  Future<void> _launchURL(String url) async {
    try {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      print('Could not launch $url');
    }
  }
}

class InitialWelcomeDialog extends StatefulWidget {
  const InitialWelcomeDialog({Key? key}) : super(key: key);

  @override
  _InitialWelcomeDialogState createState() => _InitialWelcomeDialogState();
}

class _InitialWelcomeDialogState extends State<InitialWelcomeDialog> {
  bool isHebrewLanguage = FFLocalizations.getStoredLocale() == Locale('he');

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Directionality(
        textDirection:
            isHebrewLanguage ? ui.TextDirection.rtl : ui.TextDirection.ltr,
        child: Container(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.info_outline,
                size: 80,
                color: Colors.green,
              ),
              SizedBox(height: 20),
              Text(
                isHebrewLanguage
                    ? 'ברוכים הבאים לאפליקציה שלנו'
                    : 'Welcome to Our Nutrition App',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 15),
              Text(
                isHebrewLanguage
                    ? 'אנו שמחים שהצטרפת אלינו. יישומון זה יעזור לך לנהל ולהבין את התזונה שלך באופן טוב יותר.'
                    : 'We are excited that you joined us. This app will help you understand your nutrition better.',
                style: TextStyle(
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 30),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  backgroundColor: Colors.green,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () async {
                  Navigator.pop(context);
                  FFAppState().todayShown = true;
                  // Close the dialog and navigate to NutritionCitationsScreen

                  await context.pushNamed(
                    'nutrition_citations',
                    extra: <String, dynamic>{
                      kTransitionInfoKey: const TransitionInfo(
                        hasTransition: true,
                        transitionType: PageTransitionType.bottomToTop,
                      ),
                    },
                  );
                },
                child: Text(
                  isHebrewLanguage ? 'המשך' : 'Continue',
                  style: TextStyle(
                    fontSize: 18,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:cal_counti_a_i/pages/overview_food/edit_weight.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';

class FoodLogWeightCard extends StatelessWidget {
  final VoidCallback onLogWeight;
  final String weight;
  final int daysLogged;
  final int totalDays;

  const FoodLogWeightCard({
    super.key,
    required this.onLogWeight,
    required this.weight,
    required this.daysLogged,
    required this.totalDays,
  });

  @override
  Widget build(BuildContext context) {
    final bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Row(
      children: [
        // Log Weight Card
        Expanded(
          child: Stack(
            children: [
              Container(
                height: 180,
                width: double.infinity,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 22,
                    ),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          height: 60,
                          width: 60,
                          child: CircularProgressIndicator(
                            value: 0.05, // 1/20 for demo
                            strokeWidth: 6,
                            backgroundColor: const Color(0xFFF1F1F1),
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.black),
                          ),
                        ),
                        Image.asset(
                          'assets/images/weight-scale.png',
                          height: 20,
                          width: 20,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      isEnglish ? 'Time to weigh in!' : 'הגיע הזמן לשקול!',
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                    ),
                    const SizedBox(height: 24),
                    // Spacer(),
                    // GestureDetector(
                    //   onTap: onLogWeight,
                    //   child: Container(
                    //     height: 35,
                    //     width: double.infinity,
                    //     decoration: BoxDecoration(
                    //         color: Colors.black,
                    //         borderRadius: BorderRadius.only(
                    //             bottomLeft: Radius.circular(16),
                    //             bottomRight: Radius.circular(16))),
                    //     child: Center(
                    //       child: Padding(
                    //         padding: const EdgeInsets.symmetric(horizontal: 15),
                    //         child: Row(
                    //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //           children: [
                    //             Text(
                    //               isEnglish ? 'Log weight' : 'רשום משקל',
                    //               style: FlutterFlowTheme.of(context)
                    //                   .titleLarge
                    //                   .override(
                    //                     fontSize: 14,
                    //                     color: Colors.white,
                    //                     fontWeight: FontWeight.w500,
                    //                   ),
                    //             ),
                    //             Icon(
                    //               CupertinoIcons.arrow_right,
                    //               color: Colors.white,
                    //               size: 18,
                    //             )
                    //           ],
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: GestureDetector(
                  onTap: onLogWeight,
                  child: Container(
                    height: 35,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        color: Colors.black,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(16),
                            bottomRight: Radius.circular(16))),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              isEnglish ? 'Log weight' : 'רשום משקל',
                              style: FlutterFlowTheme.of(context)
                                  .titleLarge
                                  .override(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                            Icon(
                              CupertinoIcons.arrow_right,
                              color: Colors.white,
                              size: 18,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          width: 10,
        ),
        // Days Logged Card
        Expanded(
          child: Container(
            height: 180,
            margin: const EdgeInsets.only(left: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      height: 60,
                      width: 60,
                      child: CircularProgressIndicator(
                        value: daysLogged / totalDays,
                        strokeWidth: 6,
                        backgroundColor: const Color(0xFFF1F1F1),
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                      ),
                    ),
                    Image.asset(
                      'assets/images/apple.png',
                      height: 20,
                      width: 20,
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  isEnglish ? 'Days logged' : 'ימים שנרשמו',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  isEnglish
                      ? '$daysLogged/$totalDays days'
                      : '$daysLogged/$totalDays ימים',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

import '/backend/api_requests/api_calls.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'camera_view_widget.dart' show CameraViewWidget;
import 'package:flutter/material.dart';

class CameraViewModel extends FlutterFlowModel<CameraViewWidget> {
  ///  Local state fields for this page.

  String? capturedImage;

  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - API (Add Meal)] action in Container widget.
  ApiCallResponse? addMealResponse;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}

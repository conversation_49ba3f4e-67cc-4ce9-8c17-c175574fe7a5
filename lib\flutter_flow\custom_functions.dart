import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';

String? dateTimeToString(DateTime? dateTime) {
  if (dateTime == null) {
    return null;
  }

  return dateTime.toUtc().toIso8601String();
}

DateTime? stringToDateTime(String? dateString) {
  if (dateString != null && dateString.trim().isNotEmpty) {
    return DateTime.parse(dateString);
  }
  return null;
}

UserDataStruct userDataFromJson(dynamic json) {
  return UserDataStruct(
    firebaseId: json['firebase_id'] as String?,
    appleId: json['apple_id'] as String?,
    googleId: json['google_id'] as String?,
    email: json['email'] as String?,
    signupBy: json['signup_by'] as String?,
    dob: json['dob'] as String?,
    username: json['username'] as String?,
    gender: json['gender'] as String?,
    heightValue: json['height_value'] as int?,
    heightUnit: json['height_unit'] as String?,
    weightValue: json['weight_value'] as int?,
    weightUnit: json['weight_unit'] as String?,
    workoutFrequencyId: json['workout_frequency_id'] as int?,
    fitnessGoalId: json['fitness_goal_id'] as int?,
    goalAchievementRate: json['goal_achievement_rate'] as double?,
    challenges: json['challenges'] as int?,
    accomplishmentId: json['accomplishment_id'] as int?,
    deviceId: json['device_id'] as String?,
    fcmToken: json['fcm_token'] as String?,
    targetWeightValue: json['target_weight_value'] as double?,
    targetWeightUnit: json['target_weight_unit'] as String?,
    languageCode: json['language_code'] as String?,
  );
}

dynamic userDatatoAPIJson(UserDataStruct userData) {
  return {
    "firebase_id": userData.firebaseId,
    "apple_id": userData.appleId,
    "google_id": userData.googleId,
    "email": userData.email,
    "username": userData.username,
    "signup_by": userData.signupBy,
    "dob": userData.dob,
    "gender": userData.gender,
    "height_value": userData.heightValue,
    "height_unit": userData.heightUnit,
    "weight_value": userData.weightValue,
    "weight_unit": userData.weightUnit,
    "workout_frequency_id": userData.workoutFrequencyId,
    "fitness_goal_id": userData.fitnessGoalId,
    "goal_achievement_rate": userData.goalAchievementRate,
    "challenges": userData.challenges,
    "accomplishment_id": userData.accomplishmentId,
    "device_id": userData.firebaseId,
    "fcm_token": userData.fcmToken,
    "target_weight_value": userData.targetWeightValue,
    "target_weight_unit": userData.targetWeightUnit,
  };
}

dynamic createuserDatatoAPIJson(UserDataStruct userData) {
  return {
    "apple_id": userData.appleId,
    "google_id": userData.googleId,
    "username": userData.username,
    "email": userData.email,
    "signup_by": userData.signupBy,
    "device_id": userData.firebaseId,
    "fcm_token": userData.fcmToken,
    "language_code": userData.languageCode
  };
}

dynamic onBoardDataToAPIJson(UserDataStruct userData) {
  return {
    "device_id": userData.firebaseId,
    "fcm_token": userData.fcmToken,
    "dob": userData.dob,
    "gender": userData.gender,
    "username": userData.username,
    "height_value": userData.heightValue,
    "height_unit": userData.heightUnit,
    "weight_value": userData.weightValue,
    "weight_unit": userData.weightUnit,
    "workout_frequency_id": userData.workoutFrequencyId,
    "fitness_goal_id": userData.fitnessGoalId,
    "target_weight_value": userData.targetWeightValue,
    "target_weight_unit": userData.targetWeightUnit,
    "goal_achievement_rate": userData.goalAchievementRate,
    "challenges": userData.challenges,
    "accomplishment_id": userData.accomplishmentId,
    "language_code": userData.languageCode
  };
}

dynamic updateGoalAPIJson(UserDataStruct userData) {
  return {
    "fitness_goal_id": userData.fitnessGoalId,
    "target_weight_value": userData.targetWeightValue,
    "target_weight_unit": userData.targetWeightUnit,
    "goal_achievement_rate": userData.goalAchievementRate,
    "challenges": userData.challenges,
    "accomplishment_id": userData.accomplishmentId,
    "language_code": userData.languageCode,
    "workout_frequency_id": userData.workoutFrequencyId,
  };
}

String get12HrTimeFromStringDate(String dateTimeString) {
  // I wanted to get the time in 12:60 PM format for this '2024-12-28 16:15:00'. using flutter
  try {
    final dateTime = DateTime.parse(dateTimeString);
    final formattedTime = DateFormat('h:mm a').format(dateTime.toLocal());
    return formattedTime;
  } catch (_) {
    return '';
  }
}

dynamic getUserUpdateRequestJson(UserDataStruct userData) {
  return {
    "dob": userData.dob,
    "gender": userData.gender,
    "height_value": userData.heightValue,
    "height_unit": userData.heightUnit,
    "weight_value": userData.weightValue,
    "weight_unit": userData.weightUnit
  };
}

String getUsernameFromEmail(String email) {
  if (email.contains('@')) {
    // Extract the part before '@'
    String username = email.split('@').first;

    // Remove special characters using a regular expression
    String sanitizedUsername = username.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');

    return sanitizedUsername;
  } else {
    return "testuser";
  }
}

bool hideSoftKeyboard1() {
  FocusManager.instance.primaryFocus?.unfocus();
  return true;
}

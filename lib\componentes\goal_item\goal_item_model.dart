import '/componentes/center_image/center_image_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'goal_item_widget.dart' show GoalItemWidget;
import 'package:flutter/material.dart';

class GoalItemModel extends FlutterFlowModel<GoalItemWidget> {
  ///  State fields for stateful widgets in this component.

  // Model for center_image component.
  late CenterImageModel centerImageModel1;
  // Model for center_image component.
  late CenterImageModel centerImageModel2;
  // Model for center_image component.
  late CenterImageModel centerImageModel3;
  // Model for center_image component.
  late CenterImageModel centerImageModel4;
  // Model for center_image component.
  late CenterImageModel centerImageModel5;

  @override
  void initState(BuildContext context) {
    centerImageModel1 = createModel(context, () => CenterImageModel());
    centerImageModel2 = createModel(context, () => CenterImageModel());
    centerImageModel3 = createModel(context, () => CenterImageModel());
    centerImageModel4 = createModel(context, () => CenterImageModel());
    centerImageModel5 = createModel(context, () => CenterImageModel());
  }

  @override
  void dispose() {
    centerImageModel1.dispose();
    centerImageModel2.dispose();
    centerImageModel3.dispose();
    centerImageModel4.dispose();
    centerImageModel5.dispose();
  }
}

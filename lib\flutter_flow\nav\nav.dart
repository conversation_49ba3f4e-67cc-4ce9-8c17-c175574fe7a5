import 'dart:async';

// import 'package:cal_counti_a_i/pages/subscription/subscription.dart';
import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/pages/Ingredients/adjust_goal.dart';
import 'package:cal_counti_a_i/pages/Ingredients/dish_nutrition_details.dart';
import 'package:cal_counti_a_i/pages/Ingredients/food_nutrition_details.dart';
import 'package:cal_counti_a_i/backend/schema/structs/food_item_struct.dart';

import 'package:cal_counti_a_i/pages/Ingredients/food_database_log.dart';
import 'package:bugsnag_flutter/bugsnag_flutter.dart';
import 'package:cal_counti_a_i/pages/nutrition_citations/nutrition_citations_screen.dart';
import 'package:cal_counti_a_i/pages/subscription/subscription_old.dart';
import 'package:cal_counti_a_i/pages/suggestions_bugs_screen/bug_report_screen.dart';
import 'package:flutter/material.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:provider/provider.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';

import '/auth/base_auth_user_provider.dart';

import '/backend/push_notifications/push_notifications_handler.dart'
    show PushNotificationsHandler;
import '/index.dart';
import '/main.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';

export 'package:go_router/go_router.dart';
export 'serialization_util.dart';

const kTransitionInfoKey = '__transition_info__';

GlobalKey<NavigatorState> appNavigatorKey = GlobalKey<NavigatorState>();

FoodItem _foodItemFromMealsMap(Map<String, dynamic> meal) {
  return FoodItem(
    id: meal['id'] ?? 0,
    enName: meal['name'] ?? '',
    heName: meal['name'] ?? '',
    data: FoodDetails(
      id: (meal['id'] ?? 0).toString(),
      name: meal['name'] ?? '',
      brand: '',
      fats: double.tryParse(meal['totalFats']?.toString() ??
              meal['total_fats']?.toString() ??
              '0') ??
          0,
      carbs: double.tryParse(meal['totalCarbs']?.toString() ??
              meal['total_carbs']?.toString() ??
              '0') ??
          0,
      protein: double.tryParse(meal['totalProteins']?.toString() ??
              meal['total_proteins']?.toString() ??
              '0') ??
          0,
      calories: double.tryParse(meal['totalCalories']?.toString() ??
              meal['total_calories']?.toString() ??
              '0') ??
          0,
      servings: 1,
      servingTypeId: '',
      servingTypes: const [],
    ),
    created_at: meal['scannedAt'] ?? meal['scanned_at'] ?? '',
    relevance: null,
  );
}

class AppStateNotifier extends ChangeNotifier {
  AppStateNotifier._();

  static AppStateNotifier? _instance;

  static AppStateNotifier get instance => _instance ??= AppStateNotifier._();

  BaseAuthUser? initialUser;
  BaseAuthUser? user;
  bool showSplashImage = true;
  String? _redirectLocation;

  /// Determines whether the app will refresh and build again when a sign
  /// in or sign out happens. This is useful when the app is launched or
  /// on an unexpected logout. However, this must be turned off when we
  /// intend to sign in/out and then navigate or perform any actions after.
  /// Otherwise, this will trigger a refresh and interrupt the action(s).
  bool notifyOnAuthChange = true;

  bool get loading => user == null || showSplashImage;
  bool get loggedIn => user?.loggedIn ?? false;
  bool get initiallyLoggedIn => initialUser?.loggedIn ?? false;
  bool get shouldRedirect => loggedIn && _redirectLocation != null;

  String getRedirectLocation() => _redirectLocation!;
  bool hasRedirect() => _redirectLocation != null;
  void setRedirectLocationIfUnset(String loc) => _redirectLocation ??= loc;
  void clearRedirectLocation() => _redirectLocation = null;

  /// Mark as not needing to notify on a sign in / out when we intend
  /// to perform subsequent actions (such as navigation) afterwards.
  void updateNotifyOnAuthChange(bool notify) => notifyOnAuthChange = notify;

  void update(BaseAuthUser newUser) {
    final shouldUpdate =
        user?.uid == null || newUser.uid == null || user?.uid != newUser.uid;
    initialUser ??= newUser;
    user = newUser;
    // Refresh the app on auth change unless explicitly marked otherwise.
    // No need to update unless the user has changed.
    if (notifyOnAuthChange && shouldUpdate) {
      notifyListeners();
    }
    // Once again mark the notifier as needing to update on auth change
    // (in order to catch sign in / out events).
    updateNotifyOnAuthChange(true);
  }

  void stopShowingSplashImage() {
    showSplashImage = false;
    notifyListeners();
  }
}

GoRouter createRouter(AppStateNotifier appStateNotifier) => GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      navigatorKey: appNavigatorKey,
      observers: [
        PosthogObserver(
          nameExtractor: (route) => route.name,
        ),
        BugsnagNavigatorObserver(),
        BugsnagPerformanceNavigatorObserver(),
      ],
      errorBuilder: (context, state) =>
          appStateNotifier.loggedIn ? NavBarPage() : SplashWidget(),
      routes: [
        FFRoute(
          name: '_initialize',
          path: '/',
          builder: (context, _) =>
              appStateNotifier.loggedIn ? NavBarPage() : SplashWidget(),
        ),
        FFRoute(
          name: 'entry_screen',
          path: '/entryScreen',
          builder: (context, params) => EntryScreenWidget(),
        ),
        FFRoute(
          name: 'camera_view',
          path: '/cameraView',
          builder: (context, params) => CameraViewWidget(),
        ),
        FFRoute(
          name: 'subscription',
          path: '/subscription',
          builder: (context, params) => SubscriptionScreen(),
          // builder: (context, params) => SubscriptionTrialScreen(),
        ),
        FFRoute(
          name: 'suggestions_bugs',
          path: '/suggestions_bugs',
          builder: (context, params) => BugReportScreen(),
        ),
        FFRoute(
          name: 'quiz',
          path: '/quiz',
          builder: (context, params) => QuizWidget(
            isForSignIn: params.getParam(
              'isForSignIn',
              ParamType.bool,
            ),
            isForAdjustGoal: params.getParam(
              'isForAdjustGoal',
              ParamType.bool,
            ),
          ),
        ),
        FFRoute(
          name: 'settings',
          path: '/settings',
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'settings')
              : NavBarPage(
                  initialPage: 'settings',
                  page: SettingsWidget(),
                ),
        ),
        FFRoute(
          name: 'splash',
          path: '/splash',
          builder: (context, params) => SplashWidget(),
        ),
        FFRoute(
          name: 'forgot',
          path: '/forgot',
          builder: (context, params) => ForgotWidget(),
        ),
        FFRoute(
          name: 'teams_privacy',
          path: '/teamsPrivacy',
          builder: (context, params) => TeamsPrivacyWidget(
            privacy: params.getParam(
              'privacy',
              ParamType.bool,
            ),
            url: params.getParam(
              'url',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'dashboard',
          path: '/dashboard',
          requireAuth: true,
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'dashboard')
              : NavBarPage(
                  initialPage: 'dashboard',
                  page: DashboardWidget(),
                ),
        ),
        FFRoute(
          name: 'overview_food',
          path: '/overviewFood',
          builder: (context, params) => params.isEmpty
              ? NavBarPage(initialPage: 'overview_food')
              : NavBarPage(
                  initialPage: 'overview_food',
                  page: OverviewFoodWidget(),
                ),
        ),
        // FFRoute(
        //   name: 'edit_value',
        //   path: '/edit_value',
        //   builder: (context, params) => EditIngredients(),
        // ),
        FFRoute(
          name: 'adjust_goal',
          path: '/adjust_goal',
          builder: (context, params) => AdjustGoal(
            initialCalories: params.getParam('initialCalories', ParamType.int),
            initialProtein: params.getParam('initialProtein', ParamType.int),
            initialCarbs: params.getParam('initialCarbs', ParamType.int),
            initialFats: params.getParam('initialFats', ParamType.int),
          ),
        ),
        FFRoute(
          name: 'food_database_log',
          path: '/food_database_log',
          builder: (context, params) => FoodDatabaseLog(),
        ),
        // FFRoute(
        //   name: 'barcode_scanner',
        //   path: '/barcode_scanner',
        //   builder: (context, params) => BarcodeScannerScreen(),
        // ),
        FFRoute(
          name: 'notifications',
          path: '/notifications',
          builder: (context, params) => NotificationsWidget(),
        ),
        FFRoute(
          name: 'personal_detail',
          path: '/personalDetail',
          builder: (context, params) => PersonalDetailWidget(),
        ),
        FFRoute(
          name: 'meal_details',
          path: '/mealDetails',
          builder: (context, params) => MealDetailsWidget(
            mMealItem: params.getParam(
              'mMealItem',
              ParamType.DataStruct,
              isList: false,
              structBuilder: MealDetailStruct.fromSerializableMap,
            ),
            imageUrl: params.getParam(
              'imageUrl',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'dish_nutrition_details',
          path: '/dish_nutrition_details',
          builder: (context, params) => DishNutritionDetails(
            mMealItem: params.getParam(
              'mMealItem',
              ParamType.DataStruct,
              isList: false,
              structBuilder: MealDetailStruct.fromSerializableMap,
            ),
            imageUrl: params.getParam(
              'imageUrl',
              ParamType.String,
            ),
          ),
        ),
        FFRoute(
          name: 'food_nutrition_details',
          path: '/food_nutrition_details',
          builder: (context, params) {
            final foodItemMap = params.getParam('foodItem', ParamType.JSON)
                as Map<String, dynamic>?;
            final foodItem =
                foodItemMap != null ? FoodItem.fromJson(foodItemMap) : null;
            final mealId = params.getParam('mealId', ParamType.int);
            if (foodItem == null) {
              return Scaffold(
                body: Center(
                  child: Text(FFLocalizations.of(context).languageCode == 'en'
                      ? 'Error: Invalid food item data.'
                      : 'שגיאה: נתוני פריט מזון לא תקינים.'),
                ),
              );
            }
            return FoodNutritionDetails(
              foodItem: foodItem,
              mealId: mealId,
            );
          },
        ),
        FFRoute(
          name: 'personal_detail_view',
          path: '/personalDetailView',
          builder: (context, params) => PersonalDetailViewWidget(),
        ),
        FFRoute(
          name: 'gemini_integrations',
          path: '/geminiIntegrations',
          builder: (context, params) => GeminiIntegrationsWidget(),
        ),
        FFRoute(
          name: 'nutrition_citations',
          path: '/nutrition_citations',
          builder: (context, params) => NutritionCitationsScreen(),
        )
      ].map((r) => r.toRoute(appStateNotifier)).toList(),
    );

extension NavParamExtensions on Map<String, String?> {
  Map<String, String> get withoutNulls => Map.fromEntries(
        entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
}

extension NavigationExtensions on BuildContext {
  void goNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : goNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void pushNamedAuth(
    String name,
    bool mounted, {
    Map<String, String> pathParameters = const <String, String>{},
    Map<String, String> queryParameters = const <String, String>{},
    Object? extra,
    bool ignoreRedirect = false,
  }) =>
      !mounted || GoRouter.of(this).shouldRedirect(ignoreRedirect)
          ? null
          : pushNamed(
              name,
              pathParameters: pathParameters,
              queryParameters: queryParameters,
              extra: extra,
            );

  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/');
    }
  }
}

extension GoRouterExtensions on GoRouter {
  AppStateNotifier get appState => AppStateNotifier.instance;
  void prepareAuthEvent([bool ignoreRedirect = false]) =>
      appState.hasRedirect() && !ignoreRedirect
          ? null
          : appState.updateNotifyOnAuthChange(false);
  bool shouldRedirect(bool ignoreRedirect) =>
      !ignoreRedirect && appState.hasRedirect();
  void clearRedirectLocation() => appState.clearRedirectLocation();
  void setRedirectLocationIfUnset(String location) =>
      appState.updateNotifyOnAuthChange(false);
}

extension _GoRouterStateExtensions on GoRouterState {
  Map<String, dynamic> get extraMap =>
      extra != null ? extra as Map<String, dynamic> : {};
  Map<String, dynamic> get allParams => <String, dynamic>{}
    ..addAll(pathParameters)
    ..addAll(uri.queryParameters)
    ..addAll(extraMap);
  TransitionInfo get transitionInfo => extraMap.containsKey(kTransitionInfoKey)
      ? extraMap[kTransitionInfoKey] as TransitionInfo
      : TransitionInfo.appDefault();
}

class FFParameters {
  FFParameters(this.state, [this.asyncParams = const {}]);

  final GoRouterState state;
  final Map<String, Future<dynamic> Function(String)> asyncParams;

  Map<String, dynamic> futureParamValues = {};

  // Parameters are empty if the params map is empty or if the only parameter
  // present is the special extra parameter reserved for the transition info.
  bool get isEmpty =>
      state.allParams.isEmpty ||
      (state.allParams.length == 1 &&
          state.extraMap.containsKey(kTransitionInfoKey));
  bool isAsyncParam(MapEntry<String, dynamic> param) =>
      asyncParams.containsKey(param.key) && param.value is String;
  bool get hasFutures => state.allParams.entries.any(isAsyncParam);
  Future<bool> completeFutures() => Future.wait(
        state.allParams.entries.where(isAsyncParam).map(
          (param) async {
            final doc = await asyncParams[param.key]!(param.value)
                .onError((_, __) => null);
            if (doc != null) {
              futureParamValues[param.key] = doc;
              return true;
            }
            return false;
          },
        ),
      ).onError((_, __) => [false]).then((v) => v.every((e) => e));

  dynamic getParam<T>(
    String paramName,
    ParamType type, {
    bool isList = false,
    List<String>? collectionNamePath,
    StructBuilder<T>? structBuilder,
  }) {
    if (futureParamValues.containsKey(paramName)) {
      return futureParamValues[paramName];
    }
    if (!state.allParams.containsKey(paramName)) {
      return null;
    }
    final param = state.allParams[paramName];
    // Got parameter from `extras`, so just directly return it.
    if (param is! String) {
      return param;
    }
    // Return serialized value.
    return deserializeParam<T>(
      param,
      type,
      isList,
      collectionNamePath: collectionNamePath,
      structBuilder: structBuilder,
    );
  }
}

class FFRoute {
  const FFRoute({
    required this.name,
    required this.path,
    required this.builder,
    this.requireAuth = false,
    this.asyncParams = const {},
    this.routes = const [],
  });

  final String name;
  final String path;
  final bool requireAuth;
  final Map<String, Future<dynamic> Function(String)> asyncParams;
  final Widget Function(BuildContext, FFParameters) builder;
  final List<GoRoute> routes;

  GoRoute toRoute(AppStateNotifier appStateNotifier) => GoRoute(
        name: name,
        path: path,
        redirect: (context, state) {
          if (appStateNotifier.shouldRedirect) {
            final redirectLocation = appStateNotifier.getRedirectLocation();
            appStateNotifier.clearRedirectLocation();
            return redirectLocation;
          }

          if (requireAuth && !appStateNotifier.loggedIn) {
            appStateNotifier.setRedirectLocationIfUnset(state.uri.toString());
            return '/splash';
          }
          return null;
        },
        pageBuilder: (context, state) {
          fixStatusBarOniOS16AndBelow(context);
          final ffParams = FFParameters(state, asyncParams);
          final page = ffParams.hasFutures
              ? FutureBuilder(
                  future: ffParams.completeFutures(),
                  builder: (context, _) => builder(context, ffParams),
                )
              : builder(context, ffParams);
          final child = appStateNotifier.loading
              ? Container(
                  color: FlutterFlowTheme.of(context).primary,
                  child: Center(
                    child: Image.asset(
                      'assets/images/Fixed_AppIcon.png',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                )
              : PushNotificationsHandler(child: page);

          final transitionInfo = state.transitionInfo;
          return transitionInfo.hasTransition
              ? CustomTransitionPage(
                  key: state.pageKey,
                  child: child,
                  transitionDuration: transitionInfo.duration,
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) =>
                          PageTransition(
                    type: transitionInfo.transitionType,
                    duration: transitionInfo.duration,
                    reverseDuration: transitionInfo.duration,
                    alignment: transitionInfo.alignment,
                    child: child,
                  ).buildTransitions(
                    context,
                    animation,
                    secondaryAnimation,
                    child,
                  ),
                )
              : MaterialPage(key: state.pageKey, child: child);
        },
        routes: routes,
      );
}

class TransitionInfo {
  const TransitionInfo({
    required this.hasTransition,
    this.transitionType = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.alignment,
  });

  final bool hasTransition;
  final PageTransitionType transitionType;
  final Duration duration;
  final Alignment? alignment;

  static TransitionInfo appDefault() => TransitionInfo(hasTransition: false);
}

class RootPageContext {
  const RootPageContext(this.isRootPage, [this.errorRoute]);
  final bool isRootPage;
  final String? errorRoute;

  static bool isInactiveRootPage(BuildContext context) {
    final rootPageContext = context.read<RootPageContext?>();
    final isRootPage = rootPageContext?.isRootPage ?? false;
    final location = GoRouterState.of(context).uri.toString();
    return isRootPage &&
        location != '/' &&
        location != rootPageContext?.errorRoute;
  }

  static Widget wrap(Widget child, {String? errorRoute}) => Provider.value(
        value: RootPageContext(true, errorRoute),
        child: child,
      );
}

extension GoRouterLocationExtension on GoRouter {
  String getCurrentLocation() {
    final RouteMatch lastMatch = routerDelegate.currentConfiguration.last;
    final RouteMatchList matchList = lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : routerDelegate.currentConfiguration;
    return matchList.uri.toString();
  }
}

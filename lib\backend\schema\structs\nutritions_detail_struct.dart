// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class NutritionsDetailStruct extends FFFirebaseStruct {
  NutritionsDetailStruct({
    String? scanDate,
    double? totalCaloriesPerDay,
    double? totalFatsPerDay,
    double? totalCarbsPerDay,
    double? totalProteinsPerDay,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _scanDate = scanDate,
        _totalCaloriesPerDay = totalCaloriesPerDay,
        _totalFatsPerDay = totalFatsPerDay,
        _totalCarbsPerDay = totalCarbsPerDay,
        _totalProteinsPerDay = totalProteinsPerDay,
        super(firestoreUtilData);

  // "scan_date" field.
  String? _scanDate;
  String get scanDate => _scanDate ?? '';
  set scanDate(String? val) => _scanDate = val;

  bool hasScanDate() => _scanDate != null;

  // "total_calories_per_day" field.
  double? _totalCaloriesPerDay;
  double get totalCaloriesPerDay => _totalCaloriesPerDay ?? 0.0;
  set totalCaloriesPerDay(double? val) => _totalCaloriesPerDay = val;

  void incrementTotalCaloriesPerDay(double amount) =>
      totalCaloriesPerDay = totalCaloriesPerDay + amount;

  bool hasTotalCaloriesPerDay() => _totalCaloriesPerDay != null;

  // "total_fats_per_day" field.
  double? _totalFatsPerDay;
  double get totalFatsPerDay => _totalFatsPerDay ?? 0.0;
  set totalFatsPerDay(double? val) => _totalFatsPerDay = val;

  void incrementTotalFatsPerDay(double amount) =>
      totalFatsPerDay = totalFatsPerDay + amount;

  bool hasTotalFatsPerDay() => _totalFatsPerDay != null;

  // "total_carbs_per_day" field.
  double? _totalCarbsPerDay;
  double get totalCarbsPerDay => _totalCarbsPerDay ?? 0.0;
  set totalCarbsPerDay(double? val) => _totalCarbsPerDay = val;

  void incrementTotalCarbsPerDay(double amount) =>
      totalCarbsPerDay = totalCarbsPerDay + amount;

  bool hasTotalCarbsPerDay() => _totalCarbsPerDay != null;

  // "total_proteins_per_day" field.
  double? _totalProteinsPerDay;
  double get totalProteinsPerDay => _totalProteinsPerDay ?? 0.0;
  set totalProteinsPerDay(double? val) => _totalProteinsPerDay = val;

  void incrementTotalProteinsPerDay(double amount) =>
      totalProteinsPerDay = totalProteinsPerDay + amount;

  bool hasTotalProteinsPerDay() => _totalProteinsPerDay != null;

  static NutritionsDetailStruct fromMap(Map<String, dynamic> data) =>
      NutritionsDetailStruct(
        scanDate: castToType<String?>(data['scan_date']),
        totalCaloriesPerDay: castToType<double>(data['total_calories_per_day']),
        totalFatsPerDay: castToType<double>(data['total_fats_per_day']),
        totalCarbsPerDay: castToType<double>(data['total_carbs_per_day']),
        totalProteinsPerDay: castToType<double>(data['total_proteins_per_day']),
      );

  static NutritionsDetailStruct? maybeFromMap(dynamic data) => data is Map
      ? NutritionsDetailStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'scan_date': _scanDate,
        'total_calories_per_day': _totalCaloriesPerDay,
        'total_fats_per_day': _totalFatsPerDay,
        'total_carbs_per_day': _totalCarbsPerDay,
        'total_proteins_per_day': _totalProteinsPerDay,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'scan_date': serializeParam(
          _scanDate,
          ParamType.String,
        ),
        'total_calories_per_day': serializeParam(
          _totalCaloriesPerDay,
          ParamType.double,
        ),
        'total_fats_per_day': serializeParam(
          _totalFatsPerDay,
          ParamType.double,
        ),
        'total_carbs_per_day': serializeParam(
          _totalCarbsPerDay,
          ParamType.double,
        ),
        'total_proteins_per_day': serializeParam(
          _totalProteinsPerDay,
          ParamType.double,
        ),
      }.withoutNulls;

  static NutritionsDetailStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      NutritionsDetailStruct(
        scanDate: deserializeParam(
          data['scan_date'],
          ParamType.String,
          false,
        ),
        totalCaloriesPerDay: deserializeParam(
          data['total_calories_per_day'],
          ParamType.double,
          false,
        ),
        totalFatsPerDay: deserializeParam(
          data['total_fats_per_day'],
          ParamType.double,
          false,
        ),
        totalCarbsPerDay: deserializeParam(
          data['total_carbs_per_day'],
          ParamType.double,
          false,
        ),
        totalProteinsPerDay: deserializeParam(
          data['total_proteins_per_day'],
          ParamType.double,
          false,
        ),
      );

  @override
  String toString() => 'NutritionsDetailStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is NutritionsDetailStruct &&
        scanDate == other.scanDate &&
        totalCaloriesPerDay == other.totalCaloriesPerDay &&
        totalFatsPerDay == other.totalFatsPerDay &&
        totalCarbsPerDay == other.totalCarbsPerDay &&
        totalProteinsPerDay == other.totalProteinsPerDay;
  }

  @override
  int get hashCode => const ListEquality().hash([
        scanDate,
        totalCaloriesPerDay,
        totalFatsPerDay,
        totalCarbsPerDay,
        totalProteinsPerDay
      ]);
}

NutritionsDetailStruct createNutritionsDetailStruct({
  String? scanDate,
  double? totalCaloriesPerDay,
  double? totalFatsPerDay,
  double? totalCarbsPerDay,
  double? totalProteinsPerDay,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    NutritionsDetailStruct(
      scanDate: scanDate,
      totalCaloriesPerDay: totalCaloriesPerDay,
      totalFatsPerDay: totalFatsPerDay,
      totalCarbsPerDay: totalCarbsPerDay,
      totalProteinsPerDay: totalProteinsPerDay,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

NutritionsDetailStruct? updateNutritionsDetailStruct(
  NutritionsDetailStruct? nutritionsDetail, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    nutritionsDetail
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addNutritionsDetailStructData(
  Map<String, dynamic> firestoreData,
  NutritionsDetailStruct? nutritionsDetail,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (nutritionsDetail == null) {
    return;
  }
  if (nutritionsDetail.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && nutritionsDetail.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final nutritionsDetailData =
      getNutritionsDetailFirestoreData(nutritionsDetail, forFieldValue);
  final nestedData =
      nutritionsDetailData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = nutritionsDetail.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getNutritionsDetailFirestoreData(
  NutritionsDetailStruct? nutritionsDetail, [
  bool forFieldValue = false,
]) {
  if (nutritionsDetail == null) {
    return {};
  }
  final firestoreData = mapToFirestore(nutritionsDetail.toMap());

  // Add any Firestore field values
  nutritionsDetail.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getNutritionsDetailListFirestoreData(
  List<NutritionsDetailStruct>? nutritionsDetails,
) =>
    nutritionsDetails
        ?.map((e) => getNutritionsDetailFirestoreData(e, true))
        .toList() ??
    [];

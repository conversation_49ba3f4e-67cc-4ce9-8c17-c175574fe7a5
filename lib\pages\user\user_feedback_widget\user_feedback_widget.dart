import 'package:flutter/material.dart';
import '../../../error_service.dart';

class UserFeedbackWidget extends StatefulWidget {
  @override
  _UserFeedbackWidgetState createState() => _UserFeedbackWidgetState();
}

class _UserFeedbackWidgetState extends State<UserFeedbackWidget> {
  final TextEditingController _feedbackController = TextEditingController();
  int _rating = 5;
  String _category = 'general';

  void _submitFeedback() {
    if (_feedbackController.text.isNotEmpty) {
      ErrorService.trackUserFeedback(
        message: _feedbackController.text,
        category: _category,
        rating: _rating,
        additionalData: {
          'screen': 'feedback_widget',
          'app_version': '1.0.0',
        },
      );

      _feedbackController.clear();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Thank you for your feedback!')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('How are we doing?',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 16),

            // Rating
            Row(
              children: [
                Text('Rating: '),
                ...List.generate(
                  5,
                  (index) => GestureDetector(
                    onTap: () => setState(() => _rating = index + 1),
                    child: Icon(
                      Icons.star,
                      color: index < _rating ? Colors.amber : Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),

            // Category
            DropdownButton<String>(
              value: _category,
              onChanged: (value) => setState(() => _category = value!),
              items: [
                DropdownMenuItem(value: 'general', child: Text('General')),
                DropdownMenuItem(
                    value: 'bug_report', child: Text('Bug Report')),
                DropdownMenuItem(
                    value: 'feature_request', child: Text('Feature Request')),
                DropdownMenuItem(
                    value: 'suggestion', child: Text('Suggestion')),
              ],
            ),
            SizedBox(height: 16),

            // Message
            TextField(
              controller: _feedbackController,
              decoration: InputDecoration(
                hintText: 'Tell us what you think...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 16),

            ElevatedButton(
              onPressed: _submitFeedback,
              child: Text('Send Feedback'),
            ),
          ],
        ),
      ),
    );
  }
}

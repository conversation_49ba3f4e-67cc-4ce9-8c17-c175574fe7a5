/*
// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/backend/schema/enums/enums.dart';
import '/actions/actions.dart' as action_blocks;
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom widgets
import '/custom_code/actions/index.dart'; // Imports custom actions
import '/flutter_flow/custom_functions.dart'; // Imports custom functions
import 'package:flutter/material.dart';
// Begin custom widget code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import '/flutter_flow/upload_data.dart';

import '/flutter_flow/flutter_flow_icon_button.dart';

import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import '../../auth/firebase_auth/auth_util.dart';

// Below code is custom code

class CameraCustomWidget extends StatefulWidget {
  const CameraCustomWidget({
    super.key,
    this.width,
    this.height,
  });

  final double? width;
  final double? height;

  @override
  State<CameraCustomWidget> createState() => _CameraCustomWidgetState();
}

class _CameraCustomWidgetState extends State<CameraCustomWidget> {
  bool isCameraReady = false;
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  List<CameraDescription>? cameras;
  XFile? _cameraFile;
  XFile? _filePickerFile;
  bool backCamera = true;
  double _zoomLevel = 1.0;
  double? _maxAvailableZoom;
  double _scale = 1.0;
  double _baseScale = 1.0;

  @override
  void initState() {
    _initCamera();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _initCamera() async {
    cameras = await availableCameras();
    _controller = CameraController(
      backCamera ? cameras![0] : cameras![1], // Use the first camera available
      // cameras![0], // Use the first camera available
      ResolutionPreset.ultraHigh,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,

      // Adjust the resolution here if needed
    );

    _initializeControllerFuture = _controller.initialize();
    isCameraReady = true;
    setState(() {});
  }

  void _updateZoom(double delta) async {
    _maxAvailableZoom ??= await _controller.getMaxZoomLevel();

    final newZoomLevel = _zoomLevel + delta;
    if (newZoomLevel >= 1.0 && newZoomLevel <= _maxAvailableZoom!) {
      _controller.setZoomLevel(newZoomLevel);
      setState(() {
        _zoomLevel = newZoomLevel;
      });
    }
  }

  void _onScaleStart(ScaleStartDetails details) {
    _baseScale = _scale;
  }

  void _onScaleUpdate(ScaleUpdateDetails details) {
    _scale = _baseScale * details.scale;
    _scale = _scale.clamp(1.0, _maxAvailableZoom ?? 0); // Clamp zoom level
    _controller.setZoomLevel(_scale);
  }

  void _onScaleEnd(ScaleEndDetails details) {
    // You can add any necessary cleanup here
  }

  @override
  Widget build(BuildContext context) {
    if (isCameraReady) {
      return FutureBuilder<void>(
        future: _initializeControllerFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (_maxAvailableZoom == null) {
              _controller
                  .getMaxZoomLevel()
                  .then((value) => _maxAvailableZoom = value);
            }

            return Stack(
              children: [
                if (_filePickerFile == null && _cameraFile == null)
                  GestureDetector(
                    onScaleStart: _onScaleStart,
                    onScaleUpdate: _onScaleUpdate,
                    onScaleEnd: _onScaleEnd,
                    child: cameraWidget(context),
                  ),
                if (_cameraFile != null) Image.file(File(_cameraFile!.path)),
                if (_filePickerFile != null)
                  Image.file(File(_filePickerFile!.path)),
                ..._getBottomButtons(),
              ],
            );
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        },
      );
    } else {
      return const Center(child: CircularProgressIndicator());
    }
  }

  Future<void> _changeCamera() async {
    backCamera = !backCamera;
    await _controller.dispose();
    _initCamera();
  }

  List<Widget> _getBottomButtons() {
    if (_cameraFile == null && _filePickerFile == null) {
      return [
        Align(
          alignment: AlignmentDirectional(0.0, 1.0),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FlutterFlowTheme.of(context).overlay0,
                  FlutterFlowTheme.of(context).primaryBackground
                ],
                stops: [0.0, 0.7],
                begin: AlignmentDirectional(0.0, -1.0),
                end: AlignmentDirectional(0, 1.0),
              ),
            ),
            child: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(24.0, 64.0, 24.0, 56.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12.0),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: 5.0,
                        sigmaY: 5.0,
                      ),
                      child: Container(
                        constraints: const BoxConstraints.tightFor(
                          width: 50,
                          height: 50,
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).info30,
                          borderRadius: BorderRadius.circular(12.0),
                          border: Border.all(
                            color: FlutterFlowTheme.of(context).info30,
                            width: 1.0,
                          ),
                        ),
                        child: FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 30.0,
                          borderWidth: 1.0,
                          buttonSize: 50.0,
                          icon: Icon(
                            FFIcons.kimage03,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 32.0,
                          ),
                          showLoadingIndicator: true,
                          onPressed: _filePicker,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ClipOval(
                          child: BackdropFilter(
                            filter: ImageFilter.blur(
                              sigmaX: 5.0,
                              sigmaY: 5.0,
                            ),
                            child: Container(
                              width: 72.0,
                              height: 72.0,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).info30,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 12.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 5.0),
                                  )
                                ],
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: FlutterFlowTheme.of(context).info30,
                                  width: 2.0,
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(4.0),
                                child: FlutterFlowIconButton(
                                  borderColor: Colors.transparent,
                                  borderRadius: 30.0,
                                  borderWidth: 1.0,
                                  buttonSize: 60.0,
                                  fillColor: FlutterFlowTheme.of(context).info,
                                  icon: Icon(
                                    FFIcons.kcameraLens,
                                    color: FlutterFlowTheme.of(context).info,
                                    size: 36.0,
                                  ),
                                  showLoadingIndicator: true,
                                  onPressed: _takePicture,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12.0),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: 5.0,
                        sigmaY: 5.0,
                      ),
                      child: Container(
                        constraints: const BoxConstraints.tightFor(
                          width: 50,
                          height: 50,
                        ),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).info30,
                          borderRadius: BorderRadius.circular(12.0),
                          border: Border.all(
                            color: FlutterFlowTheme.of(context).info30,
                            width: 1.0,
                          ),
                        ),
                        child: FlutterFlowIconButton(
                          borderColor: Colors.transparent,
                          borderRadius: 30.0,
                          borderWidth: 1.0,
                          buttonSize: 50.0,
                          icon: Icon(
                            FFIcons.krefreshCw03,
                            color: FlutterFlowTheme.of(context).primaryText,
                            size: 32.0,
                          ),
                          showLoadingIndicator: true,
                          onPressed: _changeCamera,
                        ),
                      ),
                    ),
                  ),
                ].divide(SizedBox(width: 16.0)),
              ),
            ),
          ),
        ),
      ];
    }
    return [
      Align(
        alignment: AlignmentDirectional(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                FlutterFlowTheme.of(context).overlay0,
                FlutterFlowTheme.of(context).primaryBackground
              ],
              stops: [0.0, 0.7],
              begin: AlignmentDirectional(0.0, -1.0),
              end: AlignmentDirectional(0, 1.0),
            ),
          ),
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(24.0, 64.0, 24.0, 56.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.0),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 5.0,
                      sigmaY: 5.0,
                    ),
                    child: Container(
                      constraints: const BoxConstraints.tightFor(
                        width: 50,
                        height: 50,
                      ),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).info30,
                        borderRadius: BorderRadius.circular(12.0),
                        border: Border.all(
                          color: FlutterFlowTheme.of(context).info30,
                          width: 1.0,
                        ),
                      ),
                      child: FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 30.0,
                        borderWidth: 1.0,
                        buttonSize: 50.0,
                        icon: Icon(
                          FFIcons.kxClose,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 32.0,
                        ),
                        showLoadingIndicator: true,
                        onPressed: () {
                          setState(() {
                            _cameraFile = null;
                            _filePickerFile = null;
                          });
                        },
                      ),
                    ),
                  ),
                ),
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.0),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 5.0,
                      sigmaY: 5.0,
                    ),
                    child: Container(
                      constraints: const BoxConstraints.tightFor(
                        width: 50,
                        height: 50,
                      ),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: FlutterFlowTheme.of(context).info30,
                        borderRadius: BorderRadius.circular(12.0),
                        border: Border.all(
                          color: FlutterFlowTheme.of(context).info30,
                          width: 1.0,
                        ),
                      ),
                      child: FlutterFlowIconButton(
                        borderColor: Colors.transparent,
                        borderRadius: 30.0,
                        borderWidth: 1.0,
                        buttonSize: 50.0,
                        icon: Icon(
                          FFIcons.kcheck,
                          color: FlutterFlowTheme.of(context).primaryText,
                          size: 32.0,
                        ),
                        showLoadingIndicator: true,
                        onPressed: () async {
                          if (_cameraFile != null) {
                            final selectedFile =
                                await _createSelectedFile(_cameraFile!);
                            Navigator.of(context).pop(selectedFile);
                          } else if (_filePickerFile != null) {
                            final selectedFile =
                                await _createSelectedFile(_filePickerFile!);
                            Navigator.of(context).pop(selectedFile);
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ].divide(SizedBox(width: 16.0)),
            ),
          ),
        ),
      ),
    ];
  }

  Widget cameraWidget(context) {
    var camera = _controller.value;
    // fetch screen size
    final size = MediaQuery.of(context).size;

    // calculate scale depending on screen and camera ratios
    // this is actually size.aspectRatio / (1 / camera.aspectRatio)
    // because camera preview size is received as landscape
    // but we're calculating for portrait orientation
    var scale = size.aspectRatio * camera.aspectRatio;

    // to prevent scaling down, invert the value
    if (scale < 1) scale = 1 / scale;

    return SizedBox(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      child: AspectRatio(
        aspectRatio: 1 / _controller.value.aspectRatio,
        child: _controller.buildPreview(),
      ),
    );
  }

  Future<void> _takePicture() async {
    try {
      await _initializeControllerFuture;
      final XFile imageFile = await _controller.takePicture();
      setState(() {
        _cameraFile = imageFile;
      });
    } catch (e) {
      print('Error: $e');
    }
  }

  Future<void> _filePicker() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _filePickerFile = pickedFile;
      });
    }
  }

  Future<SelectedFile> _createSelectedFile(XFile xFile) async {
    final path = _getStoragePath(xFile.name);
    final mediaBytes = await xFile.readAsBytes();
    final dimensions = await _getImageDimensions(mediaBytes);
    final selectedFile = SelectedFile(
      storagePath: path,
      filePath: xFile.path,
      bytes: mediaBytes,
      dimensions: dimensions,
    );
    return selectedFile;
  }

  String _getStoragePath(String filePath) {
    final pathPrefix = 'users/$currentUserUid/uploads';
    final timestamp = DateTime.now().microsecondsSinceEpoch;
    final ext = filePath.split('.').last;
    return '$pathPrefix/$timestamp.$ext';
  }

  Future<MediaDimensions> _getImageDimensions(Uint8List mediaBytes) async {
    final image = await decodeImageFromList(mediaBytes);
    return MediaDimensions(
      width: image.width.toDouble(),
      height: image.height.toDouble(),
    );
  }
}
*/

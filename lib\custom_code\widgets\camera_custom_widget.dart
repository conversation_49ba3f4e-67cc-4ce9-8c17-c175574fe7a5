import 'package:cal_counti_a_i/auth/firebase_auth/auth_util.dart';
import 'package:cal_counti_a_i/backend/api_requests/api_calls.dart';
import 'package:cal_counti_a_i/backend/schema/users_record.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import '../../flutter_flow/flutter_flow_icon_button.dart';

class CameraCustomWidget extends StatefulWidget {
  const CameraCustomWidget({
    super.key,
    this.width,
    this.height,
    this.onPickedImage,
    this.takePicture,
    this.onScannerModeChanged,
  });

  final double? width;
  final double? height;
  final Future Function(String? imagePath)? onPickedImage;
  final Future Function()? takePicture;
  final Function(ScannerMode mode)? onScannerModeChanged;

  @override
  State<CameraCustomWidget> createState() => _CameraCustomWidgetState();
}

class _CameraCustomWidgetState extends State<CameraCustomWidget> {
  bool isCameraReady = false;
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  List<CameraDescription>? cameras;
  XFile? _cameraFile;
  SelectedFile? selectedFile;
  XFile? _filePickerFile;
  bool backCamera = true;
  bool imageUploading = false;
  ScannerMode selectedType = ScannerMode.food;
  bool isProcessing = false;
  final StreamController<UploadProgress> progressController =
  StreamController<UploadProgress>();
  bool isEnglish = false;

  @override
  void initState() {
    super.initState();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    _initCamera();
  }

  @override
  void dispose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _controller.dispose();
    progressController.close();
    super.dispose();
  }

  Future<void> _initCamera() async {
    cameras = await availableCameras();
    _controller = CameraController(
      backCamera ? cameras![0] : cameras![1],
      ResolutionPreset.ultraHigh,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    _initializeControllerFuture = _controller.initialize();
    isCameraReady = true;
    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  Widget build(BuildContext context) {
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    if (isCameraReady) {
      return FutureBuilder<void>(
        future: _initializeControllerFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return Container(
              child: Stack(
                children: [
                  // Show camera preview if no image is selected
                  if (_filePickerFile == null && _cameraFile == null)
                    cameraWidget(context),
                  // Show captured image if _cameraFile is not null
                  if (_cameraFile != null)
                    SizedBox.expand(
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Image.file(
                          File(_cameraFile!.path),
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  // Show picked image if _filePickerFile is not null
                  if (_filePickerFile != null)
                    SizedBox.expand(
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Image.file(
                          File(_filePickerFile!.path),
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  // Show loading indicator if processing
                  if (isProcessing)
                    Center(
                      child: CircularProgressWidget(),
                    ),
                  // Bottom buttons
                  ..._getBottomButtons(context),
                ],
              ),
            );
          } else {
            return const Center(
                child: CircularProgressIndicator(color: Colors.grey));
          }
        },
      );
    } else {
      return const Center(child: SizedBox(
          width: 100,
          height: 100,
          child: CircularProgressWidget()));
    }
  }

  List<Widget> _getBottomButtons(BuildContext context) {
    if (_cameraFile == null && _filePickerFile == null) {
      return [
        Align(
          alignment: AlignmentDirectional(0.0, 1.0),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    foodScanWidget(isEnglish),
                    labelScanWidget(isEnglish),
                    galleryScanWidget(isEnglish),
                  ],
                ),
                SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: ClipOval(
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: 5.0,
                        sigmaY: 5.0,
                      ),
                      child: Container(
                        width: 60.0,
                        height: 60.0,
                        decoration: BoxDecoration(
                          color: info30,
                          boxShadow: [
                            BoxShadow(
                              blurRadius: 10.0,
                              color: Color(0x33000000),
                              offset: Offset(0.0, 4.0),
                            )
                          ],
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: info30,
                            width: 1.5,
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(3.0),
                          child: FlutterFlowIconButton(
                            borderColor: Colors.transparent,
                            borderRadius: 25.0,
                            borderWidth: 1.0,
                            buttonSize: 50.0,
                            fillColor: FlutterFlowTheme.of(context).info,
                            icon: Icon(
                              Icons.camera_alt,
                              color: FlutterFlowTheme.of(context).info,
                              size: 30.0,
                            ),
                            showLoadingIndicator: true,
                            onPressed: _handleCapture,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    }
    return [
      Align(
        alignment: AlignmentDirectional(0.0, 1.0),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          child: Padding(
            padding: EdgeInsets.only(bottom: 30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                buildScannerControls(context),
              ],
            ),
          ),
        ),
      ),
    ];
  }

  Row buildScannerControls(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 50,
          width: 50,
          decoration: BoxDecoration(
            color: info30,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: info30, width: 1),
          ),
          child: FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 30,
            buttonSize: 50,
            fillColor: Colors.redAccent,
            icon: Icon(Icons.close, color: Colors.white, size: 30),
            showLoadingIndicator: true,
            onPressed: () {
              if (imageUploading) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(FFLocalizations.of(context).languageCode == 'en'
                        ? 'Image upload in progress. Please wait.'
                        : 'העלאת התמונה בעיצומה. אנא המתן.'),
                  ),
                );
                return;
              }
              setState(() {
                _cameraFile = null;
                _filePickerFile = null;
                selectedType = ScannerMode.food;
                widget.onScannerModeChanged?.call(selectedType);
              });
            },
          ),
        ),
        SizedBox(width: 16),
        Container(
          height: 80,
          width: 80,
          decoration: BoxDecoration(
            color: info30,
            borderRadius: BorderRadius.circular(100),
            border: Border.all(color: info30, width: 1),
          ),
          child: FlutterFlowIconButton(
            borderColor: Colors.transparent,
            borderRadius: 100,
            buttonSize: 70,
            fillColor: Colors.greenAccent,
            icon: Icon(
              Icons.check,
              color: Colors.white,
              size: 50,
            ),
            showLoadingIndicator: true,
            onPressed: () async {
              if (imageUploading) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(FFLocalizations.of(context).languageCode == 'en'
                        ? 'Upload in progress. Please wait.'
                        : 'העלאה בתהליך. אנא המתן.'),
                  ),
                );
                return;
              }

              final uploadType = (selectedType == ScannerMode.foodLabel ? ScannerMode.foodLabel : ScannerMode.food);
              final file = _cameraFile ?? _filePickerFile;

              if (file == null) return;

              setState(() {
                isProcessing = true;
              });

              try {
                selectedFile ??= await _createSelectedFile(file);
                _uploadWithRetry(
                  imagePath: selectedFile!.filePath,
                  token: FFAppState().authToken,
                  type: uploadType,
                );
                setState(() {
                  imageUploading = false;
                  isProcessing = false;
                  selectedType = ScannerMode.food;
                  widget.onScannerModeChanged?.call(selectedType);
                });
                await Future.delayed(Duration(milliseconds: 100));
                Navigator.of(context).pop(selectedFile);
              } catch (e) {
                print('Upload error: $e');
                setState(() {
                  isProcessing = false;
                });
                if (e.toString().contains('TimeoutException')) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(isEnglish
                          ? 'Upload timed out. Check your internet and try again.'
                          : 'העלאה נכשלה. בדוק את חיבור האינטרנט ונסה שוב.'),
                      action: SnackBarAction(
                        label: isEnglish ? 'Retry' : 'נסה שוב',
                        onPressed: () async {
                          setState(() {
                            isProcessing = true;
                          });
                          try {
                            _uploadWithRetry(
                              imagePath: selectedFile!.filePath,
                              token: FFAppState().authToken,
                              type: uploadType,
                            );
                            setState(() {
                              imageUploading = false;
                              isProcessing = false;
                              selectedType = ScannerMode.food;
                              widget.onScannerModeChanged?.call(selectedType);
                            });
                            await Future.delayed(Duration(milliseconds: 100));
                            Navigator.of(context).pop(selectedFile);
                          } catch (retryError) {
                            print('Retry error: $retryError');
                            setState(() {
                              isProcessing = false;
                            });
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(isEnglish
                                    ? 'Retry failed: $retryError. Please try again.'
                                    : 'הניסיון מחדש נכשל: $retryError. אנא נסה שוב.'),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(isEnglish
                          ? 'Error uploading: $e'
                          : 'שגיאה בהעלאה: $e'),
                    ),
                  );
                }
              }
            },
          ),
        ),
      ],
    );
  }

  GestureDetector galleryScanWidget(bool isEnglish) {
    return GestureDetector(
      onTap: () async {
        if (selectedType != ScannerMode.gallery) {
          // await _controller.stopImageStream();
          setState(() {
            selectedType = ScannerMode.gallery;
            isProcessing = false;
            widget.onScannerModeChanged?.call(selectedType);
          });
        }
        await _filePicker();
      },
      child: Column(
        children: [
          Container(
            constraints: const BoxConstraints.tightFor(
              width: 65,
              height: 45,
            ),
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(horizontal: 4, vertical: 5),
            decoration: BoxDecoration(
              color: selectedType == ScannerMode.gallery
                  ? FlutterFlowTheme.of(context).primaryBackground
                  : info30,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: selectedType == ScannerMode.gallery
                    ? FlutterFlowTheme.of(context).primary
                    : info30,
                width: 1.5,
              ),
            ),
            child: Icon(
              Icons.image_rounded,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 20,
            ),
          ),
          SizedBox(height: 4),
          Text(
            isEnglish ? 'Gallery' : 'גלריה',
            style: FlutterFlowTheme.of(context).bodySmall.copyWith(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  GestureDetector labelScanWidget(bool isEnglish) {
    return GestureDetector(
      onTap: () async {
        if (selectedType != ScannerMode.foodLabel) {
          // await _controller.stopImageStream();
          setState(() {
            selectedType = ScannerMode.foodLabel;
            isProcessing = false;
            widget.onScannerModeChanged?.call(selectedType);
          });
        }
      },
      child: Column(
        children: [
          Container(
            constraints: const BoxConstraints.tightFor(
              width: 65,
              height: 45,
            ),
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(horizontal: 4, vertical: 5),
            decoration: BoxDecoration(
              color: selectedType == ScannerMode.foodLabel
                  ? FlutterFlowTheme.of(context).primaryBackground
                  : info30,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: selectedType == ScannerMode.foodLabel
                    ? FlutterFlowTheme.of(context).primary
                    : info30,
                width: 1.5,
              ),
            ),
            child: Icon(
              Icons.label,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 20,
            ),
          ),
          SizedBox(height: 4),
          Text(
            isEnglish ? 'Food Label' : 'תווית מזון',
            style: FlutterFlowTheme.of(context).bodySmall.copyWith(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  GestureDetector foodScanWidget(bool isEnglish) {
    return GestureDetector(
      onTap: () async {
        if (selectedType != ScannerMode.food) {
          // await _controller.stopImageStream();
          setState(() {
            selectedType = ScannerMode.food;
            isProcessing = false;
            widget.onScannerModeChanged?.call(selectedType);
          });
        }
      },
      child: Column(
        children: [
          Container(
            constraints: const BoxConstraints.tightFor(
              width: 65,
              height: 45,
            ),
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(horizontal: 4, vertical: 5),
            decoration: BoxDecoration(
              color: selectedType == ScannerMode.food
                  ? FlutterFlowTheme.of(context).primaryBackground
                  : info30,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: selectedType == ScannerMode.food
                    ? FlutterFlowTheme.of(context).primary
                    : info30,
                width: 1.5,
              ),
            ),
            child: Icon(
              Icons.restaurant,
              color: FlutterFlowTheme.of(context).primaryText,
              size: 20,
            ),
          ),
          SizedBox(height: 4),
          Text(
            isEnglish ? 'Scan Food' : 'סרוק מזון',
            style: FlutterFlowTheme.of(context).bodySmall.copyWith(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleCapture() async {
    if (selectedType == ScannerMode.gallery) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'Please select an image from the gallery.'
              : 'אנא בחר תמונה מהגלריה.'),
        ),
      );
      return;
    }

    bool hasCameraAccess = await _checkCameraPermission();
    if (!hasCameraAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'Camera permission denied. Please enable camera access in settings.'
              : 'הרשאת מצלמה נדחתה. אנא אפשר גישה למצלמה בהגדרות.'),
          action: SnackBarAction(
            label: isEnglish ? 'Settings' : 'הגדרות',
            onPressed: () {
              openAppSettings();
            },
          ),
        ),
      );
      return;
    }

    try {
      await _initializeControllerFuture;
      // await _controller.stopImageStream();
      final XFile imageFile = await _controller.takePicture();
      _cameraFile = imageFile;
      selectedFile = await _createSelectedFile(_cameraFile!);
      if (selectedFile != null) {
        widget.onPickedImage?.call(selectedFile!.filePath);
        print('Image captured: ${selectedFile!.filePath}');
        print('Image dimensions: ${selectedFile!.dimensions}');
      }
      isProcessing = false;
      WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
    } catch (e) {
      isProcessing = false;
      print('Error capturing image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'Error capturing image: $e'
              : 'שגיאה בלכידת התמונה: $e'),
        ),
      );
    }
  }

  Future<bool> _checkCameraPermission() async {
    PermissionStatus status = await Permission.camera.request();
    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.restricted:
        return false;
      case PermissionStatus.limited:
        return true;
      case PermissionStatus.permanentlyDenied:
        return false;
      case PermissionStatus.provisional:
        return true;
    }
  }

  Widget cameraWidget(BuildContext context) {
    if (!_controller.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    final size = MediaQuery.of(context).size;
    final deviceRatio = size.width / size.height;
    final previewRatio = _controller.value.aspectRatio;

    return Center(
      child: OverflowBox(
        maxHeight: deviceRatio > previewRatio
            ? size.width / previewRatio
            : size.height,
        maxWidth: deviceRatio > previewRatio
            ? size.width
            : size.height * previewRatio,
        child: CameraPreview(_controller),
      ),
    );
  }

  Future<void> _filePicker() async {
    bool hasFileAccess = await _checkStoragePermission();
    if (!hasFileAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'No file access permission. Please enable file access permission in settings.'
              : 'אין הרשאה לגישה לקבצים. אנא אפשר הרשאה לגישה לקבצים בהגדרות.'),
          action: SnackBarAction(
            label: isEnglish ? 'Settings' : 'הגדרות',
            onPressed: () {
              openAppSettings();
            },
          ),
        ),
      );
      return;
    }
    final picker = ImagePicker();
    final pickedFile =
    await picker.pickImage(source: ImageSource.gallery, imageQuality: 90);
    if (pickedFile != null) {
      _filePickerFile = pickedFile;
      selectedFile = await _createSelectedFile(_filePickerFile!);
      if (selectedFile != null) {
        widget.onPickedImage?.call(selectedFile!.filePath);
      }
      WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
    } else {
      setState(() {
        selectedType = ScannerMode.food;
        widget.onScannerModeChanged?.call(selectedType);
      });
    }
  }

  void setUploadProgress(int sentBytes, int totalBytes) {
    progressController
        .add(UploadProgress(sentBytes: sentBytes, totalBytes: totalBytes));
  }

  Future<bool> _checkNetworkConnectivity() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return false;
    }
    return true;
  }

  Future<bool> uploadMealImage({
    required String imagePath,
    required String token,
    required ScannerMode type,
    String? barcodeNumber,
  }) async {
    final url = Uri.parse('$baseUrl/meal/add');
    final request = http.MultipartRequest('POST', url);
    request.headers.addAll({
      'Accept': 'application/json',
      'Authorization': 'Bearer $token',
      'Connection': 'keep-alive',
    });

    print('Uploading to $url');
    print('Headers: ${request.headers}');

    request.fields['type'] = type.name;
    if (barcodeNumber != null) {
      request.fields['barcode'] = barcodeNumber;
      print('Fields: ${request.fields}');
    } else {
      print('Fields: ${request.fields}');
    }

    imageUploading = true;
    final file = await http.MultipartFile.fromPath(
      'image',
      imagePath,
    );
    request.files.add(file);
    print('Files included: image ($imagePath)');

    try {
      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 60),
        onTimeout: () {
          throw TimeoutException('Upload timed out');
        },
      );
      final response = await http.Response.fromStream(streamedResponse);
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        await updateTotalScannedCount();
        print('Upload successful: ${response.body}');
        imageUploading = false;
        return true;
      } else {
        imageUploading = false;
        try {
          final responseJson = jsonDecode(response.body);
          final errorMessage = responseJson['message'] ?? 'Unknown error';
          if (errorMessage == 'Invalid barcode') {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(isEnglish
                    ? 'The barcode is not recognized by the server. Please try a different barcode.'
                    : 'הגרקוד אינו מזוהה על ידי השר.'),
              ),
            );
          }
          throw HttpException(
              'Upload failed: ${response.statusCode} - $errorMessage');
        } catch (e) {
          throw HttpException(
              'Upload failed: ${response.statusCode} - ${response.body}');
        }
      }
    } catch (e) {
      imageUploading = false;
      throw Exception('Failed to upload: $e');
    }
  }

  Future<bool> _uploadWithRetry({
    required String imagePath,
    required String token,
    required ScannerMode type,
    String? barcodeNumber,
  }) async {
    const int maxRetries = 3;
    int attempt = 0;

    bool isConnected = await _checkNetworkConnectivity();
    if (!isConnected) {
      throw Exception(isEnglish
          ? 'No internet connection. Please check your network and try again.'
          : 'אין חיבור לאינטרנט. אנא בדוק את הרשת שלך ונסה שוב.');
    }

    while (attempt < maxRetries) {
      attempt++;
      print('Upload attempt $attempt of $maxRetries');
      try {
        return await uploadMealImage(
          imagePath: imagePath,
          token: token,
          type: type,
          barcodeNumber: barcodeNumber,
        );
      } catch (e) {
        if (e.toString().contains('TimeoutException') && attempt < maxRetries) {
          print('Timeout on attempt $attempt. Retrying...');
          await Future.delayed(Duration(seconds: 3));
          continue;
        } else if (e.toString().contains('Invalid barcode') &&
            attempt < maxRetries) {
          print('Invalid barcode on attempt $attempt. Retrying...');
          await Future.delayed(Duration(seconds: 1));
          continue;
        }
        throw e;
      }
    }

    throw Exception('Failed to upload after $maxRetries retries');
  }

  Future<void> updateTotalScannedCount() async {
    if (currentUserReference == null) {
      return;
    }
    var userData = await UsersRecord.getDocumentOnce(currentUserReference!);
    int totalScannedCount = userData.totalScannedCount;
    await currentUserReference!.update(createUsersRecordData(
      totalScannedCount: (totalScannedCount + 1),
    ));
  }

  Future<bool> _checkStoragePermission() async {
    PermissionStatus status;
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final AndroidDeviceInfo info = await deviceInfoPlugin.androidInfo;
      if ((info.version.sdkInt) >= 33) {
        status = PermissionStatus.granted;
      } else {
        status = await Permission.storage.request();
      }
    } else {
      status = await Permission.storage.request();
    }

    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.restricted:
        return false;
      case PermissionStatus.limited:
        return true;
      case PermissionStatus.permanentlyDenied:
        return false;
      case PermissionStatus.provisional:
        return true;
    }
  }

  Future<Uint8List> _compressImage(XFile xFile) async {
    final Uint8List mediaBytes = await xFile.readAsBytes();
    final result = await FlutterImageCompress.compressWithList(
      mediaBytes,
      minWidth: 800,
      minHeight: 800,
      quality: 85,
    );
    return result;
  }

  Future<SelectedFile> _createSelectedFile(XFile xFile) async {
    await requestStoragePermissions();
    final String path = await _getStoragePath(xFile.name);
    final String ext = path.split('.').last;

    final Uint8List mediaBytes = await xFile.readAsBytes();
    final Uint8List compressedBytes = await _compressImage(xFile);

    print(
        'mediaBytes length: ${mediaBytes.length} compressedBytes length: ${compressedBytes.length}');
    final File compressedFile = File(path);
    try {
      await compressedFile.writeAsBytes(compressedBytes);
    } catch (e) {
      print('Error writing file: $e');
      throw (e);
    }

    final MediaDimensions dimensions =
    await _getImageDimensions(compressedBytes);
    return SelectedFile(
      storagePath: path,
      filePath: compressedFile.path,
      bytes: compressedBytes,
      dimensions: dimensions.toJson(),
      ext: ext,
    );
  }

  Future<void> requestStoragePermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
    ].request();

    final String info = statuses[Permission.storage].toString();
    print(info);
  }

  Future<String> _getStoragePath(String filePath) async {
    Directory appCacheDir = await getApplicationCacheDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final ext = filePath.split('.').last;
    final String filePathWithTimestamp = '${appCacheDir.path}/$timestamp.$ext';
    return filePathWithTimestamp;
  }

  Future<MediaDimensions> _getImageDimensions(Uint8List mediaBytes) async {
    final image = await decodeImageFromList(mediaBytes);
    return MediaDimensions(
      width: image.width.toDouble(),
      height: image.height.toDouble(),
    );
  }

  Color get info30 => Colors.white.withOpacity(0.6);
}

class UploadProgress {
  final int sentBytes;
  final int totalBytes;

  UploadProgress({required this.sentBytes, required this.totalBytes});
}

class SelectedFile {
  final String storagePath;
  final String filePath;
  final Uint8List bytes;
  final Map<String, dynamic>? dimensions;
  final String? ext;

  SelectedFile({
    required this.storagePath,
    required this.filePath,
    required this.bytes,
    this.dimensions,
    this.ext,
  });

  Map<String, dynamic> toJson() => {
    'storage_path': storagePath,
    'file_path': filePath,
    'dimensions': dimensions,
    'ext': ext,
  };
}

class MediaDimensions {
  final double width;
  final double height;

  MediaDimensions({
    required this.width,
    required this.height,
  });

  Map<String, dynamic> toJson() => {
    'width': width,
    'height': height,
  };

  factory MediaDimensions.fromJson(Map<String, dynamic> json) {
    return MediaDimensions(
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
    );
  }

  @override
  String toString() => 'MediaDimensions(width: $width, height: $height)';
}

enum ScannerMode {
  food,
  foodLabel,
  gallery;

  String get name {
    switch (this) {
      case ScannerMode.food:
        return 'dish';
      case ScannerMode.foodLabel:
        return 'label';
      case ScannerMode.gallery:
        return 'dish';
    }
  }
}

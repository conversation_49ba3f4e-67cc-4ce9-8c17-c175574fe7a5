// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class MealDetailStruct extends FFFirebaseStruct {
  MealDetailStruct({
    int? id,
    String? name,
    String? image,
    String? totalCalories,
    String? totalFats,
    String? totalProteins,
    String? totalCarbs,
    String? scannedAt,
    String? type,
    List<MealItemsStruct>? items,
    int? healthScore,
    int? quantity,
    int? inProgress,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _id = id,
        _name = name,
        _image = image,
        _totalCalories = totalCalories,
        _totalFats = totalFats,
        _totalProteins = totalProteins,
        _totalCarbs = totalCarbs,
        _scannedAt = scannedAt,
        _type = type,
        _items = items,
        _healthScore = healthScore,
        _quantity = quantity,
        _inProgress = inProgress,
        super(firestoreUtilData);

  // "id" field.
  int? _id;
  int get id => _id ?? 0;
  set id(int? val) => _id = val;

  void incrementId(int amount) => id = id + amount;

  bool hasId() => _id != null;

  // "name" field.
  String? _name;
  String get name => _name ?? '';
  set name(String? val) => _name = val;

  bool hasName() => _name != null;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;

  bool hasImage() => _image != null;

  // "total_calories" field.
  String? _totalCalories;
  String get totalCalories => _totalCalories ?? '';
  set totalCalories(String? val) => _totalCalories = val;

  bool hasTotalCalories() => _totalCalories != null;

  // "total_fats" field.
  String? _totalFats;
  String get totalFats => _totalFats ?? '';
  set totalFats(String? val) => _totalFats = val;

  bool hasTotalFats() => _totalFats != null;

  // "total_proteins" field.
  String? _totalProteins;
  String get totalProteins => _totalProteins ?? '';
  set totalProteins(String? val) => _totalProteins = val;

  bool hasTotalProteins() => _totalProteins != null;

  // "total_carbs" field.
  String? _totalCarbs;
  String get totalCarbs => _totalCarbs ?? '';
  set totalCarbs(String? val) => _totalCarbs = val;

  bool hasTotalCarbs() => _totalCarbs != null;

  // "scanned_at" field.
  String? _scannedAt;
  String get scannedAt => _scannedAt ?? '';
  set scannedAt(String? val) => _scannedAt = val;

  bool hasScannedAt() => _scannedAt != null;

  // "type" field.
  String? _type;
  String get type => _type ?? '';
  set type(String? val) => _type = val;

  bool hasType() => _type != null;

  // "items" field.
  List<MealItemsStruct>? _items;
  List<MealItemsStruct> get items => _items ?? const [];
  set items(List<MealItemsStruct>? val) => _items = val;

  void updateItems(Function(List<MealItemsStruct>) updateFn) {
    updateFn(_items ??= []);
  }

  bool hasItems() => _items != null;

  // "health_score" field.
  int? _healthScore;
  int get healthScore => _healthScore ?? 0;
  set healthScore(int? val) => _healthScore = val;

  void incrementHealthScore(int amount) => healthScore = healthScore + amount;

  bool hasHealthScore() => _healthScore != null;

  // "quantity" field.
  int? _quantity;
  int get quantity => _quantity ?? 0;
  set quantity(int? val) => _quantity = val;

  void incrementQuantity(int amount) => quantity = quantity + amount;

  bool hasQuantity() => _quantity != null;

  // "in_progress" field.
  int? _inProgress;
  int get inProgress => _inProgress ?? 0;
  set inProgress(int? val) => _inProgress = val;

  void incrementInProgress(int amount) => inProgress = inProgress + amount;

  bool hasInProgress() => _inProgress != null;

  static MealDetailStruct fromMap(Map<String, dynamic> data) =>
      MealDetailStruct(
        id: castToType<int>(data['id']),
        name: castToType<String?>(data['name']),
        image: castToType<String?>(data['image']),
        totalCalories: castToType<String?>(data['total_calories']),
        totalFats: castToType<String?>(data['total_fats']),
        totalProteins: castToType<String?>(data['total_proteins']),
        totalCarbs: castToType<String?>(data['total_carbs']),
        scannedAt: castToType<String?>(data['scanned_at']),
        type: castToType<String?>(data['type']),
        items: getStructList(
          data['items'],
          MealItemsStruct.fromMap,
        ),
        healthScore: castToType<int>(data['health_score']),
        quantity: castToType<int>(data['quantity']),
        inProgress: castToType<int>(data['in_progress']),
      );

  static MealDetailStruct? maybeFromMap(dynamic data) => data is Map
      ? MealDetailStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'id': _id,
        'name': _name,
        'image': _image,
        'total_calories': _totalCalories,
        'total_fats': _totalFats,
        'total_proteins': _totalProteins,
        'total_carbs': _totalCarbs,
        'scanned_at': _scannedAt,
        'type': _type,
        'items': _items?.map((e) => e.toMap()).toList(),
        'health_score': _healthScore,
        'quantity': _quantity,
        'in_progress': _inProgress,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'id': serializeParam(
          _id,
          ParamType.int,
        ),
        'name': serializeParam(
          _name,
          ParamType.String,
        ),
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'total_calories': serializeParam(
          _totalCalories,
          ParamType.String,
        ),
        'total_fats': serializeParam(
          _totalFats,
          ParamType.String,
        ),
        'total_proteins': serializeParam(
          _totalProteins,
          ParamType.String,
        ),
        'total_carbs': serializeParam(
          _totalCarbs,
          ParamType.String,
        ),
        'scanned_at': serializeParam(
          _scannedAt,
          ParamType.String,
        ),
        'type': serializeParam(
          _type,
          ParamType.String,
        ),
        'items': serializeParam(
          _items,
          ParamType.DataStruct,
          isList: true,
        ),
        'health_score': serializeParam(
          _healthScore,
          ParamType.int,
        ),
        'quantity': serializeParam(
          _quantity,
          ParamType.int,
        ),
        'in_progress': serializeParam(
          _inProgress,
          ParamType.int,
        ),
      }.withoutNulls;

  static MealDetailStruct fromSerializableMap(Map<String, dynamic> data) =>
      MealDetailStruct(
        id: deserializeParam(
          data['id'],
          ParamType.int,
          false,
        ),
        name: deserializeParam(
          data['name'],
          ParamType.String,
          false,
        ),
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        totalCalories: deserializeParam(
          data['total_calories'],
          ParamType.String,
          false,
        ),
        totalFats: deserializeParam(
          data['total_fats'],
          ParamType.String,
          false,
        ),
        totalProteins: deserializeParam(
          data['total_proteins'],
          ParamType.String,
          false,
        ),
        totalCarbs: deserializeParam(
          data['total_carbs'],
          ParamType.String,
          false,
        ),
        scannedAt: deserializeParam(
          data['scanned_at'],
          ParamType.String,
          false,
        ),
        type: deserializeParam(
          data['type'],
          ParamType.String,
          false,
        ),
        items: deserializeStructParam<MealItemsStruct>(
          data['items'],
          ParamType.DataStruct,
          true,
          structBuilder: MealItemsStruct.fromSerializableMap,
        ),
        healthScore: deserializeParam(
          data['health_score'],
          ParamType.int,
          false,
        ),
        quantity: deserializeParam(
          data['quantity'],
          ParamType.int,
          false,
        ),
        inProgress: deserializeParam(
          data['in_progress'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'MealDetailStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is MealDetailStruct &&
        id == other.id &&
        name == other.name &&
        image == other.image &&
        totalCalories == other.totalCalories &&
        totalFats == other.totalFats &&
        totalProteins == other.totalProteins &&
        totalCarbs == other.totalCarbs &&
        scannedAt == other.scannedAt &&
        type == other.type &&
        listEquality.equals(items, other.items) &&
        healthScore == other.healthScore &&
        quantity == other.quantity &&
        inProgress == other.inProgress;
  }

  @override
  int get hashCode => const ListEquality().hash([
        id,
        name,
        image,
        totalCalories,
        totalFats,
        totalProteins,
        totalCarbs,
        scannedAt,
        type,
        items,
        healthScore,
        quantity,
        inProgress,
      ]);

  MealDetailStruct copyWith({
    int? id,
    String? name,
    String? image,
    String? totalCalories,
    String? totalFats,
    String? totalProteins,
    String? totalCarbs,
    String? scannedAt,
    String? type,
    List<MealItemsStruct>? items,
    int? healthScore,
    int? quantity,
    int? inProgress,
    FirestoreUtilData? firestoreUtilData,
  }) {
    return MealDetailStruct(
      id: id ?? _id,
      name: name ?? _name,
      image: image ?? _image,
      totalCalories: totalCalories ?? _totalCalories,
      totalFats: totalFats ?? _totalFats,
      totalProteins: totalProteins ?? _totalProteins,
      totalCarbs: totalCarbs ?? _totalCarbs,
      scannedAt: scannedAt ?? _scannedAt,
      type: type ?? _type,
      items: items ?? _items,
      healthScore: healthScore ?? _healthScore,
      quantity: quantity ?? _quantity,
      inProgress: inProgress ?? _inProgress,
      firestoreUtilData: firestoreUtilData ?? this.firestoreUtilData,
    );
  }
}

MealDetailStruct createMealDetailStruct({
  int? id,
  String? name,
  String? image,
  String? totalCalories,
  String? totalFats,
  String? totalProteins,
  String? totalCarbs,
  String? scannedAt,
  String? type,
  List<MealItemsStruct>? items,
  int? healthScore,
  int? quantity,
  int? inProgress,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    MealDetailStruct(
      id: id,
      name: name,
      image: image,
      totalCalories: totalCalories,
      totalFats: totalFats,
      totalProteins: totalProteins,
      totalCarbs: totalCarbs,
      scannedAt: scannedAt,
      type: type,
      items: items,
      healthScore: healthScore,
      quantity: quantity,
      inProgress: inProgress,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

MealDetailStruct? updateMealDetailStruct(
  MealDetailStruct? mealDetail, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    mealDetail
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addMealDetailStructData(
  Map<String, dynamic> firestoreData,
  MealDetailStruct? mealDetail,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (mealDetail == null) {
    return;
  }
  if (mealDetail.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && mealDetail.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final mealDetailData = getMealDetailFirestoreData(mealDetail, forFieldValue);
  final nestedData = mealDetailData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = mealDetail.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getMealDetailFirestoreData(
  MealDetailStruct? mealDetail, [
  bool forFieldValue = false,
]) {
  if (mealDetail == null) {
    return {};
  }
  final firestoreData = mapToFirestore(mealDetail.toMap());

  // Add any Firestore field values
  mealDetail.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getMealDetailListFirestoreData(
  List<MealDetailStruct>? mealDetails,
) =>
    mealDetails?.map((e) => getMealDetailFirestoreData(e, true)).toList() ?? [];

// ignore_for_file: unnecessary_getters_setters

import 'package:cal_counti_a_i/backend/schema/structs/daily_log.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class FoodOverviewDetailStruct extends FFFirebaseStruct {
  FoodOverviewDetailStruct({
    List<GoalsStruct>? goals,
    List<DailyLogStruct>? dailyLogs,
    NutritionsDataStruct? nutritions,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _goals = goals,
        _dailyLogs = dailyLogs,
        _nutritions = nutritions,
        super(firestoreUtilData);

  // "goals" field.
  List<GoalsStruct>? _goals;
  List<GoalsStruct> get goals => _goals ?? const [];
  set goals(List<GoalsStruct>? val) => _goals = val;

  void updateGoals(Function(List<GoalsStruct>) updateFn) {
    updateFn(_goals ??= []);
  }

  bool hasGoals() => _goals != null;

  // "dailyLogs" field.
  List<DailyLogStruct>? _dailyLogs;
  List<DailyLogStruct> get dailyLogs => _dailyLogs ?? const [];
  set dailyLogs(List<DailyLogStruct>? val) => _dailyLogs = val;

  void updateDailyLogs(Function(List<DailyLogStruct>) updateFn) {
    updateFn(_dailyLogs ??= []);
  }

  bool hasDailyLogs() => _dailyLogs != null;

  // "nutritions" field.
  NutritionsDataStruct? _nutritions;
  NutritionsDataStruct get nutritions => _nutritions ?? NutritionsDataStruct();
  set nutritions(NutritionsDataStruct? val) => _nutritions = val;

  void updateNutritions(Function(NutritionsDataStruct) updateFn) {
    updateFn(_nutritions ??= NutritionsDataStruct());
  }

  bool hasNutritions() => _nutritions != null;

  static FoodOverviewDetailStruct fromMap(Map<String, dynamic> data) =>
      FoodOverviewDetailStruct(
        goals: getStructList(
          data['goals'],
          GoalsStruct.fromMap,
        ),
        dailyLogs: getStructList(
          data['dailyLogs'],
          DailyLogStruct.fromMap,
        ),
        nutritions: data['nutritions'] is NutritionsDataStruct
            ? data['nutritions']
            : NutritionsDataStruct.maybeFromMap(data['nutritions']),
      );

  static FoodOverviewDetailStruct? maybeFromMap(dynamic data) => data is Map
      ? FoodOverviewDetailStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'goals': _goals?.map((e) => e.toMap()).toList(),
        'dailyLogs': _dailyLogs?.map((e) => e.toMap()).toList(),
        'nutritions': _nutritions?.toMap(),
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'goals': serializeParam(
          _goals,
          ParamType.DataStruct,
          isList: true,
        ),
        'dailyLogs': serializeParam(
          _dailyLogs,
          ParamType.DataStruct,
          isList: true,
        ),
        'nutritions': serializeParam(
          _nutritions,
          ParamType.DataStruct,
        ),
      }.withoutNulls;

  static FoodOverviewDetailStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      FoodOverviewDetailStruct(
        goals: deserializeStructParam<GoalsStruct>(
          data['goals'],
          ParamType.DataStruct,
          true,
          structBuilder: GoalsStruct.fromSerializableMap,
        ),
        dailyLogs: deserializeStructParam<DailyLogStruct>(
          data['dailyLogs'],
          ParamType.DataStruct,
          true,
          structBuilder: DailyLogStruct.fromSerializableMap,
        ),
        nutritions: deserializeStructParam(
          data['nutritions'],
          ParamType.DataStruct,
          false,
          structBuilder: NutritionsDataStruct.fromSerializableMap,
        ),
      );

  @override
  String toString() => 'FoodOverviewDetailStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is FoodOverviewDetailStruct &&
        listEquality.equals(goals, other.goals) &&
        listEquality.equals(dailyLogs, other.dailyLogs) &&
        nutritions == other.nutritions;
  }

  @override
  int get hashCode => const ListEquality().hash([goals, dailyLogs, nutritions]);
}

FoodOverviewDetailStruct createFoodOverviewDetailStruct({
  NutritionsDataStruct? nutritions,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    FoodOverviewDetailStruct(
      nutritions:
          nutritions ?? (clearUnsetFields ? NutritionsDataStruct() : null),
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

FoodOverviewDetailStruct? updateFoodOverviewDetailStruct(
  FoodOverviewDetailStruct? foodOverviewDetail, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    foodOverviewDetail
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addFoodOverviewDetailStructData(
  Map<String, dynamic> firestoreData,
  FoodOverviewDetailStruct? foodOverviewDetail,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (foodOverviewDetail == null) {
    return;
  }
  if (foodOverviewDetail.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && foodOverviewDetail.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final foodOverviewDetailData =
      getFoodOverviewDetailFirestoreData(foodOverviewDetail, forFieldValue);
  final nestedData =
      foodOverviewDetailData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields =
      foodOverviewDetail.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getFoodOverviewDetailFirestoreData(
  FoodOverviewDetailStruct? foodOverviewDetail, [
  bool forFieldValue = false,
]) {
  if (foodOverviewDetail == null) {
    return {};
  }
  final firestoreData = mapToFirestore(foodOverviewDetail.toMap());

  // Handle nested data for "nutritions" field.
  addNutritionsDataStructData(
    firestoreData,
    foodOverviewDetail.hasNutritions() ? foodOverviewDetail.nutritions : null,
    'nutritions',
    forFieldValue,
  );

  // Add any Firestore field values
  foodOverviewDetail.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getFoodOverviewDetailListFirestoreData(
  List<FoodOverviewDetailStruct>? foodOverviewDetails,
) =>
    foodOverviewDetails
        ?.map((e) => getFoodOverviewDetailFirestoreData(e, true))
        .toList() ??
    [];

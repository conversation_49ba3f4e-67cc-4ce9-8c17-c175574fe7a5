import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/componentes/goal_item/goal_item_widget.dart';
import '/componentes/progressive_appbar/progressive_appbar_widget.dart';
import '/componentes/review_card/review_card_widget.dart';
import '/componentes/segmented/segmented_widget.dart';
import '/componentes/selectable_item/selectable_item_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import 'dart:async';
import 'quiz_widget.dart' show QuizWidget;
import 'package:flutter/material.dart';

class QuizModel extends FlutterFlowModel<QuizWidget> {
  ///  Local state fields for this page.

  String? deviceId;

  String? fcmToken;

  bool forSignUp = true;

  DateTime? dobDateTime;

  ///  State fields for stateful widgets in this page.

  final formKey2 = GlobalKey<FormState>();
  final formKey1 = GlobalKey<FormState>();
  // Stores action output result for [Custom Action - getDeviceId] action in quiz widget.
  String? deviceIdOutput;
  // Stores action output result for [Custom Action - getDeviceId] action in quiz widget.
  String? deviceIdOutput1;
  // Model for progressive_appbar component.
  late ProgressiveAppbarModel progressiveAppbarModel;
  // State field(s) for PageView widget.
  PageController? pageViewController;

  int get pageViewCurrentIndex => pageViewController != null &&
          pageViewController!.hasClients &&
          pageViewController!.page != null
      ? pageViewController!.page!.round()
      : 0;
  // State field(s) for gender widget.
  FormFieldController<String>? genderValueController;

  // Model for workout_item1.
  late SelectableItemModel workoutItem1Model;
  // Model for workout_item2.
  late SelectableItemModel workoutItem2Model1;
  // Model for workout_item3.
  late SelectableItemModel workoutItem3Model1;
  // Model for fitness_item1.
  late SelectableItemModel fitnessItem1Model;
  // Model for workout_item2.
  late SelectableItemModel workoutItem2Model2;
  // Model for workout_item3.
  late SelectableItemModel workoutItem3Model2;
  // State field(s) for weightinput widget.
  // FocusNode? weightinputFocusNode;
  // TextEditingController? weightinputTextController;
  String? Function(BuildContext, String?)? weightinputTextControllerValidator;
  // Model for segmented component.
  late SegmentedModel segmentedModel1;
  // Model for goal_item1.
  late GoalItemModel goalItem1Model;
  // Model for goal_item2.
  late GoalItemModel goalItem2Model;
  // Model for goal_item3.
  late GoalItemModel goalItem3Model;
  // Model for goal_item4.
  late GoalItemModel goalItem4Model;
  // State field(s) for Slider widget.
  double? sliderValue;
  // Model for challenges_item1.
  late SelectableItemModel challengesItem1Model;
  // Model for challenges_item2.
  late SelectableItemModel challengesItem2Model;
  // Model for challenges_item3.
  late SelectableItemModel challengesItem3Model;
  // Model for challenges_item4.
  late SelectableItemModel challengesItem4Model;
  // Model for challenges_item5.
  late SelectableItemModel challengesItem5Model;
  // Model for accomplishment_item1.
  late SelectableItemModel accomplishmentItem1Model;
  // Model for accomplishment_item2.
  late SelectableItemModel accomplishmentItem2Model;
  // Model for accomplishment_item3.
  late SelectableItemModel accomplishmentItem3Model;
  // Model for accomplishment_item4.
  late SelectableItemModel accomplishmentItem4Model;
  // Model for review_card component.
  late ReviewCardModel reviewCardModel1;
  // Model for review_card component.
  late ReviewCardModel reviewCardModel2;
  // Model for review_card component.
  late ReviewCardModel reviewCardModel3;
  // Model for review_card component.
  late ReviewCardModel reviewCardModel4;
  // Model for segmented component.
  late SegmentedModel segmentedModel2;
  // State field(s) for emailAddress widget.
  FocusNode? emailAddressFocusNode;
  TextEditingController? emailAddressTextController;
  String? Function(BuildContext, String?)? emailAddressTextControllerValidator;
  // State field(s) for password widget.
  FocusNode? passwordFocusNode;
  TextEditingController? passwordTextController;
  late bool passwordVisibility;
  String? Function(BuildContext, String?)? passwordTextControllerValidator;
  // State field(s) for confirmPassword widget.
  FocusNode? confirmPasswordFocusNode;
  TextEditingController? confirmPasswordTextController;
  late bool confirmPasswordVisibility;
  String? Function(BuildContext, String?)?
      confirmPasswordTextControllerValidator;
  // Stores action output result for [Custom Action - getFCMToken] action in Button widget.
  String? tokenSU;
  // Stores action output result for [Backend Call - API (SignUp)] action in Button widget.
  ApiCallResponse? newUserEmailSignUp;
  // Stores action output result for [Backend Call - API (Onboarding)] action in Button widget.
  ApiCallResponse? newUserEmailSignUpOnboarding;
  // Stores action output result for [Custom Action - getFCMToken] action in Button widget.
  String? token;
  // Stores action output result for [Backend Call - API (SignUp)] action in Button widget.
  ApiCallResponse? apiResultUserEmailLogin;
  // Stores action output result for [Backend Call - Read Document] action in Button widget.
  UsersRecord? loginUserD;
  // Stores action output result for [Backend Call - Read Document] action in Button widget.
  UsersRecord? userDataGoogle;
  // Stores action output result for [Custom Action - getFCMToken] action in Button widget.
  String? tokenGoogle;
  // Stores action output result for [Backend Call - API (SignUp)] action in Button widget.
  ApiCallResponse? apiResultGoogleUser;
  // Stores action output result for [Backend Call - API (Onboarding)] action in Button widget.
  ApiCallResponse? apiResGoogleOnboarding;
  // Stores action output result for [Backend Call - Read Document] action in Button widget.
  UsersRecord? userDataApple;
  // Stores action output result for [Custom Action - getFCMToken] action in Button widget.
  String? tokenApple;
  // Stores action output result for [Backend Call - API (SignUp)] action in Button widget.
  ApiCallResponse? apiResultNewUserApple;
  // Stores action output result for [Backend Call - API (Onboarding)] action in Button widget.
  ApiCallResponse? apiResOnboardingApple;
  // Stores action output result for [Backend Call - API (Update Goal)] action in Button widget.
  ApiCallResponse? apiResultUpdateGoal;

  @override
  void initState(BuildContext context) {
    progressiveAppbarModel =
        createModel(context, () => ProgressiveAppbarModel());
    workoutItem1Model = createModel(context, () => SelectableItemModel());
    workoutItem2Model1 = createModel(context, () => SelectableItemModel());
    workoutItem3Model1 = createModel(context, () => SelectableItemModel());
    fitnessItem1Model = createModel(context, () => SelectableItemModel());
    workoutItem2Model2 = createModel(context, () => SelectableItemModel());
    workoutItem3Model2 = createModel(context, () => SelectableItemModel());
    segmentedModel1 = createModel(context, () => SegmentedModel());
    goalItem1Model = createModel(context, () => GoalItemModel());
    goalItem2Model = createModel(context, () => GoalItemModel());
    goalItem3Model = createModel(context, () => GoalItemModel());
    goalItem4Model = createModel(context, () => GoalItemModel());
    challengesItem1Model = createModel(context, () => SelectableItemModel());
    challengesItem2Model = createModel(context, () => SelectableItemModel());
    challengesItem3Model = createModel(context, () => SelectableItemModel());
    challengesItem4Model = createModel(context, () => SelectableItemModel());
    challengesItem5Model = createModel(context, () => SelectableItemModel());
    accomplishmentItem1Model =
        createModel(context, () => SelectableItemModel());
    accomplishmentItem2Model =
        createModel(context, () => SelectableItemModel());
    accomplishmentItem3Model =
        createModel(context, () => SelectableItemModel());
    accomplishmentItem4Model =
        createModel(context, () => SelectableItemModel());
    reviewCardModel1 = createModel(context, () => ReviewCardModel());
    reviewCardModel2 = createModel(context, () => ReviewCardModel());
    reviewCardModel3 = createModel(context, () => ReviewCardModel());
    reviewCardModel4 = createModel(context, () => ReviewCardModel());
    segmentedModel2 = createModel(context, () => SegmentedModel());
    passwordVisibility = false;
    confirmPasswordVisibility = false;
  }

  @override
  void dispose() {
    progressiveAppbarModel.dispose();
    workoutItem1Model.dispose();
    workoutItem2Model1.dispose();
    workoutItem3Model1.dispose();
    fitnessItem1Model.dispose();
    workoutItem2Model2.dispose();
    workoutItem3Model2.dispose();
    // weightinputFocusNode?.dispose();
    // weightinputTextController?.dispose();

    segmentedModel1.dispose();
    goalItem1Model.dispose();
    goalItem2Model.dispose();
    goalItem3Model.dispose();
    goalItem4Model.dispose();
    challengesItem1Model.dispose();
    challengesItem2Model.dispose();
    challengesItem3Model.dispose();
    challengesItem4Model.dispose();
    challengesItem5Model.dispose();
    accomplishmentItem1Model.dispose();
    accomplishmentItem2Model.dispose();
    accomplishmentItem3Model.dispose();
    accomplishmentItem4Model.dispose();
    reviewCardModel1.dispose();
    reviewCardModel2.dispose();
    reviewCardModel3.dispose();
    reviewCardModel4.dispose();
    segmentedModel2.dispose();
    emailAddressFocusNode?.dispose();
    emailAddressTextController?.dispose();

    passwordFocusNode?.dispose();
    passwordTextController?.dispose();

    confirmPasswordFocusNode?.dispose();
    confirmPasswordTextController?.dispose();
  }

  /// Action blocks.
  Future setExistingData(BuildContext context) async {}

  /// Additional helper methods.
  String? get genderValue => genderValueController?.value;
}

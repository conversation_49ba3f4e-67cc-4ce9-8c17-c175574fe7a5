import 'dart:math';

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';

import '/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';
import 'package:percent_indicator/percent_indicator.dart';
export 'circular_progress_model.dart';
import 'dart:async';

class CircularProgressWidget extends StatefulWidget {
  const CircularProgressWidget({super.key});

  @override
  State<CircularProgressWidget> createState() => _CircularProgressWidgetState();
}

class _CircularProgressWidgetState extends State<CircularProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  late AnimationController _iconScaleController;
  late Animation<double> _iconScaleAnimation;

  int _currentIndex = 0;

  final List<IconData> icons = [
    Icons.fastfood,
    Icons.local_dining,
    Icons.qr_code_scanner,
    Icons.restaurant_menu,
    Icons.camera_alt,
  ];

  final List<String> emojis = [
    '🥫','🥩','🥪','🥦','🌭','🌮','🌯','🥧','🍓','🍜','🍔',
    '🍝','🍕','🍞','🍖','🍟','🍱','🍦','🍲','🍨','🍩','🍿',
    '🍳','🥗','🥑','🥘','🥙','🥚','🥕','🥔','🥝','🥞','🫘',
  ];

  late final List<dynamic> combinedItems;

  Timer? _iconCycleTimer;

  late AnimationController _bgColorController;
  late Animation<Color?> _bgColorAnimation;
  late AnimationController _rotationController;


  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat();

    _bgColorController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    )..repeat(reverse: true);

    _bgColorAnimation = ColorTween(
      begin: FlutterFlowTheme.of(context).accent4,
      end: FlutterFlowTheme.of(context).primary.withOpacity(0.2),
    ).animate(_bgColorController);


    // Combine both lists — emojis as String, icons as IconData
    combinedItems = [/*...icons,*/ ...emojis];

    _progressController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _progressAnimation =
    Tween<double>(begin: 0.0, end: 1.0).animate(_progressController)
      ..addListener(() {
        setState(() {});
      })
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _progressController.reverse();
        } else if (status == AnimationStatus.dismissed) {
          _progressController.forward();
        }
      });

    _progressController.forward();

    _iconScaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
      reverseDuration: Duration(milliseconds: 500),
    );

    _iconScaleAnimation =
        Tween<double>(begin: 0.8, end: 1.2).animate(CurvedAnimation(
            parent: _iconScaleController,
            curve: Curves.easeInOut,
            reverseCurve: Curves.easeOut
        ));

    _iconScaleController.repeat(reverse: true);

    _iconCycleTimer = Timer.periodic(const Duration(milliseconds: 400), (timer) {
      setState(() {
        _currentIndex = (Random().nextInt(combinedItems.length)) % combinedItems.length;
      });
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _iconScaleController.dispose();
    _iconCycleTimer?.cancel();
    _rotationController.dispose();
    _bgColorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentItem = combinedItems[_currentIndex];

    Widget displayedWidget;

    if (currentItem is IconData) {
      displayedWidget = Icon(
        currentItem,
        key: ValueKey<int>(_currentIndex),
        size: 20,
        color: FlutterFlowTheme.of(context).primary,
      );
    } else if (currentItem is String) {
      displayedWidget = Text(
        currentItem,
        key: ValueKey<int>(_currentIndex),
        style: TextStyle(
          fontSize: 25,
          shadows: [
            Shadow(
              blurRadius: 4.0,
              color: Colors.black26,
              offset: Offset(1.5, 1.5),
            ),
          ],
        ),
      );
    } else {
      // fallback, should not happen
      displayedWidget = SizedBox.shrink();
    }

    return BugsnagLoadingIndicator(
      child: Container(
        width: 90,
        height: 90,
        alignment: Alignment.center,
        child: Stack(
          alignment: Alignment.center,
          children: [
            RotationTransition(
              turns: _rotationController,
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return SweepGradient(
                    startAngle: 0.0,
                    endAngle: 3.14 * 2,
                    colors: [
                      Colors.pink,
                      Colors.orange,
                      Colors.yellow,
                      Colors.green,
                      Colors.cyan,
                      Colors.blue,
                      Colors.purple,
                      Colors.pink,
                    ],
                  ).createShader(bounds);
                },
                child: CircularPercentIndicator(
                  radius: 40,
                  lineWidth: 5,
                  percent: _progressAnimation.value,
                  progressColor: Colors.white, // placeholder for gradient mask
                  backgroundColor: Colors.black12,
                  circularStrokeCap: CircularStrokeCap.round,
                ),
              ),
            ),
            ScaleTransition(
              scale: _iconScaleAnimation,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (child, animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: GestureDetector(
                        onTap: () {
                          if (_iconCycleTimer?.isActive ?? false) {
                            _iconCycleTimer?.cancel();
                          } else {
                            _iconCycleTimer = Timer.periodic(const Duration(milliseconds: 400), (timer) {
                              setState(() {
                                _currentIndex = Random().nextInt(combinedItems.length);
                              });
                            });
                          }
                        },
                        child: ScaleTransition(scale: animation, child: child)),
                  );
                },
                child: Tooltip(
                  message: currentItem is IconData ? currentItem.toString() : "Food emoji",
                  child: displayedWidget,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:bugsnag_flutter/bugsnag_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import '/custom_code/actions/index.dart' as actions;

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'auth/firebase_auth/firebase_user_provider.dart';
import 'auth/firebase_auth/auth_util.dart';

import 'backend/push_notifications/push_notifications_util.dart';
import 'backend/firebase/firebase_config.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import 'flutter_flow/flutter_flow_util.dart';
import 'flutter_flow/internationalization.dart';
import 'index.dart';
import 'flutter_flow/revenue_cat_util.dart' as revenue_cat;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  GoRouter.optionURLReflectsImperativeAPIs = true;
  usePathUrlStrategy();

  await initFirebase();

  await GetStorage.init();

  await FFLocalizations.initialize();

  final appState = FFAppState(); // Initialize FFAppState
  await appState.initializePersistedState();

  await revenue_cat.initialize(
    "appl_PknYchOxEaCDVeTKEYCrihQnxEI",
    "goog_yiXIzYuiqxKiQGlCsekAxpqjgih",
    debugLogEnabled: true,
    loadDataAfterLaunch: true,
  );

  // await initializeFirebaseRemoteConfig();

  final config = PostHogConfig('phc_CzDzxMZ3I199ik5QYyIUKYIYycFD8qopW8Mk5KRuUSP');
  config.debug = kDebugMode;
  config.captureApplicationLifecycleEvents = true;
  config.host = 'https://us.i.posthog.com';
  config.sessionReplay = true;

  await Posthog().setup(config);
  var bugsnagKey = '4428e57bb37154996d4cfccbf939310e';
  bugsnag_performance.start(apiKey: bugsnagKey);

  /// Initialize Bugsnag for critical errors
  await bugsnag.start(
    apiKey: bugsnagKey,
    projectPackages: const BugsnagProjectPackages.only({'com.calcountai.app'}),
    autoDetectErrors: true,
    autoTrackSessions: true,
    releaseStage: kDebugMode ? 'development' : 'production',
    enabledErrorTypes: BugsnagEnabledErrorTypes(
      anrs: true,
      appHangs: true,
      crashes: true,
      unhandledDartExceptions: true,
    ),
    onError: [
      (BugsnagEvent event) {
        if (event.unhandled) {
          event.addMetadata('info', {
            'hint': 'Unhandled exception occurred',
            'severity': 'critical',
            'timestamp': DateTime.now().toIso8601String(),
          });
          return true; // Allow unhandled exceptions
        } else {
          if (event.errors.any((e) => e.errorClass == 'User Reported Bug')) {
            event.addMetadata('info', {
              'hint': 'User-reported bug',
              'severity': 'info',
              'timestamp': DateTime.now().toIso8601String(),
            });
            return true;
          }
          event.addMetadata('info', {
            'hint': 'Handled exception occurred',
            'severity': 'warning',
            'timestamp': DateTime.now().toIso8601String(),
          });
          return false;
        }
      }
    ],
  );

  bugsnag_performance.measureRunApp(() async => runApp(ChangeNotifierProvider(
        create: (context) => appState,
        child: MyApp(),
      )));
}

class MyApp extends StatefulWidget {
  // This widget is the root of your application.
  @override
  State<MyApp> createState() => _MyAppState();

  static _MyAppState of(BuildContext context) => context.findAncestorStateOfType<_MyAppState>()!;
}

class _MyAppState extends State<MyApp> {
  Locale? _locale = FFLocalizations.getStoredLocale();

  ThemeMode _themeMode = ThemeMode.system;

  late AppStateNotifier _appStateNotifier;
  late GoRouter _router;

  late Stream<BaseAuthUser> userStream;

  final authUserSub = authenticatedUserStream.listen((user) {
    revenue_cat.login(user?.uid);
  });
  final fcmTokenSub = fcmTokenUserStream.listen((_) {});

  @override
  void initState() {
    super.initState();

    _appStateNotifier = AppStateNotifier.instance;
    _router = createRouter(_appStateNotifier);
    userStream = calCountiAIFirebaseUserStream()
      ..listen((user) {
        _appStateNotifier.update(user);
      });
    jwtTokenStream.listen((_) {});
    Future.delayed(
      Duration(milliseconds: 1000),
      () => _appStateNotifier.stopShowingSplashImage(),
    );
  }

  @override
  void dispose() {
    authUserSub.cancel();
    fcmTokenSub.cancel();
    super.dispose();
  }

  void setLocale(String language) {
    safeSetState(() => _locale = createLocale(language));
    FFLocalizations.storeLocale(language);
  }

  void setThemeMode(ThemeMode mode) => safeSetState(() {
        _themeMode = mode;
      });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: false,
      left: false,
      right: false,
      top: false,
      child: MaterialApp.router(
        title: 'CalCounti AI',
        debugShowCheckedModeBanner: false,
        localizationsDelegates: [
          FFLocalizationsDelegate(),
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          FallbackMaterialLocalizationDelegate(),
          FallbackCupertinoLocalizationDelegate(),
        ],
        locale: _locale,
        supportedLocales: const [
          // Locale('he'),
          Locale('en'),
        ],
        theme: ThemeData(
          brightness: Brightness.light,
          scrollbarTheme: ScrollbarThemeData(
            thumbVisibility: WidgetStateProperty.all(true),
          ),
          useMaterial3: false,
        ),
        themeMode: _themeMode,
        routerConfig: _router,
        builder: (_, child) => MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: MediaQuery.of(context).textScaler.clamp(
                  minScaleFactor: 1.0,
                  maxScaleFactor: 1.0,
                ),
          ),
          child: child!,
        ),
      ),
    );
  }
}

class NavBarPage extends StatefulWidget {
  NavBarPage({Key? key, this.initialPage, this.page}) : super(key: key);

  final String? initialPage;
  final Widget? page;

  @override
  _NavBarPageState createState() => _NavBarPageState();
}

/// This is the private State class that goes with NavBarPage.
class _NavBarPageState extends State<NavBarPage> {
  String _currentPageName = 'dashboard';
  late Widget? _currentPage;

  @override
  void initState() {
    super.initState();
    _currentPageName = widget.initialPage ?? _currentPageName;
    _currentPage = widget.page;
  }

  @override
  Widget build(BuildContext context) {
    actions.getDeviceSize(
      context,
    );
    final tabs = {
      'dashboard': DashboardWidget(),
      'overview_food': OverviewFoodWidget(),
      'settings': SettingsWidget(),
    };
    final currentIndex = tabs.keys.toList().indexOf(_currentPageName);

    return Scaffold(
      body: _currentPage ?? tabs[_currentPageName],
      bottomNavigationBar: Visibility(
        visible: responsiveVisibility(
          context: context,
          tablet: false,
          tabletLandscape: false,
          desktop: false,
        ),
        child: BottomNavigationBar(
          currentIndex: currentIndex,
          onTap: (i) => safeSetState(() {
            _currentPage = null;
            _currentPageName = tabs.keys.toList()[i];
          }),
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          selectedItemColor: FlutterFlowTheme.of(context).primary,
          unselectedItemColor: Color(0xFF727986),
          showSelectedLabels: true,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          items: <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(
                Icons.home_rounded,
                size: 24.0,
              ),
              label: FFLocalizations.of(context).getText(
                'qeeiespb' /* Home */,
              ),
              tooltip: '',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.restaurant_outlined,
                size: 24.0,
              ),
              label: FFLocalizations.of(context).getText(
                'xh5t4r1k' /* Data Analysis */,
              ),
              tooltip: '',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                Icons.settings_outlined,
                size: 24.0,
              ),
              label: FFLocalizations.of(context).getText(
                'dc0ym1hb' /* Settings */,
              ),
              tooltip: '',
            )
          ],
        ),
      ),
    );
  }
}

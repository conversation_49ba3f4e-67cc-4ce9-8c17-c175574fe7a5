import '/flutter_flow/flutter_flow_choice_chips.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import 'package:flutter/material.dart';
import 'segmented_model.dart';
export 'segmented_model.dart';

class SegmentedWidget extends StatefulWidget {
  const SegmentedWidget({
    super.key,
    required this.segmentItems,
    required this.onSelect,
    required this.initialValue,
  });

  final List<String>? segmentItems;
  final Future Function(String title)? onSelect;
  final String? initialValue;

  @override
  State<SegmentedWidget> createState() => _SegmentedWidgetState();
}

class _SegmentedWidgetState extends State<SegmentedWidget> {
  late SegmentedModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SegmentedModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: EdgeInsets.all(5.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: EdgeInsets.all(5.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                FlutterFlowChoiceChips(
                  options: widget.segmentItems!.map((label) => ChipData(getLabel(label, isEnglish))).toList(),
                  onChanged: (val) async {
                    print('_model.choiceChipsValue ${_model.choiceChipsValue} ${val?.firstOrNull}');
                    safeSetState(() => _model.choiceChipsValue = val?.firstOrNull);
                    logFirebaseEvent('ChoiceChips_execute_callback');
                    await widget.onSelect?.call(
                      getValue(_model.choiceChipsValue, isEnglish)!,
                    );
                  },
                  selectedChipStyle: ChipStyle(
                    backgroundColor: FlutterFlowTheme.of(context).primaryText,
                    textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'SFHebrew',
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                          fontSize: 14.0,
                          letterSpacing: 0.0,
                        ),
                    iconColor: FlutterFlowTheme.of(context).primaryText,
                    iconSize: 18.0,
                    elevation: 0.0,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  unselectedChipStyle: ChipStyle(
                    backgroundColor: Color(0xFFF1F4F8),
                    textStyle: FlutterFlowTheme.of(context).bodySmall.override(
                          fontFamily: 'SFHebrew',
                          color: FlutterFlowTheme.of(context).secondaryText,
                          fontSize: 14.0,
                          letterSpacing: 0.0,
                        ),
                    iconColor: FlutterFlowTheme.of(context).primaryText,
                    iconSize: 18.0,
                    elevation: 0.0,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  chipSpacing: 8.0,
                  rowSpacing: 8.0,
                  multiselect: false,
                  initialized: _model.choiceChipsValue != null,
                  alignment: WrapAlignment.start,
                  controller: _model.choiceChipsValueController ??= FormFieldController<List<String>>(
                    [
                      widget.initialValue != null && widget.initialValue != ''
                          ? getLabel(widget.initialValue!, isEnglish)
                          : getLabel(widget.segmentItems!.firstOrNull!, isEnglish)
                    ],
                  ),
                  wrapped: false,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String getLabel(String label, bool isEnglish) {
    if (isEnglish) {
      switch (label) {
        case 'threeMonth':
          return '3 Months';
        case 'oneMonth':
          return '1 Month';
        case 'twoWeek':
          return '2 Weeks';
        case 'oneWeek':
          return '1 Week';
        case 'fats':
          return 'Fats';
        case 'carbs':
          return 'Carbs';
        case 'protein':
          return 'Protein';
        case 'thisWeek':
          return 'This Week';
        case 'lastWeek':
          return 'Last Week';
        case 'twoWeekAgo':
          return '2 Weeks Ago';
        case 'threeWeekAgo':
          return '3 Weeks Ago';
      }
    }

    switch (label) {
      case 'threeMonth':
        return '3 חודשים';
      case 'oneMonth':
        return 'חודש אחד';
      case 'twoWeek':
        return 'שבועיים';
      case 'oneWeek':
        return 'שבוע אחד';
      case 'fats':
        return 'שומנים';
      case 'carbs':
        return 'פחמימות';
      case 'protein':
        return 'חלבונים';
      case 'thisWeek':
        return 'השבוע';
      case 'lastWeek':
        return 'שבוע שעבר';
      case 'twoWeekAgo':
        return 'לפני שבועיים';
      case 'threeWeekAgo':
        return 'לפני שלושה שבועות';
    }
    return label;
  }

  String? getValue(String? label, bool isEnglish) {
    if (label == null) return null;

    if (isEnglish) {
      switch (label) {
        case '3 Months':
          return 'threeMonth';
        case '1 Month':
          return 'oneMonth';
        case '2 Weeks':
          return 'twoWeek';
        case '1 Week':
          return 'oneWeek';
        case 'Fats':
          return 'fats';
        case 'Carbs':
          return 'carbs';
        case 'Protein':
          return 'protein';
        case 'This Week':
          return 'thisWeek';
        case 'Last Week':
          return 'lastWeek';
        case '2 Weeks Ago':
          return 'twoWeekAgo';
        case '3 Weeks Ago':
          return 'threeWeekAgo';
      }
    }

    switch (label) {
      case '3 חודשים':
        return 'threeMonth';
      case 'חודש אחד':
        return 'oneMonth';
      case 'שבועיים':
        return 'twoWeek';
      case 'שבוע אחד':
        return 'oneWeek';
      case 'שומנים':
        return 'fats';
      case 'פחמימות':
        return 'carbs';
      case 'חלבונים':
        return 'protein';
      case 'השבוע':
        return 'thisWeek';
      case 'שבוע שעבר':
        return 'lastWeek';
      case 'לפני שבועיים':
        return 'twoWeekAgo';
      case 'לפני שלושה שבועות':
        return 'threeWeekAgo';
    }
    return label;
  }
}

import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'center_image_model.dart';
export 'center_image_model.dart';

class CenterImageWidget extends StatefulWidget {
  const CenterImageWidget({
    super.key,
    required this.imageType,
    bool? isSelected,
  }) : this.isSelected = isSelected ?? false;

  final ImageType? imageType;
  final bool isSelected;

  @override
  State<CenterImageWidget> createState() => _CenterImageWidgetState();
}

class _CenterImageWidgetState extends State<CenterImageWidget> {
  late CenterImageModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => CenterImageModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50.0,
      height: 50.0,
      alignment: AlignmentDirectional(0.5, 0.5),
      child: Builder(
        builder: (context) {
          if (widget.imageType == ImageType.oneStar) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/star1.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.twoStar) {
            return SvgPicture.asset(
              'assets/images/star2.svg',
              width: 50.0,
              height: 50.0,
              fit: BoxFit.cover,
            );
          } else if (widget.imageType == ImageType.threeStar) {
            return SvgPicture.asset(
              'assets/images/star3.svg',
              width: 50.0,
              height: 50.0,
              fit: BoxFit.cover,
            );
          } else if (widget.imageType == ImageType.loseWeight) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(1).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/loseweight.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.toneUp) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(2).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/toneup.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.gainWeight) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(3).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/gainweight.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.lackConsistancy) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(6).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/lackconsistancy.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.unhealthyHabit) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(7).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/unhealthy.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.lackSupport) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(8).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/lacksupport.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.busySchedule) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(9).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/busyschedule.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.lackMotivation) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(10).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/lackmotivation.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.eatLiveHealthy) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(11).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/eathealthier.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.boostEnergy) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/energry_white.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/energry_black.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.stayMotivate) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(13).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/staymotivated.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.feelBody) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return SvgPicture.asset(
                    'assets/images/Property_1=Variant2_(14).svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                } else {
                  return SvgPicture.asset(
                    'assets/images/feelbody.svg',
                    width: 50.0,
                    height: 50.0,
                    fit: BoxFit.cover,
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.kg1) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/man_one.svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                } else {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/Property_1=Default_(1).svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.kg2) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/man_two.svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                } else {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/Property_1=Default_(2).svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.kg8) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/Property_1=Variant2_(4).svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                } else {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/man_three.svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                }
              },
            );
          } else if (widget.imageType == ImageType.kg10) {
            return Builder(
              builder: (context) {
                if (widget.isSelected) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/Property_1=Variant2_(5).svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                } else {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(8.0),
                    child: SvgPicture.asset(
                      'assets/images/man_four.svg',
                      width: 200.0,
                      height: 200.0,
                      fit: BoxFit.cover,
                    ),
                  );
                }
              },
            );
          } else {
            return SvgPicture.asset(
              'assets/images/star3.svg',
              width: 50.0,
              height: 50.0,
              fit: BoxFit.cover,
            );
          }
        },
      ),
    );
  }
}

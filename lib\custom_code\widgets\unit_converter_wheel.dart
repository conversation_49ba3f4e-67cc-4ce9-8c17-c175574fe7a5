import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

// Begin custom widget code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

class UnitConverterWheel extends StatefulWidget {
  const UnitConverterWheel({
    super.key,
    this.width,
    this.height,
    required this.onChange,
    this.initialValue,
  });

  final double? width;
  final double? height;
  final Future Function(dynamic data) onChange;
  final dynamic initialValue;

  @override
  State<UnitConverterWheel> createState() => _UnitConverterWheelState();
}

class _UnitConverterWheelState extends State<UnitConverterWheel> {
  // Metric ranges (more realistic ranges)
  final List<int> metricHeightCm =
      List.generate(157, (index) => index + 120);
  final List<int> metricWeightKg = List.generate(
      210,
      (index) => (35 + index * 1));

  // Default values (average adult values)
  static const int defaultMetricHeight = 221; // 170 cm
  static const int defaultMetricWeight = 91; // 70 kg

  late int selectedFeet;
  late int selectedInches;
  late int selectedWeight;
  late int selectedHeightCm;
  late int selectedWeightKg;

  final FixedExtentScrollController _feetController =
      FixedExtentScrollController();
  final FixedExtentScrollController _inchesController =
      FixedExtentScrollController();
  final FixedExtentScrollController _heightController =
      FixedExtentScrollController();
  final FixedExtentScrollController _weightController =
      FixedExtentScrollController();

  @override
  void initState() {
    super.initState();
    _initializeValues(true);
  }

  @override
  void dispose() {
    _feetController.dispose();
    _inchesController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  void _initializeValues([bool fromInit = false]) {
    if (widget.initialValue != null) {
      try {
        if (widget.initialValue['height_value'] != null) {
          selectedHeightCm = widget.initialValue['height_value'];
          selectedHeightCm = _clampValue(selectedHeightCm, metricHeightCm);
          _heightController
              .jumpToItem(metricHeightCm.indexOf(selectedHeightCm));
        } else {
          _setDefaultMetricHeight();
        }

        if (widget.initialValue['weight_value'] != null) {
          selectedWeightKg = widget.initialValue['weight_value'];
          selectedWeightKg =
              _clampValueInt(selectedWeightKg, metricWeightKg);
          _weightController
              .jumpToItem(metricWeightKg.indexOf(selectedWeightKg));
        } else {
          _setDefaultMetricWeight(fromInit);
        }
      } catch (e) {
        _setDefaultValues(fromInit);
      }
    } else {
      _setDefaultValues(fromInit);
    }
    _notifyChange(fromInit);
  }

  T _clampValue<T extends num>(T value, List<T> range) {
    if (!range.contains(value)) {
      return range.reduce((curr, next) =>
          (curr - value).abs() < (next - value).abs() ? curr : next);
    }
    return value;
  }

  int _clampValueInt(int value, List<int> range) {
    if (!range.contains(value)) {
      return range.reduce((curr, next) =>
          (curr - value).abs() < (next - value).abs() ? curr : next);
    }
    return value;
  }

  Future<void> _setDefaultMetricHeight([bool fromInit = false]) async {
    selectedHeightCm = defaultMetricHeight;
    _heightController.jumpToItem(metricHeightCm.indexOf(selectedHeightCm));

    // Scroll to 170cm
    final targetHeight = 170;
    final targetIndex = metricHeightCm.indexOf(targetHeight);
    if (targetIndex != -1) {
      await Future.delayed(Duration(milliseconds: 100));
      _heightController.jumpToItem(targetIndex);
    }
    _notifyChange(fromInit);
  }

  Future<void> _setDefaultMetricWeight([bool fromInit = false]) async {
    selectedWeightKg = defaultMetricWeight;
    _weightController.jumpToItem(metricWeightKg.indexOf(selectedWeightKg));

    // Scroll to 70kg
    final targetWeight = 70;
    final targetIndex = metricWeightKg.indexOf(targetWeight);
    if (targetIndex != -1) {
      await Future.delayed(Duration(milliseconds: 100));
      _weightController.jumpToItem(targetIndex);
    }
    _notifyChange(fromInit);
  }

  void _setDefaultValues([bool fromInit = false]) {
    _setDefaultMetricHeight(fromInit);
    _setDefaultMetricWeight(fromInit);
  }

  Future<void> _notifyChange([bool fromInit = false]) async {
    final Map<String, dynamic> currentValue = {
      'height_value': selectedHeightCm,
      'height_unit': 'cm',
      'weight_value': selectedWeightKg,
      'weight_unit': 'kg'
    };

    if (!fromInit) {
      await widget.onChange(currentValue);
    }
  }

  Widget _buildListWheel({
    required List<dynamic> items,
    required FixedExtentScrollController controller,
    required Function(int) onChanged,
    required dynamic selectedValue,
    required String suffix,
  }) {
    return SizedBox(
      width: 130,
      child: ListWheelScrollView.useDelegate(
        controller: controller,
        itemExtent: 40,
        perspective: 0.005,
        diameterRatio: 1.5,
        overAndUnderCenterOpacity: 0.5,
        physics: const FixedExtentScrollPhysics(),
        childDelegate: ListWheelChildBuilderDelegate(
          childCount: items.length,
          builder: (context, index) {
            final item = items[index];
            final isSelected = item == selectedValue;
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              decoration: BoxDecoration(
                color: isSelected ? Colors.black : null,
                borderRadius: BorderRadius.circular(8),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        )
                      ]
                    : null,
              ),
              padding: const EdgeInsets.all(4),
              child: Center(
                child: Text(
                  '${item.toString()} $suffix',
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black54,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 18,
                  ),
                ),
              ),
            );
          },
        ),
        onSelectedItemChanged: onChanged,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Container(
      width: widget.width,
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  isEnglish ? 'Height' : 'גובה',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Container(
                height: 200,
                child: _buildListWheel(
                  items: metricHeightCm,
                  controller: _heightController,
                  onChanged: (index) {
                    setState(() {
                      selectedHeightCm = metricHeightCm[index];
                      _notifyChange();
                    });
                  },
                  selectedValue: selectedHeightCm,
                  // suffix: 'cm',
                  suffix: isEnglish ? 'cm' : 'סנטימטר',
                ),
              ),
            ],
          ),
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  isEnglish ? 'Weight' : 'משקל',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              Container(
                height: 200,
                child: _buildListWheel(
                  items: metricWeightKg,
                  controller: _weightController,
                  onChanged: (index) {
                    setState(() {
                      selectedWeightKg = metricWeightKg[index];
                      _notifyChange();
                    });
                  },
                  selectedValue: selectedWeightKg,
                  // suffix: 'kg',
                  suffix: isEnglish ? 'kg' : 'קילוגרם',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

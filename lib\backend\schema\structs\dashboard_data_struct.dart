// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class DashboardDataStruct extends FFFirebaseStruct {
  DashboardDataStruct({
    String? date,
    CaloriesStruct? calories,
    FatsStruct? fats,
    ProteinsStruct? proteins,
    CarbsStruct? carbs,
    List<MealDetailStruct>? meals,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _date = date,
        _calories = calories,
        _fats = fats,
        _proteins = proteins,
        _carbs = carbs,
        _meals = meals,
        super(firestoreUtilData);

  // "date" field.
  String? _date;
  String get date => _date ?? '';
  set date(String? val) => _date = val;

  bool hasDate() => _date != null;

  // "calories" field.
  CaloriesStruct? _calories;
  CaloriesStruct get calories => _calories ?? CaloriesStruct();
  set calories(CaloriesStruct? val) => _calories = val;

  void updateCalories(Function(CaloriesStruct) updateFn) {
    updateFn(_calories ??= CaloriesStruct());
  }

  bool hasCalories() => _calories != null;

  // "fats" field.
  FatsStruct? _fats;
  FatsStruct get fats => _fats ?? FatsStruct();
  set fats(FatsStruct? val) => _fats = val;

  void updateFats(Function(FatsStruct) updateFn) {
    updateFn(_fats ??= FatsStruct());
  }

  bool hasFats() => _fats != null;

  // "proteins" field.
  ProteinsStruct? _proteins;
  ProteinsStruct get proteins => _proteins ?? ProteinsStruct();
  set proteins(ProteinsStruct? val) => _proteins = val;

  void updateProteins(Function(ProteinsStruct) updateFn) {
    updateFn(_proteins ??= ProteinsStruct());
  }

  bool hasProteins() => _proteins != null;

  // "carbs" field.
  CarbsStruct? _carbs;
  CarbsStruct get carbs => _carbs ?? CarbsStruct();
  set carbs(CarbsStruct? val) => _carbs = val;

  void updateCarbs(Function(CarbsStruct) updateFn) {
    updateFn(_carbs ??= CarbsStruct());
  }

  bool hasCarbs() => _carbs != null;

  // "meals" field.
  List<MealDetailStruct>? _meals;
  List<MealDetailStruct> get meals => _meals ?? const [];
  set meals(List<MealDetailStruct>? val) => _meals = val;

  void updateMeals(Function(List<MealDetailStruct>) updateFn) {
    updateFn(_meals ??= []);
  }

  bool hasMeals() => _meals != null;

  static DashboardDataStruct fromMap(Map<String, dynamic> data) =>
      DashboardDataStruct(
        date: castToType<String?>(data['date']),
        calories: data['calories'] is CaloriesStruct
            ? data['calories']
            : CaloriesStruct.maybeFromMap(data['calories']),
        fats: data['fats'] is FatsStruct
            ? data['fats']
            : FatsStruct.maybeFromMap(data['fats']),
        proteins: data['proteins'] is ProteinsStruct
            ? data['proteins']
            : ProteinsStruct.maybeFromMap(data['proteins']),
        carbs: data['carbs'] is CarbsStruct
            ? data['carbs']
            : CarbsStruct.maybeFromMap(data['carbs']),
        meals: getStructList(
          data['meals'],
          MealDetailStruct.fromMap,
        ),
      );

  static DashboardDataStruct? maybeFromMap(dynamic data) => data is Map
      ? DashboardDataStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'date': _date,
        'calories': _calories?.toMap(),
        'fats': _fats?.toMap(),
        'proteins': _proteins?.toMap(),
        'carbs': _carbs?.toMap(),
        'meals': _meals?.map((e) => e.toMap()).toList(),
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'date': serializeParam(
          _date,
          ParamType.String,
        ),
        'calories': serializeParam(
          _calories,
          ParamType.DataStruct,
        ),
        'fats': serializeParam(
          _fats,
          ParamType.DataStruct,
        ),
        'proteins': serializeParam(
          _proteins,
          ParamType.DataStruct,
        ),
        'carbs': serializeParam(
          _carbs,
          ParamType.DataStruct,
        ),
        'meals': serializeParam(
          _meals,
          ParamType.DataStruct,
          isList: true,
        ),
      }.withoutNulls;

  static DashboardDataStruct fromSerializableMap(Map<String, dynamic> data) =>
      DashboardDataStruct(
        date: deserializeParam(
          data['date'],
          ParamType.String,
          false,
        ),
        calories: deserializeStructParam(
          data['calories'],
          ParamType.DataStruct,
          false,
          structBuilder: CaloriesStruct.fromSerializableMap,
        ),
        fats: deserializeStructParam(
          data['fats'],
          ParamType.DataStruct,
          false,
          structBuilder: FatsStruct.fromSerializableMap,
        ),
        proteins: deserializeStructParam(
          data['proteins'],
          ParamType.DataStruct,
          false,
          structBuilder: ProteinsStruct.fromSerializableMap,
        ),
        carbs: deserializeStructParam(
          data['carbs'],
          ParamType.DataStruct,
          false,
          structBuilder: CarbsStruct.fromSerializableMap,
        ),
        meals: deserializeStructParam<MealDetailStruct>(
          data['meals'],
          ParamType.DataStruct,
          true,
          structBuilder: MealDetailStruct.fromSerializableMap,
        ),
      );

  @override
  String toString() => 'DashboardDataStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is DashboardDataStruct &&
        date == other.date &&
        calories == other.calories &&
        fats == other.fats &&
        proteins == other.proteins &&
        carbs == other.carbs &&
        listEquality.equals(meals, other.meals);
  }

  @override
  int get hashCode =>
      const ListEquality().hash([date, calories, fats, proteins, carbs, meals]);
}

DashboardDataStruct createDashboardDataStruct({
  String? date,
  CaloriesStruct? calories,
  FatsStruct? fats,
  ProteinsStruct? proteins,
  CarbsStruct? carbs,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    DashboardDataStruct(
      date: date,
      calories: calories ?? (clearUnsetFields ? CaloriesStruct() : null),
      fats: fats ?? (clearUnsetFields ? FatsStruct() : null),
      proteins: proteins ?? (clearUnsetFields ? ProteinsStruct() : null),
      carbs: carbs ?? (clearUnsetFields ? CarbsStruct() : null),
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

DashboardDataStruct? updateDashboardDataStruct(
  DashboardDataStruct? dashboardData, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    dashboardData
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addDashboardDataStructData(
  Map<String, dynamic> firestoreData,
  DashboardDataStruct? dashboardData,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (dashboardData == null) {
    return;
  }
  if (dashboardData.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && dashboardData.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final dashboardDataData =
      getDashboardDataFirestoreData(dashboardData, forFieldValue);
  final nestedData =
      dashboardDataData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = dashboardData.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getDashboardDataFirestoreData(
  DashboardDataStruct? dashboardData, [
  bool forFieldValue = false,
]) {
  if (dashboardData == null) {
    return {};
  }
  final firestoreData = mapToFirestore(dashboardData.toMap());

  // Handle nested data for "calories" field.
  addCaloriesStructData(
    firestoreData,
    dashboardData.hasCalories() ? dashboardData.calories : null,
    'calories',
    forFieldValue,
  );

  // Handle nested data for "fats" field.
  addFatsStructData(
    firestoreData,
    dashboardData.hasFats() ? dashboardData.fats : null,
    'fats',
    forFieldValue,
  );

  // Handle nested data for "proteins" field.
  addProteinsStructData(
    firestoreData,
    dashboardData.hasProteins() ? dashboardData.proteins : null,
    'proteins',
    forFieldValue,
  );

  // Handle nested data for "carbs" field.
  addCarbsStructData(
    firestoreData,
    dashboardData.hasCarbs() ? dashboardData.carbs : null,
    'carbs',
    forFieldValue,
  );

  // Add any Firestore field values
  dashboardData.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getDashboardDataListFirestoreData(
  List<DashboardDataStruct>? dashboardDatas,
) =>
    dashboardDatas
        ?.map((e) => getDashboardDataFirestoreData(e, true))
        .toList() ??
    [];

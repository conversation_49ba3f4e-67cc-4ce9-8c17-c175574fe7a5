import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/backend/api_requests/api_calls.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:flutter/material.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_theme.dart';

class EditWeight extends StatefulWidget {
  final double initialWeight;
  const EditWeight({super.key, this.initialWeight = 70.0});

  @override
  State<EditWeight> createState() => _EditWeightState();
}

class _EditWeightState extends State<EditWeight> {
  bool isMetric = true;
  late double currentWeight;

  FixedExtentScrollController? _scrollController;

  @override
  void initState() {
    super.initState();
    currentWeight = widget.initialWeight;
    final minWeight = isMetric ? 30.0 : 66.0;
    final initialItem = ((currentWeight - minWeight) * 2).round();
    _scrollController = FixedExtentScrollController(initialItem: initialItem);
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    final weightUnit = isMetric ? 'ק"ג' : 'פאונד ';
    final minWeight = isMetric ? 30.0 : 66.0;
    final maxWeight = isMetric ? 150.0 : 200.0;

    return MeasuredWidget(
        name: 'EditWeight',
        builder: (context) => Container(
              color: Colors.white,
              padding: EdgeInsetsDirectional.fromSTEB(
                  0.0,
                  valueOrDefault<double>(
                    FFAppState().topPadding,
                    0.0,
                  ),
                  0.0,
                  0.0),
              child: Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  backgroundColor: Colors.white,
                  elevation: 0,
                  leading: IconButton(
                    icon: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: const Icon(Icons.arrow_back, color: Colors.black),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  centerTitle: true,
                  title: Text(
                    isEnglish ? 'Edit Weight' : 'ערוך משקל',
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontSize: 18,
                        ),
                  ),
                ),
                body: SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 150),
                      // Toggle
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.center,
                      //   children: [
                      //     Text(
                      //       'Imperial',
                      //       style: FlutterFlowTheme.of(context).bodyMedium.override(
                      //             fontSize: 18,
                      //             fontWeight: FontWeight.w600,
                      //             color: !isMetric ? Colors.black : Colors.grey.shade400,
                      //           ),
                      //     ),
                      //     SizedBox(
                      //       width: 10,
                      //     ),
                      //     FlutterSwitch(
                      //       value: isMetric,
                      //       width: 50,
                      //       height: 30,
                      //       onToggle: (value) {
                      //         setState(() {
                      //           isMetric = value;
                      //           // Snap to new scale
                      //           final minWeight = isMetric ? 30.0 : 66.0;
                      //           final maxWeight = isMetric ? 150.0 : 200.0;
                      //           // Clamp currentWeight to new scale
                      //           currentWeight = currentWeight.clamp(minWeight, maxWeight);
                      //           final initialItem =
                      //               ((currentWeight - minWeight) * 10).round();
                      //           _scrollController = FixedExtentScrollController(
                      //             initialItem: initialItem,
                      //           );
                      //         });
                      //       },
                      //       activeColor: Colors.black,
                      //       inactiveColor: Colors.grey.shade300,
                      //       inactiveToggleColor: Colors.grey.shade400,
                      //     ),
                      //     SizedBox(
                      //       width: 10,
                      //     ),
                      //     Text(
                      //       'Metric',
                      //       style: FlutterFlowTheme.of(context).bodyMedium.override(
                      //             fontSize: 18,
                      //             fontWeight: FontWeight.w600,
                      //             color: isMetric ? Colors.black : Colors.grey.shade400,
                      //           ),
                      //     ),
                      //   ],
                      // ),
                      const SizedBox(height: 24),
                      Text(
                        isEnglish ? 'Current Weight' : 'משקל נוכחי',
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              color: Colors.black87,
                              fontSize: 16,
                            ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${currentWeight.toStringAsFixed(1)} $weightUnit',
                        style: FlutterFlowTheme.of(context)
                            .headlineMedium
                            .override(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      // Horizontal Picker
                      SizedBox(
                        height: 100,
                        child: RotatedBox(
                          quarterTurns: -1,
                          child: ListWheelScrollView.useDelegate(
                            controller: _scrollController,
                            itemExtent: 70,
                            diameterRatio: 2.5,
                            physics: const FixedExtentScrollPhysics(),
                            onSelectedItemChanged: (index) {
                              setState(() {
                                currentWeight = minWeight +
                                    (index * 0.5); // 0.5 step increment
                              });
                            },
                            childDelegate: ListWheelChildBuilderDelegate(
                              builder: (context, index) {
                                final value =
                                    minWeight + (index * 0.5); // 0.5 step value
                                if (index < 0 || value > maxWeight)
                                  return null; // Valid range
                                return RotatedBox(
                                  quarterTurns: 1,
                                  child: Center(
                                    child: Container(
                                      width: 70,
                                      child: Text(
                                        value.toStringAsFixed(
                                            1), // Display with one decimal (e.g., 70.5)
                                        style: FlutterFlowTheme.of(context)
                                            .titleLarge
                                            .override(
                                              fontSize: value == currentWeight
                                                  ? 26
                                                  : 16,
                                              fontWeight: value == currentWeight
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                              color: value == currentWeight
                                                  ? Colors.black
                                                  : Colors.grey,
                                            ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                              childCount: ((maxWeight - minWeight) * 2 + 1)
                                  .toInt(), // 0.5 step count
                            ),
                          ),
                        ),
                      ),
                      const Spacer(),
                      // Save Button
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 18),
                        child: SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.black,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            onPressed: () async {
                              showDialog(
                                context: context,
                                barrierDismissible: false,
                                builder: (context) => const Center(
                                    child: CircularProgressWidget()),
                              );
                              try {
                                final user = FFAppState().savedUserData;
                                final accessToken = FFAppState().authToken;
                                final result = await WeightLogCall.call(
                                  accessToken: accessToken,
                                  // fitnessGoalId: user.fitnessGoalId,
                                  targetWeightValue: currentWeight,
                                  // targetWeightUnit: isMetric ? 'kg' : 'lb',
                                  // goalAchievementRate: user.goalAchievementRate,
                                  // challenges: user.challenges,
                                  // accomplishmentId: user.accomplishmentId,
                                );
                                Navigator.of(context).pop();
                                if (result.succeeded) {
                                  FFAppState().updateSavedUserDataStruct((e) =>
                                      e..weightValue = currentWeight.toInt());
                                  Navigator.of(context).pop(currentWeight);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text(isEnglish
                                            ? 'Weight log updated successfully!'
                                            : 'רישום המשקל עודכן בהצלחה!')),
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text(isEnglish
                                            ? 'Failed to update weight log.'
                                            : 'נכשל עדכון רישום המשקל.')),
                                  );
                                }
                              } catch (e) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                      content: Text('Error: ${e.toString()}')),
                                );
                              }
                            },
                            child: Text(
                              isEnglish ? 'Save changes' : 'שמור שינויים',
                              style: FlutterFlowTheme.of(context)
                                  .titleLarge
                                  .override(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }
}

// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCRhk6yOzCVLvls-p-clMIOiKVvFi1E6IQ',
    appId: '1:1075353593734:android:2485a2a554922b62f06e07',
    messagingSenderId: '1075353593734',
    projectId: 'calcountai-5f099',
    storageBucket: 'calcountai-5f099.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBk_B3UMmRCotXqJN4Ep8S3j9SMg-pKNmg',
    appId: '1:1075353593734:ios:8571a773f6d7ff9bf06e07',
    messagingSenderId: '1075353593734',
    projectId: 'calcountai-5f099',
    storageBucket: 'calcountai-5f099.firebasestorage.app',
    androidClientId: '1075353593734-3u2vlnvbmq744902ppqbohc3vmtnpefk.apps.googleusercontent.com',
    iosClientId: '1075353593734-06lc5egpvrpn7173367i4m5f68eb4b8l.apps.googleusercontent.com',
    iosBundleId: 'com.calcountiai.app',
  );
}

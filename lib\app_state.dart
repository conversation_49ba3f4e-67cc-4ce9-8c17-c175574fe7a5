import 'package:flutter/material.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
// import 'package:csv/csv.dart';
import 'package:synchronized/synchronized.dart';
import 'flutter_flow/flutter_flow_util.dart';

class FFAppState extends ChangeNotifier {
  static FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal();

  static void reset() {
    _instance = FFAppState._internal();
  }

  Future initializePersistedState() async {
    secureStorage = FlutterSecureStorage();
    await _safeInitAsync(() async {
      _deviceWidth =
          await secureStorage.getDouble('ff_deviceWidth') ?? _deviceWidth;
    });
    await _safeInitAsync(() async {
      _deviceHeight =
          await secureStorage.getDouble('ff_deviceHeight') ?? _deviceHeight;
    });
    await _safeInitAsync(() async {
      _bottomPadding =
          await secureStorage.getDouble('ff_bottomPadding') ?? _bottomPadding;
    });
    await _safeInitAsync(() async {
      _topPadding =
          await secureStorage.getDouble('ff_topPadding') ?? _topPadding;
    });
    await _safeInitAsync(() async {
      _isOnboardingCompleted =
          await secureStorage.getBool('ff_isOnboardingCompleted') ??
              _isOnboardingCompleted;
    });
    await _safeInitAsync(() async {
      if (await secureStorage.read(key: 'ff_savedUserData') != null) {
        try {
          final serializedData =
              await secureStorage.getString('ff_savedUserData') ?? '{}';
          _savedUserData =
              UserDataStruct.fromSerializableMap(jsonDecode(serializedData));
        } catch (e) {
          print("Can't decode persisted data type. Error: $e.");
        }
      }
    });
    await _safeInitAsync(() async {
      _authToken = await secureStorage.getString('ff_authToken') ?? _authToken;
    });
    await _safeInitAsync(() async {
      _languageCode =
          await secureStorage.getString('ff_languageCode') ?? _languageCode;
    });

    await _safeInitAsync(() async {
      _isOnboardingCompleted =
          await secureStorage.getBool('ff_isOnboardingCompleted') ?? _isOnboardingCompleted;
    });
    await _safeInitAsync(() async {
      _hasAcceptedTerms =
          await secureStorage.getBool('ff_hasAcceptedTerms') ?? _hasAcceptedTerms;
    });
    await _safeInitAsync(() async {
      _hasSeenDisclaimer =
          await secureStorage.getBool('ff_hasSeenDisclaimer') ?? _hasSeenDisclaimer;
    });
    await _safeInitAsync(() async {
      _hasSeenMedicalNotice =
          await secureStorage.getBool('ff_hasSeenMedicalNotice') ?? _hasSeenMedicalNotice;
    });
  }

  void update(VoidCallback callback) {
    callback();
    notifyListeners();
  }

  late FlutterSecureStorage secureStorage;

  int _dialogShowCount = 0;
  int get dialogShowCount => _dialogShowCount;
  set dialogShowCount(int value) {
    _dialogShowCount = value;
    secureStorage.setInt('ff_dialogShowCount', value);
  }

  bool _todayShown = false;
  bool get todayShown => _todayShown;
  set todayShown(bool value) {
    _todayShown = value;
  }

  double _deviceWidth = 0.0;
  double get deviceWidth => _deviceWidth;
  set deviceWidth(double value) {
    _deviceWidth = value;
    secureStorage.setDouble('ff_deviceWidth', value);
  }

  void deleteDeviceWidth() {
    secureStorage.delete(key: 'ff_deviceWidth');
  }

  double _deviceHeight = 0.0;
  double get deviceHeight => _deviceHeight;
  set deviceHeight(double value) {
    _deviceHeight = value;
    secureStorage.setDouble('ff_deviceHeight', value);
  }

  void deleteDeviceHeight() {
    secureStorage.delete(key: 'ff_deviceHeight');
  }

  double _bottomPadding = 0.0;
  double get bottomPadding => _bottomPadding;
  set bottomPadding(double value) {
    _bottomPadding = value;
    secureStorage.setDouble('ff_bottomPadding', value);
  }

  void deleteBottomPadding() {
    secureStorage.delete(key: 'ff_bottomPadding');
  }

  double _topPadding = 0.0;
  double get topPadding => _topPadding;
  set topPadding(double value) {
    _topPadding = value;
    secureStorage.setDouble('ff_topPadding', value);
  }

  void deleteTopPadding() {
    secureStorage.delete(key: 'ff_topPadding');
  }

  bool _isOnboardingCompleted = false;
  bool get isOnboardingCompleted => _isOnboardingCompleted;
  set isOnboardingCompleted(bool value) {
    _isOnboardingCompleted = value;
    secureStorage.setBool('ff_isOnboardingCompleted', value);
  }

  void deleteIsOnboardingCompleted() {
    secureStorage.delete(key: 'ff_isOnboardingCompleted');
  }

  bool _hasAcceptedTerms = false;
  bool get hasAcceptedTerms => _hasAcceptedTerms;
  set hasAcceptedTerms(bool value) {
    _hasAcceptedTerms = value;
    secureStorage.setBool('ff_hasAcceptedTerms', value);
  }

  void deletehasAcceptedTerms() {
    secureStorage.delete(key: 'ff_hasAcceptedTerms');
  }


  bool _hasSeenDisclaimer = false;
  bool get hasSeenDisclaimer => _hasSeenDisclaimer;
  set hasSeenDisclaimer(bool value) {
    _hasSeenDisclaimer = value;
    secureStorage.setBool('ff_hasSeenDisclaimer', value);
  }

  void deletehasSeenDisclaimer() {
    secureStorage.delete(key: 'ff_hasSeenDisclaimer');
  }

  bool _hasSeenMedicalNotice = false;
  bool get hasSeenMedicalNotice => _hasSeenMedicalNotice;
  set hasSeenMedicalNotice(bool value) {
    _hasSeenMedicalNotice = value;
    secureStorage.setBool('ff_hasSeenMedicalNotice', value);
  }

  void deletehasSeenMedicalNotice() {
    secureStorage.delete(key: 'ff_hasSeenMedicalNotice');
  }

  UserDataStruct _savedUserData = UserDataStruct();
  UserDataStruct get savedUserData => _savedUserData;
  set savedUserData(UserDataStruct value) {
    _savedUserData = value;
    secureStorage.setString('ff_savedUserData', value.serialize());
  }

  void deleteSavedUserData() {
    secureStorage.delete(key: 'ff_savedUserData');
  }

  void updateSavedUserDataStruct(Function(UserDataStruct) updateFn) {
    updateFn(_savedUserData);
    secureStorage.setString('ff_savedUserData', _savedUserData.serialize());
  }

  String _authToken = '';
  String get authToken => _authToken;
  set authToken(String value) {
    _authToken = value;
    secureStorage.setString('ff_authToken', value);
  }

  void deleteAuthToken() {
    secureStorage.delete(key: 'ff_authToken');
  }

  String _appVersion = '';
  String get appVersion => _appVersion;
  set appVersion(String value) {
    _appVersion = value;
  }

  String _languageCode = '';
  String get languageCode => _languageCode;
  set languageCode(String value) {
    _languageCode = value;
    secureStorage.setString('ff_languageCode', value);
  }

  void deleteLanguageCode() {
    secureStorage.delete(key: 'ff_languageCode');
  }
}

void _safeInit(Function() initializeField) {
  try {
    initializeField();
  } catch (_) {}
}

Future _safeInitAsync(Function() initializeField) async {
  try {
    await initializeField();
  } catch (_) {}
}

extension FlutterSecureStorageExtensions on FlutterSecureStorage {
  static final _lock = Lock();

  Future<void> writeSync({required String key, String? value}) async =>
      await _lock.synchronized(() async {
        await write(key: key, value: value);
      });

  void remove(String key) => delete(key: key);

  Future<String?> getString(String key) async => await read(key: key);
  Future<void> setString(String key, String value) async =>
      await writeSync(key: key, value: value);

  Future<bool?> getBool(String key) async => (await read(key: key)) == 'true';
  Future<void> setBool(String key, bool value) async =>
      await writeSync(key: key, value: value.toString());

  Future<int?> getInt(String key) async =>
      int.tryParse(await read(key: key) ?? '');
  Future<void> setInt(String key, int value) async =>
      await writeSync(key: key, value: value.toString());

  Future<double?> getDouble(String key) async =>
      double.tryParse(await read(key: key) ?? '');
  Future<void> setDouble(String key, double value) async =>
      await writeSync(key: key, value: value.toString());

  // Future<List<String>?> getStringList(String key) async =>
  //     await read(key: key).then((result) {
  //       if (result == null || result.isEmpty) {
  //         return null;
  //       }
  //       return CsvToListConverter()
  //           .convert(result)
  //           .first
  //           .map((e) => e.toString())
  //           .toList();
  //     });
  // Future<void> setStringList(String key, List<String> value) async =>
  //     await writeSync(key: key, value: ListToCsvConverter().convert([value]));
}

// final String appTerms = 'https://calcountai.wixsite.com/calcountai/terms-conditions';
// String get appPrivacy => 'https://calcountai.wixsite.com/calcountai/terms-conditions/privacypolicy';

final String appTerms =
    'https://docs.google.com/document/d/1XtrB4eFQox_g30e1pr0hwS-sc_vwn9q0-7EHjj4ZO4g/edit?usp=sharing';

String get appPrivacy =>
    'https://docs.google.com/document/d/1HkeVAf5y_XCLVxSfuXq5cyIyRgfgALIKXZVS8xRyQwE/edit?usp=sharing';
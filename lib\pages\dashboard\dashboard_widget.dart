import 'dart:async';
import 'dart:io';

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/custom_code/widgets/camera_custom_widget.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:cal_counti_a_i/auth/firebase_auth/auth_util.dart';
import 'package:cal_counti_a_i/pages/dashboard/food_scan_modal.dart';
import 'package:cal_counti_a_i/pages/nutrition_citations/nutrition_citations_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:path_provider/path_provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:webviewx_plus/webviewx_plus.dart';

import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/componentes/circular_progress/circular_progress_widget.dart';
import '/componentes/no_meals/no_meals_widget.dart';
import '/componentes/recent_meal_item/recent_meal_item_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/permissions_util.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;

// Add imports for FoodItem, FoodDetails, MealDetailStruct
import '../../backend/schema/structs/food_item_struct.dart';
import '../../error_service.dart';
import 'dashboard_model.dart';

export 'dashboard_model.dart';

class DashboardWidget extends StatefulWidget {
  const DashboardWidget({super.key});

  @override
  State<DashboardWidget> createState() => _DashboardWidgetState();
}

class _DashboardWidgetState extends State<DashboardWidget> with TickerProviderStateMixin {
  // Helper to convert MealDetailStruct to FoodItem for manual meals
  FoodItem? mealsStructToFoodItem(MealDetailStruct meal) {
    try {
      if (meal == null || meal.name.isEmpty) {
        print('mealsStructToFoodItem: Invalid meal struct');
        return null;
      }
      return FoodItem(
        id: meal.id,
        enName: meal.name,
        heName: meal.name,
        // Adjust if you have Hebrew name
        data: FoodDetails(
          id: meal.id.toString(),
          name: meal.name,
          brand: '',
          fats: double.tryParse(meal.totalFats) ?? 0,
          carbs: double.tryParse(meal.totalCarbs) ?? 0,
          protein: double.tryParse(meal.totalProteins) ?? 0,
          calories: double.tryParse(meal.totalCalories) ?? 0,
          servings: 1,
          servingTypeId: '',
          servingTypes: const [],
        ),
        created_at: meal.scannedAt,
        relevance: null,
      );
    } catch (e) {
      print('mealsStructToFoodItem error: ' + e.toString());
      return null;
    }
  }

  late DashboardModel _model;
  var _isDialogShow = false;
  var _processedBarcode = false;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};
  static const _maxDialogShowCount = 1;
  bool isFreeUser = false;

  @override
  void initState() {
    super.initState();
    try {
      ErrorService.setupUser(
        currentUserReference?.id ?? '',
        currentUserDocument?.email ?? '',
        currentUserDocument?.displayName ?? '',
      );
    } catch (e) {
      print(e);
    }
    _model = createModel(context, () => DashboardModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('dashboard_update_page_state');
      _model.selectedDate = getCurrentTimestamp;
      _model.isInProgress = true;
      actions.getFCMToken().then((tokenStr) {
        FFAppState().updateSavedUserDataStruct((e) => e..fcmToken = tokenStr);
        print('tokenStr: $tokenStr');
        currentUserReference!.update(createUsersRecordData(facmToken: tokenStr));
      });
      if (!(FFAppState().authToken != '')) {
        logFirebaseEvent('dashboard_navigate_to');

        context.goNamed('entry_screen');

        logFirebaseEvent('dashboard_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        return;
      }
      logFirebaseEvent('dashboard_alert_dialog');
      showDialog(
        context: context,
        builder: (dialogContext) {
          _isDialogShow = true;
          return Dialog(
            elevation: 0,
            insetPadding: EdgeInsets.zero,
            backgroundColor: Colors.transparent,
            alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
            child: WebViewAware(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(dialogContext).unfocus();
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: Container(
                  height: 100.0,
                  width: 100.0,
                  child: CircularProgressWidget(),
                ),
              ),
            ),
          );
        },
      ).whenComplete(() => _isDialogShow = false);

      logFirebaseEvent('dashboard_wait__delay');
      await Future.delayed(const Duration(milliseconds: 10));
      logFirebaseEvent('dashboard_backend_call');
      final String timezone = await FlutterTimezone.getLocalTimezone();
      _model.dashboardApiResult = await DashboardDataCall.call(
        accessToken: FFAppState().authToken,
        date: _model.selectedDate?.toUtc().toIso8601String(),
        timezone: timezone,
        // date: dateTimeFormat(
        //   "yyyy-MM-dd",
        //   _model.selectedDate,
        //   locale: isEnglish ? 'en' : 'he',
        // ),
      );

      logFirebaseEvent('dashboard_wait__delay');
      await Future.delayed(const Duration(milliseconds: 50));
      logFirebaseEvent('dashboard_update_page_state');
      _model.isInProgress = false;
      safeSetState(() {});
      logFirebaseEvent('dashboard_dismiss_dialog');
      if (_isDialogShow && mounted) {
        Navigator.pop(context);
        _isDialogShow = false;
      }
      if ((_model.dashboardApiResult?.succeeded ?? true)) {
        logFirebaseEvent('dashboard_update_page_state');
        _model.dashboardData = DashboardDataCall.data(
          (_model.dashboardApiResult?.jsonBody ?? ''),
        );
        safeSetState(() {});
        Future.delayed(Duration(seconds: 1)).whenComplete(() {
          // showDialogIfNeeded(context);
        });
      }
    });

    animationsMap.addAll({
      'containerOnPageLoadAnimation1': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'containerOnPageLoadAnimation2': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'containerOnPageLoadAnimation3': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'containerOnPageLoadAnimation4': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'containerOnPageLoadAnimation5': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'listViewOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
      'circularProgressOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
      'imageOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
    });
  }

  bool _shouldShowDialog() {
    int currentCount = FFAppState().dialogShowCount;
    bool todayShown = FFAppState().todayShown;

    if (currentCount < _maxDialogShowCount && !todayShown) {
      return true;
    }
    return false;
  }

  Future<void> showDialogIfNeeded(BuildContext context) async {
    if (await _shouldShowDialog()) {
      int currentCount = FFAppState().dialogShowCount;
      FFAppState().dialogShowCount = currentCount + 1;
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => InitialWelcomeDialog(),
      );
    }
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  bool isEnglish = false;

  Future<void> _navigateToAdjustGoal() async {
    logFirebaseEvent('Container_navigate_to');
    int parseSafely(String? value) {
      if (value == null || value.isEmpty) return 0;
      final numeric = value.replaceAll(RegExp(r'[^0-9]'), '');
      return int.tryParse(numeric) ?? 0;
    }

    // Check if dashboardData or its fields are null
    if (_model.dashboardData == null ||
        _model.dashboardData!.calories == null ||
        _model.dashboardData!.proteins == null ||
        _model.dashboardData!.carbs == null ||
        _model.dashboardData!.fats == null) {
      print('Error: dashboardData or its fields are null');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish ? 'Error: Nutrition data not loaded' : 'שגיאה: נתוני תזונה לא נטענו',
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).error,
                ),
          ),
          duration: Duration(milliseconds: 2000),
          backgroundColor: FlutterFlowTheme.of(context).secondary,
        ),
      );
      return;
    }

    // Extract values
    final calories = _model.dashboardData!.calories.caloriesRequired;
    final protein = parseSafely(_model.dashboardData!.proteins.proteinsRequired);
    final carbs = parseSafely(_model.dashboardData!.carbs.carbsRequired);
    final fats = parseSafely(_model.dashboardData!.fats.fatsRequired);

    // Check if all values are zero
    if (calories == 0 && protein == 0 && carbs == 0 && fats == 0) {
      print('Warning: All nutrition goals are zero');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish
                ? 'No goals set. Please set your nutrition goals.'
                : 'לא הוגדרו יעדים. אנא הגדר את יעדי התזונה שלך.',
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).error,
                ),
          ),
          duration: Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).secondary,
        ),
      );
      return;
    }

    var result = await context.pushNamed(
      'adjust_goal',
      queryParameters: {
        'initialCalories': serializeParam(
          calories,
          ParamType.int,
        ),
        'initialProtein': serializeParam(
          protein,
          ParamType.int,
        ),
        'initialCarbs': serializeParam(
          carbs,
          ParamType.int,
        ),
        'initialFats': serializeParam(
          fats,
          ParamType.int,
        ),
      }.withoutNulls,
    );

    if (result is Map<String, dynamic>) {
      setState(() {
        _model.dashboardData!.calories.caloriesRequired = (result['calories'] as int?) ?? calories;
        final proteinUnit = (_model.dashboardData!.proteins.proteinsRequired).replaceAll(RegExp(r'[0-9]'), '');
        _model.dashboardData!.proteins.proteinsRequired =
            ((result['protein'] as int?) ?? protein).toString() + proteinUnit;
        final carbsUnit = (_model.dashboardData!.carbs.carbsRequired).replaceAll(RegExp(r'[0-9]'), '');
        _model.dashboardData!.carbs.carbsRequired = ((result['carbs'] as int?) ?? carbs).toString() + carbsUnit;
        final fatsUnit = (_model.dashboardData!.fats.fatsRequired).replaceAll(RegExp(r'[0-9]'), '');
        _model.dashboardData!.fats.fatsRequired = ((result['fats'] as int?) ?? fats).toString() + fatsUnit;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
      name: 'Dashboard',
      builder: (context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          floatingActionButton: Builder(
            builder: (context) => FloatingActionButton(
              onPressed: () {
                showModalBottomSheet(
                  useSafeArea: true,
                  clipBehavior: Clip.antiAlias,
                  context: context,
                  backgroundColor: Colors.transparent,
                  isScrollControlled: true,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  builder: (context) => FoodScanModal(
                    onScanBarcode: () async {
                      Navigator.pop(context);
                      await _onScanBarcode(context);
                    },
                    onFoodDatabase: () async {
                      Navigator.pop(context);
                      bool _isValid = await validatePermissionAndSusbscription();
                      if (!_isValid) {
                        return;
                      }
                      var res = await context.pushNamed('food_database_log');
                      if (res != null) {
                        fetchMealsAgain(context, true, false);
                      }
                    },
                    onScanFood: () async {
                      Navigator.pop(context);

                      bool _isValid = await validatePermissionAndSusbscription();
                      if (_isValid) {
                        var imageData = await proceedWithCamera(context);
                        if (imageData != null) {
                          Future.delayed(const Duration(milliseconds: 1500)).then((_) {
                            fetchMealsAgain(context, true, false);
                          });
                        }
                      }
                    },
                  ),
                );
              },
              backgroundColor: FlutterFlowTheme.of(context).primaryText,
              elevation: 8.0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: Icon(
                  Icons.add,
                  size: 30,
                ),
              ),
            ),
          ),
          appBar: AppBar(
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            automaticallyImplyLeading: false,
            actions: [],
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: const EdgeInsets.all(0),
              title: Padding(
                padding: EdgeInsetsDirectional.fromSTEB(
                    0.0,
                    valueOrDefault<double>(
                      FFAppState().topPadding + 16,
                      0.0,
                    ),
                    0.0,
                    0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 0.0, 0.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  '2xe2fqe6' /* CalCounti AI */,
                                ),
                                style: FlutterFlowTheme.of(context).headlineMedium.override(
                                      fontFamily: 'SFHebrew',
                                      color: FlutterFlowTheme.of(context).primary,
                                      letterSpacing: 0.0,
                                      fontWeight: FontWeight.w900,
                                    ),
                              ),
                            ),
                            SizedBox(width: 8),
                            GestureDetector(
                              onDoubleTap: () {
                                isFreeUser = true;
                              },
                              child: Image.asset(
                                'assets/images/avocado.png',
                                width: 35.0,
                                height: 35.0,
                                fit: BoxFit.cover,
                              ).animateOnPageLoad(animationsMap['imageOnPageLoadAnimation']!),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              centerTitle: false,
              expandedTitleScale: 1.0,
            ),
            toolbarHeight: 80.0,
            elevation: 2.0,
          ),
          body: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 5.0, 0.0, 0.0),
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).secondaryBackground,
                  ),
                ),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    logFirebaseEvent('RefreshIndicator_onRefresh');
                    await onRefresh(_model.selectedDate ?? DateTime.now());
                    return Future.value();
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        /// header date view
                        headerDateView(),

                        /// Calories
                        caloriesWidget(),

                        Builder(builder: (context) {
                          int _parseNutrient(String? value) =>
                              int.parse((value ?? '0').replaceAll(RegExp(r'[^0-9]'), ''));

                          String _getUnit(String? value) => (value ?? '').replaceAll(RegExp(r'[0-9]'), '');

                          final proteins = _model.dashboardData?.proteins;
                          final carbs = _model.dashboardData?.carbs;
                          final fats = _model.dashboardData?.fats;

                          final proteinsContained = _parseNutrient(proteins?.proteinsContained);
                          final proteinsRequired = _parseNutrient(proteins?.proteinsRequired);
                          final proteinsUnit = _getUnit(proteins?.proteinsRequired);

                          final carbsContained = _parseNutrient(carbs?.carbsContained);
                          final carbsRequired = _parseNutrient(carbs?.carbsRequired);
                          final carbsUnit = _getUnit(carbs?.carbsRequired);

                          final fatsContained = _parseNutrient(fats?.fatsContained);
                          final fatsRequired = _parseNutrient(fats?.fatsRequired);
                          final fatsUnit = _getUnit(fats?.fatsRequired);

                          final isGreaterProteins = proteinsContained >= proteinsRequired;
                          final isGreaterCarbs = carbsContained >= carbsRequired;
                          final isGreaterFats = fatsContained >= fatsRequired;
                          return Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                /// Proteins
                                proteinsWidget(
                                    context, proteinsRequired, proteinsContained, proteinsUnit, isGreaterProteins),

                                /// Carbs
                                carbsWidget(context, carbsRequired, carbsContained, carbsUnit, isGreaterCarbs),

                                /// Fats
                                fatsWidget(context, fatsRequired, fatsContained, fatsUnit, isGreaterFats),
                              ],
                            ),
                          );
                        }),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 50.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'f902qa66' /* Meals You've Eaten Recently */,
                                  ),
                                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                              ),
                              Builder(
                                builder: (context) {
                                  if (!_model.isInProgress) {
                                    return Container(
                                      width: double.infinity,
                                      constraints: BoxConstraints(
                                        minHeight: MediaQuery.sizeOf(context).height * 0.1,
                                      ),
                                      decoration: BoxDecoration(
                                        color: FlutterFlowTheme.of(context).secondaryBackground,
                                        borderRadius: BorderRadius.circular(10.0),
                                        border: Border.all(
                                          color: Color(0xFF9C9C9C),
                                        ),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                                        child: Builder(
                                          builder: (context) {
                                            if (_model.isInProgress) {
                                              return loadingIndicator(context);
                                            }

                                            return Container(
                                              width: double.infinity,
                                              constraints: BoxConstraints(
                                                minHeight: MediaQuery.sizeOf(context).height * 0.1,
                                              ),
                                              child: Padding(
                                                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                                                child: Builder(
                                                  builder: (context) {
                                                    final meals = _sortedMealItems();
                                                    if (meals.isEmpty) {
                                                      return Center(
                                                        child: Container(
                                                          width: double.infinity,
                                                          child: NoMealsWidget(),
                                                        ),
                                                      );
                                                    }

                                                    return recentMealItems(meals);
                                                  },
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    );
                                  } else {
                                    return loadingIndicator(context);
                                  }
                                },
                              ),
                            ].divide(SizedBox(height: 16.0)),
                          ),
                        ),
                      ].divide(SizedBox(height: 16.0)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container loadingIndicator(BuildContext context) {
    return Container(
      width: double.infinity,
      height: MediaQuery.sizeOf(context).height * 0.3,
      decoration: BoxDecoration(),
      child: wrapWithModel(
        model: _model.circularProgressModel,
        updateCallback: () => safeSetState(() {}),
        child: CircularProgressWidget(),
      ),
    );
  }

  Padding headerDateView() {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
      child: Container(
        height: 70.0,
        decoration: BoxDecoration(),
        child: Builder(
          builder: (context) => Container(
            width: double.infinity,
            height: 100.0,
            child: custom_widgets.CalendarHeaderUI(
              width: double.infinity,
              height: 100.0,
              selectedDate: _model.selectedDate != null ? _model.selectedDate! : getCurrentTimestamp,
              onDateChange: (newSelectedDate) async {
                var _shouldSetState = false;
                if (dateTimeFormat(
                      "MEd",
                      newSelectedDate,
                      locale: isEnglish ? 'en' : 'he',
                    ) !=
                    dateTimeFormat(
                      "MEd",
                      _model.selectedDate,
                      locale: isEnglish ? 'en' : 'he',
                    )) {
                  logFirebaseEvent('CalendarHeaderUI_wait__delay');
                  await Future.delayed(const Duration(milliseconds: 10));
                  logFirebaseEvent('CalendarHeaderUI_alert_dialog');
                  showDialog(
                    context: context,
                    builder: (dialogContext) {
                      _isDialogShow = true;
                      return Dialog(
                        elevation: 0,
                        insetPadding: EdgeInsets.zero,
                        backgroundColor: Colors.transparent,
                        alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                        child: WebViewAware(
                          child: GestureDetector(
                            onTap: () {
                              FocusScope.of(dialogContext).unfocus();
                              FocusManager.instance.primaryFocus?.unfocus();
                            },
                            child: Container(
                              height: 80.0,
                              width: 80.0,
                              child: CircularProgressWidget(),
                            ),
                          ),
                        ),
                      );
                    },
                  ).whenComplete(() => _isDialogShow = false);

                  logFirebaseEvent('CalendarHeaderUI_backend_call');
                  _model.apiResultDashboardData = await DashboardDataCall.call(
                    accessToken: FFAppState().authToken,
                    date: newSelectedDate.toUtc().toIso8601String(),
                    // date: dateTimeFormat(
                    //   "yyyy-MM-dd",
                    //   newSelectedDate,
                    //   locale: isEnglish ? 'en' : 'he',
                    // ),
                  );

                  _shouldSetState = true;
                  logFirebaseEvent('CalendarHeaderUI_wait__delay');
                  await Future.delayed(const Duration(milliseconds: 50));
                  logFirebaseEvent('CalendarHeaderUI_dismiss_dialog');
                  if (_isDialogShow && mounted) {
                    Navigator.pop(context);
                    _isDialogShow = false;
                  }
                  if ((_model.apiResultDashboardData?.succeeded ?? true)) {
                    logFirebaseEvent('CalendarHeaderUI_update_page_state');
                    _model.selectedDate = newSelectedDate;
                    logFirebaseEvent('CalendarHeaderUI_update_page_state');
                    _model.dashboardData = DashboardDataCall.data(
                      (_model.apiResultDashboardData?.jsonBody ?? ''),
                    );
                    safeSetState(() {});
                  }
                }

                if (_shouldSetState) safeSetState(() {});
              },
            ),
          ),
        ),
      ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation1']!),
    );
  }

  Builder caloriesWidget() {
    return Builder(builder: (context) {
      final calories = _model.dashboardData?.calories;

      final caloriesContained = calories?.caloriesContained ?? 0;
      final caloriesRequired = calories?.caloriesRequired ?? 0;
      final isGreaterCalories = caloriesContained >= caloriesRequired;

      return Padding(
        padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
        child: InkWell(
          splashColor: Colors.transparent,
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: _navigateToAdjustGoal,
          child: Material(
            color: Colors.transparent,
            elevation: 3.0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15.0),
            ),
            child: Container(
              width: MediaQuery.sizeOf(context).width * 1.0,
              height: 100.0,
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).secondaryBackground,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 1.0,
                    color: Color(0x46000000),
                    offset: Offset(
                      0.0,
                      0.0,
                    ),
                    spreadRadius: 0.0,
                  )
                ],
                borderRadius: BorderRadius.circular(15.0),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 0.0, horizontal: 16.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Builder(builder: (context) {
                            return Text(
                              valueOrDefault<String>(
                                (caloriesRequired - caloriesContained).toString().replaceAll('-', ''),
                                '0',
                              ),
                              textAlign: TextAlign.end,
                              style: FlutterFlowTheme.of(context).headlineLarge.override(
                                    fontFamily: 'SFHebrew',
                                    color: Colors.black,
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w900,
                                  ),
                            );
                          }),
                          Text(
                            FFLocalizations.of(context).getText(
                              isGreaterCalories
                                  ? 'oq5blxkt' /* Calories Over */
                                  : 'fq5blxkt' /* Calories Left */,
                            ),
                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                  fontFamily: 'SFHebrew',
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  letterSpacing: 0.0,
                                  fontWeight: FontWeight.w800,
                                ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 120.0,
                      height: 90.0,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            width: 100.0,
                            height: 90.0,
                            decoration: BoxDecoration(
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                            ),
                            child: CircularPercentIndicator(
                              percent: valueOrDefault<double>(
                                (String? dValue) {
                                  return (double.parse(dValue ?? "0.1") / 100).toDouble();
                                }(valueOrDefault<String>(
                                  _model.dashboardData?.calories.caloriesPercentage,
                                  '0.0',
                                )),
                                0.0,
                              ).clamp(0.0, 1.0),
                              radius: 35.0,
                              circularStrokeCap: CircularStrokeCap.round,
                              lineWidth: 5.0,
                              animation: true,
                              animateFromLastPercent: true,
                              // progressColor:
                              //     FlutterFlowTheme.of(context).primary,
                              linearGradient: LinearGradient(colors: [
                                Colors.black26,
                                Colors.black,
                              ]),
                              backgroundColor: Color(0xFFD9D9D9),
                              startAngle: 180.0,
                            ),
                          ),
                          Align(
                            alignment: Alignment.center,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 5.0),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.0),
                                    child: SvgPicture.asset(
                                      'assets/images/Group.svg',
                                      width: 16.0,
                                      height: 16.0,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation2']!),
      );
    });
  }

  Widget proteinsWidget(
      BuildContext context, int proteinsRequired, int proteinsContained, String proteinsUnit, bool isGreaterProteins) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: _navigateToAdjustGoal,
      child: Material(
        color: Colors.transparent,
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 0.28,
          height: MediaQuery.sizeOf(context).height * 0.16,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            boxShadow: [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x33000000),
                offset: Offset(
                  0.0,
                  1.0,
                ),
                spreadRadius: 1.0,
              )
            ],
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Padding(
            padding: EdgeInsets.all(10.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 50.0,
                  height: 50.0,
                  child: Stack(
                    children: [
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context).secondaryBackground,
                          ),
                          child: CircularPercentIndicator(
                            percent: valueOrDefault<double>(
                              (String? dValue) {
                                return (double.parse(dValue ?? "0.1") / 100).toDouble();
                              }(valueOrDefault<String>(
                                _model.dashboardData?.proteins.proteinsPercentage,
                                '0.0',
                              )),
                              0.0,
                            ).clamp(0.0, 1.0),
                            radius: 25.0,
                            circularStrokeCap: CircularStrokeCap.round,
                            lineWidth: 5.0,
                            animation: true,
                            animateFromLastPercent: true,
                            // progressColor: Colors.deepOrangeAccent,
                            rotateLinearGradient: true,
                            linearGradient: LinearGradient(colors: [
                              Colors.deepOrangeAccent.shade100,
                              Colors.deepOrangeAccent,
                            ]),
                            backgroundColor: Color(0xFFD9D9D9),
                            startAngle: 180.0,
                          ),
                        ),
                      ),
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/images/Frame.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ].divide(SizedBox(height: 2.0)),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      valueOrDefault<String>(
                            (proteinsRequired - proteinsContained).toString().replaceAll('-', ''),
                            '0',
                          ) +
                          proteinsUnit,
                      style: FlutterFlowTheme.of(context).bodyLarge.override(
                            fontFamily: 'SFHebrew',
                            letterSpacing: 0.0,
                            color: Colors.black,
                          ),
                    ),
                    Text(
                      FFLocalizations.of(context).getText(
                        isGreaterProteins
                            ? 'ogwqrwip' /* Proteins Over */
                            : 'xgwqrwip' /* Proteins Left */,
                      ),
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'SFHebrew',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            letterSpacing: 0.0,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation3']!);
  }

  Widget carbsWidget(
      BuildContext context, int carbsRequired, int carbsContained, String carbsUnit, bool isGreaterCarbs) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: _navigateToAdjustGoal,
      child: Material(
        color: Colors.transparent,
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 0.28,
          height: MediaQuery.sizeOf(context).height * 0.16,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            boxShadow: [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x33000000),
                offset: Offset(
                  0.0,
                  1.0,
                ),
                spreadRadius: 1.0,
              )
            ],
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Padding(
            padding: EdgeInsets.all(10.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 50.0,
                  height: 50.0,
                  child: Stack(
                    children: [
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: FlutterFlowTheme.of(context).secondaryBackground,
                          ),
                          child: CircularPercentIndicator(
                            percent: valueOrDefault<double>(
                              (String? dValue) {
                                return (double.parse(dValue ?? "0.1") / 100).toDouble();
                              }(valueOrDefault<String>(
                                _model.dashboardData?.carbs.carbsPercentage,
                                '0.0',
                              )),
                              0.0,
                            ).clamp(0.0, 1.0),
                            radius: 25.0,
                            circularStrokeCap: CircularStrokeCap.round,
                            lineWidth: 5.0,
                            animation: true,
                            animateFromLastPercent: true,
                            // progressColor: Colors.orange,
                            linearGradient: LinearGradient(colors: [
                              Colors.orange.shade100,
                              Colors.orange,
                            ]),
                            backgroundColor: Color(0xFFD9D9D9),
                            startAngle: 180.0,
                          ),
                        ),
                      ),
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/images/wheat-barley_svgrepo.com.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ].divide(SizedBox(height: 2.0)),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      valueOrDefault<String>(
                            (carbsRequired - carbsContained).toString().replaceAll('-', ''),
                            '0',
                          ) +
                          carbsUnit,
                      style: FlutterFlowTheme.of(context).bodyLarge.override(
                            fontFamily: 'SFHebrew',
                            letterSpacing: 0.0,
                            color: Colors.black,
                          ),
                    ),
                    Text(
                      FFLocalizations.of(context).getText(
                        isGreaterCarbs
                            ? 'o57pxnqr' /* Carbs Over */
                            : '957pxnqr' /* Carbs Left */,
                      ),
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'SFHebrew',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            letterSpacing: 0.0,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation4']!);
  }

  Widget fatsWidget(BuildContext context, int fatsRequired, int fatsContained, String fatsUnit, bool isGreaterFats) {
    return InkWell(
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      hoverColor: Colors.transparent,
      highlightColor: Colors.transparent,
      onTap: _navigateToAdjustGoal,
      child: Material(
        color: Colors.transparent,
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Container(
          width: MediaQuery.sizeOf(context).width * 0.28,
          height: MediaQuery.sizeOf(context).height * 0.16,
          decoration: BoxDecoration(
            color: FlutterFlowTheme.of(context).secondaryBackground,
            boxShadow: [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x33000000),
                offset: Offset(
                  0.0,
                  1.0,
                ),
                spreadRadius: 1.0,
              )
            ],
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Padding(
            padding: EdgeInsets.all(10.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 50.0,
                  height: 50.0,
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).secondaryBackground,
                        ),
                        child: Align(
                          alignment: AlignmentDirectional(0.0, 0.0),
                          child: CircularPercentIndicator(
                            percent: valueOrDefault<double>(
                              (String? dValue) {
                                return (double.parse(dValue ?? "0.1") / 100).toDouble();
                              }(valueOrDefault<String>(
                                _model.dashboardData?.fats.fatsPercentage,
                                '0.0',
                              )),
                              0.0,
                            ).clamp(0.0, 1.0),
                            radius: 25.0,
                            circularStrokeCap: CircularStrokeCap.round,
                            lineWidth: 5.0,
                            animation: true,
                            animateFromLastPercent: true,
                            // progressColor: Colors.blueAccent,
                            linearGradient: LinearGradient(colors: [
                              Colors.blueAccent.shade100,
                              Colors.blueAccent,
                            ]),
                            backgroundColor: Color(0xFFD9D9D9),
                            startAngle: 180.0,
                          ),
                        ),
                      ),
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              'assets/images/drop-invert_svgrepo.com.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ].divide(SizedBox(height: 2.0)),
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      valueOrDefault<String>(
                            (fatsRequired - fatsContained).toString().replaceAll('-', ''),
                            '0',
                          ) +
                          fatsUnit,
                      style: FlutterFlowTheme.of(context).bodyLarge.override(
                            fontFamily: 'SFHebrew',
                            letterSpacing: 0.0,
                            color: Colors.black,
                          ),
                    ),
                    Text(
                      FFLocalizations.of(context).getText(
                        isGreaterFats
                            ? 'ogh5ss9l' /* Fats Over */
                            : 'tgh5ss9l' /* Fats Left */,
                      ),
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'SFHebrew',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            letterSpacing: 0.0,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation5']!);
  }

  List<MealDetailStruct> _sortedMealItems() {
    return (_model.dashboardData?.meals.toList() ?? [])
      ..sort((a, b) {
        final aTime = DateTime.tryParse(a.scannedAt) ?? DateTime(0);
        final bTime = DateTime.tryParse(b.scannedAt) ?? DateTime(0);
        return bTime.compareTo(aTime); // Descending order
      });
  }

  Widget recentMealItems(List<MealDetailStruct> meals) {
    return ListView.separated(
      padding: EdgeInsets.fromLTRB(0, 00.0, 0, 10.0),
      shrinkWrap: true,
      scrollDirection: Axis.vertical,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: meals.length,
      separatorBuilder: (_, __) => SizedBox(height: 10.0),
      itemBuilder: (context, mealsIndex) {
        final mealsItem = meals[mealsIndex];
        bool isLoading = (_model.mealUploadStatus[mealsItem.id.toString()] == true);

        final showImage = mealsItem.type != 'manual';
        return InkWell(
          splashColor: Colors.transparent,
          focusColor: Colors.transparent,
          hoverColor: Colors.transparent,
          highlightColor: Colors.transparent,
          onTap: () async {
            logFirebaseEvent('recent_meal_item_navigate_to');
            await onRecentMealTap(
              mealsItem,
              context,
              mealsIndex,
            );
          },
          child: Stack(
            children: [
              RecentMealItemWidget(
                key: Key('Key4gn_${mealsIndex}_of_${meals.length}'),
                mealData: mealsItem,
                isLoading: isLoading,
                showImage: showImage, // Pass the showImage flag
              ),
              if (isLoading) loadingWrapperWidget(context, isEnglish),
              // if (!isLoading &&
              //     _model.mealRetryAvailable[mealsItem
              //             .id
              //             .toString()] ==
              //         true)
              //   retryWrapperWidget(
              //       mealsItem,
              //       context,
              //       isEnglish),
            ],
          ),
        );
      },
    ).animateOnPageLoad(animationsMap['listViewOnPageLoadAnimation']!);
  }

  Future<void> onRecentMealTap(
    MealDetailStruct mealsItem,
    BuildContext context,
    int mealsIndex,
  ) async {
    if (mealsItem.type == 'manual') {
      logFirebaseEvent('recent_meal_item_backend_call');
      final mealDetailResponse = await MealDetailCall.call(
        accessToken: FFAppState().authToken,
        mealId: mealsItem.id,
      );

      if (mealDetailResponse.succeeded) {
        final mealDetailJson = getJsonField(mealDetailResponse.jsonBody, r'''$.data''') as Map<String, dynamic>?;
        if (mealDetailJson != null) {
          final calories = double.tryParse(mealDetailJson['total_calories']?.toString() ?? '0') ?? 0.0;
          final fats = double.tryParse(mealDetailJson['total_fats']?.toString() ?? '0') ?? 0.0;
          final carbs = double.tryParse(mealDetailJson['total_carbs']?.toString() ?? '0') ?? 0.0;
          final protein = double.tryParse(mealDetailJson['total_proteins']?.toString() ?? '0') ?? 0.0;
          final quantity = (mealDetailJson['quantity'] as num?)?.toDouble() ?? 1.0;
          final servingTypeLabel = mealDetailJson['serving_type']?.toString() ?? 'Serving';

          final items = mealDetailJson['items'] as List<dynamic>? ?? [];
          final servingTypes = <ServingType>[];
          if (items.isNotEmpty) {
            final item = items[0] as Map<String, dynamic>? ?? {};
            // Iterate through all nested serving types (keys "0", "1", "2", etc.)
            item.forEach((key, value) {
              if (value is Map<String, dynamic> && key != 'id' && key != 'is_enable') {
                final servingType = ServingType(
                  id: value['id']?.toString() ?? '',
                  label: value['label']?.toString() ?? '',
                  formalName: value['formalName']?.toString() ?? '',
                  metricUnit: value['metricUnit']?.toString() ?? '',
                  metricAmount: (value['metricAmount'] as num?)?.toDouble() ?? 0.0,
                  numberOfUnits: (value['numberOfUnits'] as num?)?.toDouble() ?? 1.0,
                  calories: (value['calories'] as num?)?.toDouble() ?? 0.0,
                  protein: (value['protein'] as num?)?.toDouble() ?? 0.0,
                  carbs: (value['carbs'] as num?)?.toDouble() ?? 0.0,
                  fat: (value['fat'] as num?)?.toDouble() ?? 0.0,
                  fiber: (value['fiber'] as num?)?.toDouble() ?? 0.0,
                  sugar: (value['sugar'] as num?)?.toDouble() ?? 0.0,
                  sodium: (value['sodium'] as num?)?.toDouble() ?? 0.0,
                  calcium: (value['calcium'] as num?)?.toDouble() ?? 0.0,
                  iron: (value['iron'] as num?)?.toDouble() ?? 0.0,
                  potassium: (value['potassium'] as num?)?.toDouble() ?? 0.0,
                  vitaminA: (value['vitaminA'] as num?)?.toDouble() ?? 0.0,
                  vitaminC: (value['vitaminC'] as num?)?.toDouble() ?? 0.0,
                  cholesterol: (value['cholesterol'] as num?)?.toDouble() ?? 0.0,
                  saturatedFat: (value['saturatedFat'] as num?)?.toDouble() ?? 0.0,
                  monoUnsaturatedFat: (value['monoUnsaturatedFat'] as num?)?.toDouble() ?? 0.0,
                  polyUnsaturatedFat: (value['polyUnsaturatedFat'] as num?)?.toDouble() ?? 0.0,
                );
                if (servingType.id.isNotEmpty) {
                  servingTypes.add(servingType);
                }
              }
            });
          }

          // Add a default serving type if none are found
          if (servingTypes.isEmpty) {
            servingTypes.add(ServingType(
              id: 'default_serving_${mealDetailJson['id']}',
              label: servingTypeLabel,
              formalName: servingTypeLabel,
              metricUnit: 'unit',
              metricAmount: 100.0,
              numberOfUnits: 1.0,
              calories: calories,
              protein: protein,
              carbs: carbs,
              fat: fats,
              fiber: 0.0,
              sugar: 0.0,
              sodium: 0.0,
              calcium: 0.0,
              iron: 0.0,
              potassium: 0.0,
              vitaminA: 0.0,
              vitaminC: 0.0,
              cholesterol: 0.0,
              saturatedFat: 0.0,
              monoUnsaturatedFat: 0.0,
              polyUnsaturatedFat: 0.0,
            ));
          }

          // Find the serving type that matches the serving_type from the API
          final defaultServingType = servingTypes.firstWhere(
            (st) => st.label.toLowerCase() == servingTypeLabel.toLowerCase(),
            orElse: () => servingTypes.isNotEmpty ? servingTypes[0] : ServingType(id: 'default', label: 'Serving'),
          );

          final foodItem = FoodItem(
            id: mealDetailJson['id'] ?? 0,
            enName: mealDetailJson['name']?.toString() ?? '',
            heName: mealDetailJson['name']?.toString() ?? '',
            data: FoodDetails(
              id: mealDetailJson['id']?.toString() ?? '',
              name: mealDetailJson['name']?.toString() ?? '',
              brand: '',
              fats: fats,
              carbs: carbs,
              protein: protein,
              calories: calories,
              servings: quantity,
              servingTypeId: defaultServingType.id,
              servingTypes: servingTypes,
            ),
            created_at: mealDetailJson['scanned_at']?.toString(),
            relevance: null,
          );

          var res = await context.pushNamed(
            'food_nutrition_details',
            queryParameters: {
              'foodItem': serializeParam(
                foodItem.toJson(),
                ParamType.JSON,
              ),
              'mealId': serializeParam(
                mealsItem.id,
                ParamType.int,
              ),
            }.withoutNulls,
          );
          if (res is bool && res) {
            _model.dashboardData?.meals.removeAt(mealsIndex);
            safeSetState(() {});
            await onRefresh(_model.selectedDate ?? DateTime.now());
            safeSetState(() {});
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isEnglish ? 'Failed to load meal details.' : 'נכשל בטעינת פרטי הארוחה.'),
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEnglish ? 'Error fetching meal details.' : 'שגיאה בקבלת פרטי הארוחה.'),
          ),
        );
      }
    } else {
      var res = await context.pushNamed(
        'dish_nutrition_details',
        queryParameters: {
          'mMealItem': serializeParam(
            mealsItem,
            ParamType.DataStruct,
          ),
          'imageUrl': serializeParam(
            mealsItem.image,
            ParamType.String,
          ),
        }.withoutNulls,
      );
      if (res is bool && res) {
        print('is deleted case');
        _model.dashboardData?.meals.removeAt(mealsIndex);
        safeSetState(() {});
        await onRefresh(_model.selectedDate ?? DateTime.now());
        safeSetState(() {});
      } else {
        fetchMealsAgain(context, true, false);
      }
    }
  }

  Dialog buildDialog(
    BuildContext context,
    int freeTrialsLeft,
    bool _shouldSetState,
  ) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      elevation: 16,
      backgroundColor: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Optional: You can replace this with a Lottie animation or SVG
            Icon(
              Icons.lock_open,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),

            // Title
            Text(
              FFLocalizations.of(context).getText('free_trial_title' /* Free Trial */),
              style: FlutterFlowTheme.of(context).titleLarge.override(
                    fontFamily: 'SFHebrew',
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Content message
            Text(
              getFreeTrialMessage(context, freeTrialsLeft),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    fontSize: 16,
                    color: Colors.black87,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.grey.shade400),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                    ),
                    child: Text(
                      FFLocalizations.of(context).getText('5efik18d' /* Cancel */),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'SFHebrew',
                            fontSize: 15,
                            color: Colors.black87,
                          ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      Navigator.pop(context);
                      var imageData = await proceedWithCamera(context);
                      if (imageData != null) {
                        print('0checkData: ${imageData}');
                        await currentUserReference!.update(createUsersRecordData(
                          freeTrialsLeft: (freeTrialsLeft - 1),
                        ));
                        Future.delayed(const Duration(milliseconds: 1500)).then((_) {
                          fetchMealsAgain(context, true, false);
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                    ),
                    child: Text(
                      FFLocalizations.of(context).getText('d9zpqvzf' /* Continue */),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            fontFamily: 'SFHebrew',
                            fontSize: 15,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void needCamera(BuildContext context, bool _shouldSetState) {
    logFirebaseEvent('FloatingActionButton_show_snack_bar');
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Need camera access for scanning Food.',
          style: TextStyle(
            color: FlutterFlowTheme.of(context).error,
          ),
        ),
        duration: Duration(milliseconds: 4000),
        backgroundColor: FlutterFlowTheme.of(context).alternate,
      ),
    );
    if (_shouldSetState) safeSetState(() {});
  }

  Widget loadingWrapperWidget(BuildContext context, bool isEnglish) {
    return Positioned.fill(
      child: Container(
        child: Center(
          child: Dialog(
            elevation: 0,
            insetPadding: EdgeInsets.zero,
            backgroundColor: Colors.transparent,
            alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
            child: WebViewAware(
              child: Container(
                height: 100.0,
                width: 100.0,
                child: CircularProgressWidget(),
              ),
            ),
          ),
        ),
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).primaryText.withOpacity(0.7),
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(
            color: FlutterFlowTheme.of(context).primary.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: FlutterFlowTheme.of(context).primary.withOpacity(0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
      ),
    );
  }

  Widget retryWrapperWidget(MealDetailStruct mealsItem, BuildContext context, bool isEnglish) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(10.0),
            onTap: () {
              safeSetState(() {
                _model.mealUploadStatus[mealsItem.id.toString()] = true;
                _model.mealRetryAvailable[mealsItem.id.toString()] = false;
              });
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).primary.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.refresh_rounded,
                    color: Colors.white,
                    size: 32.0,
                  ),
                ),
                SizedBox(height: 12),
                Text(
                  isEnglish ? 'Tap to retry' : 'הקש כדי לנסות שוב',
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'SFHebrew',
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> fetchMealsAgain(BuildContext context, bool _shouldSetState, [bool _showProgress = true]) async {
    logFirebaseEvent('FloatingActionButton_alert_dialog');
    int mealsCount = _model.dashboardData?.meals.length ?? 0;
    if (_showProgress) {
      showDialog(
        context: context,
        builder: (dialogContext) {
          _isDialogShow = true;
          return Dialog(
            elevation: 0,
            insetPadding: EdgeInsets.zero,
            backgroundColor: Colors.transparent,
            alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
            child: WebViewAware(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(dialogContext).unfocus();
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: Container(
                  height: 100.0,
                  width: 100.0,
                  child: CircularProgressWidget(),
                ),
              ),
            ),
          );
        },
      ).whenComplete(() => _isDialogShow = false);
    }

    logFirebaseEvent('FloatingActionButton_wait__delay');
    await Future.delayed(const Duration(milliseconds: 0));
    logFirebaseEvent('FloatingActionButton_backend_call');
    _model.dashboardApiResult1 = await DashboardDataCall.call(
      accessToken: FFAppState().authToken,
      date: _model.selectedDate?.toUtc().toIso8601String(),
      // date: dateTimeFormat(
      //   "yyyy-MM-dd",
      //   _model.selectedDate,
      //   locale: isEnglish ? 'en' : 'he',
      // ),
    );

    _shouldSetState = true;
    logFirebaseEvent('FloatingActionButton_wait__delay');
    await Future.delayed(const Duration(milliseconds: 50));
    logFirebaseEvent('FloatingActionButton_dismiss_dialog');
    if (_showProgress && _isDialogShow && mounted) {
      Navigator.pop(context);
      _isDialogShow = false;
    }
    if ((_model.dashboardApiResult1?.succeeded ?? true)) {
      logFirebaseEvent('FloatingActionButton_update_page_state');
      _model.dashboardData = DashboardDataCall.data(
        (_model.dashboardApiResult1?.jsonBody ?? ''),
      );
      _model.isInProgress = false;
      // Update the processing status for each meal item
      for (MealDetailStruct meal in _model.dashboardData?.meals ?? []) {
        _model.mealUploadStatus[meal.id.toString()] = (meal.inProgress == 1);
        _model.mealRetryAvailable[meal.id.toString()] = false;
        if (meal.inProgress == 1) {
          Future.delayed(Duration(seconds: 4), () {
            if (_model.mealUploadStatus[meal.id.toString()] == true) {
              safeSetState(() {
                _model.mealRetryAvailable[meal.id.toString()] = true;
              });
            }
          });
        }
      }

      if (mealsCount != (_model.dashboardData?.meals.length ?? 0)) {
        print('testData');
        fetchMealsAgain(context, _shouldSetState, false);
      }
      // if _model.mealUploadStatus list having any true value OR _model.mealRetryAvailable list having any true value
      if (_model.mealUploadStatus.values.any((value) => value == true) ||
          _model.mealRetryAvailable.values.any((value) => value == true)) {
        print('testData');
        Future.delayed(Duration(seconds: 4), () {
          fetchMealsAgain(context, _shouldSetState, false);
        });
      }
      safeSetState(() {});
    } else {
      logFirebaseEvent('FloatingActionButton_update_page_state');
      _model.isInProgress = false;
      safeSetState(() {});
    }

    if (_shouldSetState) safeSetState(() {});
  }

  Future<void> onRefresh(DateTime newSelectedDate) async {
    logFirebaseEvent('CalendarHeaderUI_wait__delay');
    await Future.delayed(const Duration(milliseconds: 10));
    logFirebaseEvent('CalendarHeaderUI_alert_dialog');
    showDialog(
      context: context,
      builder: (dialogContext) {
        _isDialogShow = true;
        return Dialog(
          elevation: 0,
          insetPadding: EdgeInsets.zero,
          backgroundColor: Colors.transparent,
          alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
          child: WebViewAware(
            child: GestureDetector(
              onTap: () {
                FocusScope.of(dialogContext).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: Container(
                height: 80.0,
                width: 80.0,
                child: CircularProgressWidget(),
              ),
            ),
          ),
        );
      },
    ).whenComplete(() => _isDialogShow = false);

    logFirebaseEvent('CalendarHeaderUI_backend_call');
    _model.apiResultDashboardData = await DashboardDataCall.call(
      accessToken: FFAppState().authToken,
      date: newSelectedDate.toUtc().toIso8601String(),
      // date: dateTimeFormat(
      //   "yyyy-MM-dd",
      //   newSelectedDate,
      //   locale: isEnglish ? 'en' : 'he',
      // ),
    );

    logFirebaseEvent('CalendarHeaderUI_wait__delay');
    await Future.delayed(const Duration(milliseconds: 50));
    logFirebaseEvent('CalendarHeaderUI_dismiss_dialog');
    if (_isDialogShow && mounted) {
      Navigator.pop(context);
      _isDialogShow = false;
    }
    if ((_model.apiResultDashboardData?.succeeded ?? true)) {
      logFirebaseEvent('CalendarHeaderUI_update_page_state');
      _model.selectedDate = newSelectedDate;
      logFirebaseEvent('CalendarHeaderUI_update_page_state');
      _model.dashboardData = DashboardDataCall.data(
        (_model.apiResultDashboardData?.jsonBody ?? ''),
      );
      safeSetState(() {});
    }
  }

  Future<dynamic> proceedWithCamera(BuildContext context) async {
    return await context.pushNamed(
      'camera_view',
      extra: <String, dynamic>{
        kTransitionInfoKey: const TransitionInfo(
          hasTransition: true,
          transitionType: PageTransitionType.bottomToTop,
        ),
      },
    );
  }

  String getFreeTrialMessage(BuildContext context, int freeTrialsLeft) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    if (freeTrialsLeft == 0) {
      return isEnglish
          ? 'Your free trial has ended. Subscribe to continue scanning.'
          : 'תקופת הניסיון שלך הסתיימה. הירשם כדי להמשיך לסרוק.';
    } else if (freeTrialsLeft == 1) {
      return isEnglish ? 'You have 1 free scan left. Make it count!' : 'נותרה לך סריקה חינמית אחת. תשתמש בה בחוכמה!';
    } else {
      return isEnglish ? 'You have $freeTrialsLeft free scans left.' : 'נותרו לך $freeTrialsLeft סריקות חינמיות. תהנה!';
    }
  }

  Future<void> _onScanBarcode(BuildContext context) async {
    bool _isValid = await validatePermissionAndSusbscription();
    if (_isValid) {
      await barcodeScanFlow(context);
    }
  }

  Future<bool> validatePermissionAndSusbscription() async {
    var _shouldSetState = false;
    logFirebaseEvent('FloatingActionButton_request_permissions');
    await requestPermission(cameraPermission);
    if (!(await getPermissionStatus(cameraPermission))) {
      needCamera(context, _shouldSetState);
      return false;
    }
    logFirebaseEvent('FloatingActionButton_navigate_to');
    bool _isSubscribed = revenue_cat.isSubscribed() || kDebugMode;
    if (!_isSubscribed) {
      await context.pushNamed(
        'subscription',
        extra: <String, dynamic>{
          kTransitionInfoKey: const TransitionInfo(
            hasTransition: true,
            transitionType: PageTransitionType.bottomToTop,
          ),
        },
      );
      bool __isSubscribed = revenue_cat.isSubscribed() || kDebugMode;
      return __isSubscribed;
    } else {
      return _isSubscribed;
    }
  }

  Future<void> barcodeScanFlow(BuildContext context) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => mobileScannerWidget(context),
      ),
    );
  }

  MobileScannerController mobileScannerController = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    returnImage: true,
    facing: CameraFacing.back,
    useNewCameraSelector: true,
  );

  Widget mobileScannerWidget(BuildContext context) {
    return SizedBox(
      height: double.infinity,
      width: double.infinity,
      child: Stack(
        children: [
          /// Camera Scanner (Base layer)
          MobileScanner(
            controller: mobileScannerController,
            onDetect: (BarcodeCapture capture) async {
              /// The row string scanned barcode value
              final String? scannedValue = capture.barcodes.first.rawValue;
              debugPrint("Barcode scanned: $scannedValue");

              /// List of scanned barcodes if any
              final barcodes = capture.barcodes;
              debugPrint("Barcode list: $barcodes");

              if (capture.barcodes.isEmpty) {
                return;
              }

              if (capture.barcodes.first.rawValue!.trim().isEmpty) {
                return;
              }

              final rawValue = capture.barcodes.first.rawValue!.trim();
              final isNumeric = RegExp(r'^[0-9]+$').hasMatch(rawValue);
              if (!isNumeric) {
                return;
              }

              /// The `Uint8List` image is only available if `returnImage` is set to `true`.
              final Uint8List? image = capture.image;
              debugPrint("Barcode image: $image");

              await handleBarcodeResponse(rawValue, image, context);
            },
          ),

          /// Scanner Overlay Frame
          Container(
            padding: EdgeInsets.only(
              bottom: FFAppState().bottomPadding + 70,
            ),
            child: Align(
              child: SvgPicture.asset(
                'assets/images/radius_scanner.svg',
                width: MediaQuery.sizeOf(context).width * 0.85,
                height: MediaQuery.sizeOf(context).height * 0.47,
                fit: BoxFit.contain,
                alignment: Alignment.center,
              ),
              alignment: Alignment.center,
            ),
          ),

          /// Animated Scanner Overlay
          Align(
            alignment: AlignmentDirectional(0.0, 0.0),
            child: Container(
              padding: EdgeInsets.only(
                bottom: FFAppState().bottomPadding + 70,
              ),
              child: Lottie.asset(
                'assets/jsons/animated_scanner.json',
                width: MediaQuery.sizeOf(context).width * 0.6,
                height: MediaQuery.sizeOf(context).height * 0.6,
                fit: BoxFit.contain,
                animate: true,
              ),
            ),
          ),

          Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    logFirebaseEvent('IconButton_navigate_back');
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.fromLTRB(20, 30, 20, 0),
                    width: 45,
                    height: 45.0,
                    decoration: BoxDecoration(color: Colors.white.withOpacity(0.5), shape: BoxShape.circle),
                    child: Icon(
                      Icons.clear_rounded,
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      size: 30.0,
                    ),
                  ),
                ),
                Spacer(),

                /// Flash Toggle Button
                Builder(builder: (context) {
                  return Material(
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      margin: EdgeInsets.fromLTRB(20, 30, 20, 0),
                      child: IconButton(
                        onPressed: () async {
                          await mobileScannerController.toggleTorch();
                        },
                        icon: Icon(
                          mobileScannerController.torchEnabled ? Icons.flash_off : Icons.flash_on,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),

          /// Bottom Action Buttons
          // Material(
          //   color: Colors.transparent,
          //   child: Container(
          //     padding: EdgeInsets.only(bottom: FFAppState().bottomPadding + 16),
          //     child: Align(
          //       alignment: Alignment.bottomCenter,
          //       child: Row(
          //         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          //         children: [
          //           /// Gallery/Image Picker Button
          //           Container(
          //             decoration: BoxDecoration(
          //               color: Colors.black.withOpacity(0.7),
          //               borderRadius: BorderRadius.circular(30),
          //             ),
          //             child: IconButton(
          //               onPressed: () async {
          //                 /// Implement image picker from gallery
          //                 await _filePicker(context);
          //               },
          //               icon: Icon(
          //                 Icons.photo_library,
          //                 color: Colors.white,
          //                 size: 28,
          //               ),
          //               iconSize: 50,
          //             ),
          //           ),
          //
          //           /// Manual Entry Button (optional)
          //           Container(
          //             decoration: BoxDecoration(
          //               color: Colors.black.withOpacity(0.7),
          //               borderRadius: BorderRadius.circular(30),
          //             ),
          //             child: IconButton(
          //               onPressed: () {
          //                 /// Implement manual barcode entry
          //                 _showManualEntryDialog(context);
          //               },
          //               icon: Icon(
          //                 Icons.keyboard,
          //                 color: Colors.white,
          //                 size: 28,
          //               ),
          //               iconSize: 50,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Future<void> _filePicker(BuildContext context) async {
    bool hasFileAccess = await _checkStoragePermission();
    if (!hasFileAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'No file access permission. Please enable file access permission in settings.'
              : 'אין הרשאה לגישה לקבצים. אנא אפשר הרשאה לגישה לקבצים בהגדרות.'),
          action: SnackBarAction(
            label: isEnglish ? 'Settings' : 'הגדרות',
            onPressed: () {
              openAppSettings();
            },
          ),
        ),
      );
      return;
    }
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery, imageQuality: 90);
    if (pickedFile != null) {
      var _filePickerFile = pickedFile;
      var selectedFile = await _createSelectedFile(_filePickerFile);
      Uint8List? img = await _filePickerFile.readAsBytes();

      final BarcodeScanner barcodeScanner = BarcodeScanner();
      final InputImage inputImage = InputImage.fromBytes(
        bytes: await _filePickerFile.readAsBytes(),
        metadata: InputImageMetadata(
          size: Size(
            selectedFile.dimensions!['width']?.toDouble() ?? 0,
            selectedFile.dimensions!['height']?.toDouble() ?? 0,
          ),
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.nv21,
          bytesPerRow: selectedFile.dimensions!['width']?.toInt() ?? 0,
        ),
      );
      final barcodes = await barcodeScanner.processImage(inputImage);

      if (barcodes.isEmpty) {
        return;
      }

      if (barcodes.first.rawValue!.trim().isEmpty) {
        return;
      }

      final rawValue = barcodes.first.rawValue!.trim();
      final isNumeric = RegExp(r'^[0-9]+$').hasMatch(rawValue);
      if (!isNumeric) {
        return;
      }

      await handleBarcodeResponse(barcodes.first.rawValue!, img, context);
      WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
    }
  }

  Future<bool> _checkStoragePermission() async {
    PermissionStatus status;
    if (Platform.isAndroid) {
      final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      final AndroidDeviceInfo info = await deviceInfoPlugin.androidInfo;
      if ((info.version.sdkInt) >= 33) {
        status = PermissionStatus.granted;
      } else {
        status = await Permission.storage.request();
      }
    } else {
      status = await Permission.storage.request();
    }

    switch (status) {
      case PermissionStatus.denied:
        return false;
      case PermissionStatus.granted:
        return true;
      case PermissionStatus.restricted:
        return false;
      case PermissionStatus.limited:
        return true;
      case PermissionStatus.permanentlyDenied:
        return false;
      case PermissionStatus.provisional:
        return true;
    }
  }

  Future<void> requestStoragePermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
    ].request();

    final String info = statuses[Permission.storage].toString();
    print(info);
  }

  Future<String> _getStoragePath(String filePath) async {
    Directory appCacheDir = await getApplicationCacheDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final ext = filePath.split('.').last;
    final String filePathWithTimestamp = '${appCacheDir.path}/$timestamp.$ext';
    return filePathWithTimestamp;
  }

  Future<MediaDimensions> _getImageDimensions(Uint8List mediaBytes) async {
    final image = await decodeImageFromList(mediaBytes);
    return MediaDimensions(
      width: image.width.toDouble(),
      height: image.height.toDouble(),
    );
  }

  Future<Uint8List> _compressImage(XFile xFile) async {
    final Uint8List mediaBytes = await xFile.readAsBytes();
    final result = await FlutterImageCompress.compressWithList(
      mediaBytes,
      minWidth: 800,
      minHeight: 800,
      quality: 85,
    );
    return result;
  }

  Future<SelectedFile> _createSelectedFile(XFile xFile) async {
    await requestStoragePermissions();
    final String path = await _getStoragePath(xFile.name);
    final String ext = path.split('.').last;

    final Uint8List mediaBytes = await xFile.readAsBytes();
    final Uint8List compressedBytes = await _compressImage(xFile);

    print('mediaBytes length: ${mediaBytes.length} compressedBytes length: ${compressedBytes.length}');
    final File compressedFile = File(path);
    try {
      await compressedFile.writeAsBytes(compressedBytes);
    } catch (e) {
      print('Error writing file: $e');
      throw (e);
    }

    final MediaDimensions dimensions = await _getImageDimensions(compressedBytes);
    return SelectedFile(
      storagePath: path,
      filePath: compressedFile.path,
      bytes: compressedBytes,
      dimensions: dimensions.toJson(),
      ext: ext,
    );
  }

  Future<void> handleBarcodeResponse(
    String rawValue,
    Uint8List? image,
    BuildContext context,
  ) async {
    if (_processedBarcode) {
      return;
    }
    try {
      await Future.delayed(Duration(milliseconds: 100));
      context.pop();
      _processedBarcode = true;
      // if (image == null) {
      //   return;
      // }
      File? file;
      if (image != null) {
        Directory appCacheDir = await getApplicationCacheDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final ext = 'png';
        final String filePathWithTimestamp = '${appCacheDir.path}/$timestamp.$ext';
        file = File(filePathWithTimestamp);
        await file.writeAsBytes(image);
      }
      await Future.delayed(Duration(milliseconds: 100));
      showDialog(
        context: context,
        builder: (dialogContext) {
          _isDialogShow = true;
          return Dialog(
            elevation: 0,
            insetPadding: EdgeInsets.zero,
            backgroundColor: Colors.transparent,
            alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
            child: WebViewAware(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(dialogContext).unfocus();
                  FocusManager.instance.primaryFocus?.unfocus();
                },
                child: Container(
                  height: 80.0,
                  width: 80.0,
                  child: CircularProgressWidget(),
                ),
              ),
            ),
          );
        },
      ).whenComplete(() => _isDialogShow = false);
      // api call for barcode add
      bool res = await _model.uploadWithRetry(
        context: context,
        imagePath: file?.path,
        token: FFAppState().authToken,
        isEnglish: isEnglish,
        barcodeNumber: rawValue,
      );
      _processedBarcode = false;
      if (_isDialogShow && mounted) {
        Navigator.pop(context);
      }

      if (res) {
        fetchMealsAgain(context, true, false);
      }
    } catch (e) {
      _processedBarcode = false;
      print(e);
    }
  }

  // Helper function for manual entry dialog
  void _showManualEntryDialog(BuildContext context) {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isEnglish ? 'Enter Barcode Manually' : 'הזן ברקוד ידנית'),
          content: TextField(
            controller: controller,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: isEnglish ? 'Enter barcode number' : 'הזן מספר ברקוד',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(FFLocalizations.of(context).getText(
                '5efik18d' /* Cancel */,
              )),
            ),
            TextButton(
              onPressed: () async {
                final value = controller.text.trim();
                if (value.isNotEmpty && RegExp(r'^[0-9]+$').hasMatch(value)) {
                  Navigator.of(context).pop();
                  await handleBarcodeResponse(value, null, context);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text(
                      isEnglish ? 'Please enter a valid numeric barcode' : 'אנא הזן ברקוד מספרי חוקי',
                    )),
                  );
                }
              },
              child: Text(isEnglish ? 'Submit' : 'שלח'),
            ),
          ],
        );
      },
    );
  }
}

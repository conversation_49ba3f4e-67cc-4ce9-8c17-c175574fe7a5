// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class DailyLogStruct extends FFFirebaseStruct {
  DailyLogStruct({
    String? logDate,
    int? totalWeight,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _logDate = logDate,
        _totalWeight = totalWeight,
        super(firestoreUtilData);

  // "log_date" field.
  String? _logDate;
  String get logDate => _logDate ?? '';
  set logDate(String? val) => _logDate = val;

  bool haslogDate() => _logDate != null;

  // "total_weight" field.
  int? _totalWeight;
  int get totalWeight => _totalWeight ?? 0;
  set totalWeight(int? val) => _totalWeight = val;

  void incrementtotalWeight(int amount) =>
      totalWeight = totalWeight + amount;

  bool hastotalWeight() => _totalWeight != null;

  static DailyLogStruct fromMap(Map<String, dynamic> data) => DailyLogStruct(
        logDate: castToType<String?>(data['log_date']),
        totalWeight: castToType<int>(data['total_weight']),
      );

  static DailyLogStruct? maybeFromMap(dynamic data) =>
      data is Map ? DailyLogStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'log_date': _logDate,
        'total_weight': _totalWeight,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'log_date': serializeParam(
          _logDate,
          ParamType.String,
        ),
        'total_weight': serializeParam(
          _totalWeight,
          ParamType.int,
        ),
      }.withoutNulls;

  static DailyLogStruct fromSerializableMap(Map<String, dynamic> data) =>
      DailyLogStruct(
        logDate: deserializeParam(
          data['log_date'],
          ParamType.String,
          false,
        ),
        totalWeight: deserializeParam(
          data['total_weight'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'DailyLogStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is DailyLogStruct &&
        logDate == other.logDate &&
        totalWeight == other.totalWeight;
  }

  @override
  int get hashCode =>
      const ListEquality().hash([logDate, totalWeight]);
}

DailyLogStruct createDailyLogStruct({
  String? logDate,
  int? totalWeight,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    DailyLogStruct(
      logDate: logDate,
      totalWeight: totalWeight,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

DailyLogStruct? updateDailyLogStruct(
  DailyLogStruct? goals, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    goals
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addDailyLogStructData(
  Map<String, dynamic> firestoreData,
  DailyLogStruct? goals,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (goals == null) {
    return;
  }
  if (goals.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && goals.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final goalsData = getGoalsFirestoreData(goals, forFieldValue);
  final nestedData = goalsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = goals.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getGoalsFirestoreData(
  DailyLogStruct? goals, [
  bool forFieldValue = false,
]) {
  if (goals == null) {
    return {};
  }
  final firestoreData = mapToFirestore(goals.toMap());

  // Add any Firestore field values
  goals.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getGoalsListFirestoreData(
  List<DailyLogStruct>? goalss,
) =>
    goalss?.map((e) => getGoalsFirestoreData(e, true)).toList() ?? [];

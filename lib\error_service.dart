import 'dart:convert';
import 'dart:io';

import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:bugsnag_flutter/bugsnag_flutter.dart';
import 'package:path/path.dart' as path;

class ErrorService {
  static void setupUser(String userId, String email, String name) {
    // Set user for both services
    Posthog().identify(
      userId: userId,
      userProperties: {
        'email': email,
        'name': name,
      },
    );

    bugsnag.setUser(
      id: userId,
      email: email,
      name: name,
    );
  }

  // Handle type casting errors
  static T? safeCast<T>(dynamic value, {String? context}) {
    try {
      if (value == null) return null;
      return value as T;
    } catch (e, stackTrace) {
      final errorDetails = {
        'expected_type': T.toString(),
        'actual_type': value.runtimeType.toString(),
        'value': value.toString(),
        'context': context ?? 'unknown',
      };

      // Track in PostHog for analytics
      Posthog().capture(
        eventName: 'type_casting_error',
        properties: errorDetails,
      );

      // Report to Bugsnag for critical tracking
      bugsnag.notify(e, stackTrace);

      return null;
    }
  }

  // Handle API failures (for http package)
  static void reportApiError({
    required Exception error,
    String? endpoint,
    String? method,
    int? statusCode,
    String? responseBody,
    Map<String, dynamic>? requestData,
    StackTrace? stackTrace,
  }) {
    final errorData = <String, Object>{
      'endpoint': endpoint ?? 'unknown',
      'status_code': statusCode ?? 0,
      'method': method ?? 'GET',
      'error_message': error.toString(),
      'response_body': responseBody ?? '',
      'request_data': requestData ?? '',
    };

    // Track API failures in PostHog
    Posthog().capture(
      eventName: 'api_failure',
      properties: errorData,
    );

    // Report critical API errors to Bugsnag
    if (statusCode == null || statusCode >= 500) {
      bugsnag.notify(error, stackTrace);
    }
  }

  // Handle ANR (Application Not Responding)
  static void reportANR(String operation, Duration duration) {
    final anrData = {
      'operation': operation,
      'duration_ms': duration.inMilliseconds,
      'threshold_exceeded': duration.inMilliseconds > 3000,
    };

    // Track ANR in PostHog
    Posthog().capture(
      eventName: 'anr_detected',
      properties: anrData,
    );

    // Report severe ANR to Bugsnag (>10 seconds)
    if (duration.inMilliseconds > 3000) {
      bugsnag.leaveBreadcrumb(
        'ANR detected: $operation took ${duration.inMilliseconds}ms',
        metadata: anrData,
      );
    }
  }

  // User feedback and satisfaction tracking
  static void trackUserFeedback({
    required String message,
    String? category,
    int? rating,
    Map<String, dynamic>? additionalData,
  }) {
    // Track feedback in PostHog
    Posthog().capture(
      eventName: 'user_feedback',
      properties: {
        'message': message,
        'category': category ?? 'general',
        'rating': rating ?? 5,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      },
    );

    // Leave breadcrumb in Bugsnag for context
    bugsnag.leaveBreadcrumb(
      'User feedback: ${category ?? 'general'}',
      metadata: {
        'rating': rating ?? 5,
        'has_message': message.isNotEmpty,
      },
    );
  }

  // Track user satisfaction metrics
  static void trackUserSatisfaction({
    required String feature,
    required int satisfactionScore, // 1-5
    String? comments,
  }) {
    Posthog().capture(
      eventName: 'user_satisfaction',
      properties: {
        'feature': feature,
        'satisfaction_score': satisfactionScore,
        'comments': comments ?? '',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // Track user journey and experience
  static void trackUserAction(
    String action, {
    Map<String, dynamic>? properties,
  }) {
    Posthog().capture(
      eventName: action,
      properties: properties?.cast<String, Object>() ?? {},
    );
  }

  static Future<bool> reportToBugsnag(
    String email,
    String description,
    File? media, {
    String? actualResults,
    String? expectedResults,
  }) async {
    try {
      // Create a Bugsnag event with user details and description
      await bugsnag.notify(
        Exception('User Reported Bug'),
        StackTrace.current,
        callback: (event) {
          event.setUser(email: email);
          event.addMetadata('bug_details', {
            'description': description,
            'actual_results': actualResults ?? '',
            'expected_results': expectedResults ?? '',
          });

          // If there's a media file, attach it
          if (media != null) {
            final bytes = media.readAsBytesSync();
            if (bytes.length > 1 * 1024 * 1024) {
              /// Limit to 1MB
              print('Media file too large: ${bytes.length} bytes');
              return false;
            }
            final base64Media = base64Encode(bytes);
            event.addMetadata('attachment', {
              'file_name': path.basename(media.path),
              'file_content': base64Media,
            });
          }
          return true;
        },
      );
      return true;
    } catch (e) {
      print('Failed to report to Bugsnag: $e');
      return false;
    }
  }

  static Future<bool> reportToPostHog(
    String email,
    String description,
    File? media, {
    String? actualResults,
    String? expectedResults,
  }) async {
    try {
      /// Identify the user in PostHog
      await Posthog().identify(
        userId: email,
        userProperties: {'email': email},
      );

      // Capture the bug report event
      final properties = {
        'description': description,
        'actual_results': actualResults ?? '',
        'expected_results': expectedResults ?? '',
        'timestamp': DateTime.now().toIso8601String(),
      };

      // If there's a media file, add its path or content
      if (media != null) {
        properties['media_path'] = media.path;
      }

      await Posthog().capture(
        eventName: 'user_reported_bug',
        properties: properties,
      );
      return true;
    } catch (e) {
      print('Failed to report to PostHog: $e');
      return false;
    }
  }

  static Future<bool> reportUserSuggestion(
      String email,
      String suggestion, {
        String? category,
        Map<String, dynamic>? additionalData,
      }) async {
    try {
      await bugsnag.notify(
        Exception('User Suggestion'),
        StackTrace.current,
        callback: (event) {
          event.setUser(email: email);
          event.addMetadata('suggestion_details', {
            'suggestion': suggestion,
            'category': category ?? 'general',
            'timestamp': DateTime.now().toIso8601String(),
            ...?additionalData,
          });
          event.severity = BugsnagSeverity.error;
          return true;
        },
      );
      return true;
    } catch (e, stackTrace) {
      print('Failed to report suggestion to Bugsnag: $e\nStackTrace: $stackTrace');
      return false;
    }
  }
}

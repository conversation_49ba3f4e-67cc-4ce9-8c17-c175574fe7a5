import 'dart:io';

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/revenue_cat_util.dart'
    as revenue_cat;
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({Key? key}) : super(key: key);

  @override
  _SubscriptionScreenState createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  Offerings? offerings;
  Package? selectedPackage;
  CustomerInfo? purchaserInfo;

  @override
  void initState() {
    if (revenue_cat.offerings == null || revenue_cat.customerInfo == null) {
      return;
    }
    offerings = revenue_cat.offerings!;
    purchaserInfo = revenue_cat.customerInfo!;
    super.initState();
    isUserSubscribed();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    if ((revenue_cat.offerings?.current?.availablePackages ?? []).isNotEmpty) {
      if (selectedPackage == null) {
        selectedPackage = offerings!.current?.monthly ??
            offerings!.current!.availablePackages.first;
      }
    }
    return MeasuredWidget(
        name: 'Subscription',
        builder: (context) => Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () {
                          context.pop();
                        },
                        icon: Icon(Icons.clear_outlined),
                      ),
                      Spacer(),
                      ElevatedButton(
                        onPressed: () {
                          revenue_cat.restorePurchases();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          animationDuration: Duration(milliseconds: 500),
                          visualDensity: VisualDensity.adaptivePlatformDensity,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                          elevation: 3,
                          padding:
                              EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                        ),
                        child: Text(
                          FFLocalizations.of(context).getText(
                            'wgtiby8u' /* Restore */,
                          ),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontFamily: 'SFHebrew',
                          ),
                        ),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 20),
                          // Main title
                          Text(
                            isEnglish ? 'Choose Your Plan' : 'בחר את המסלול שלך',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                              fontFamily: 'SFHebrew',
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8),
                          // Rating section
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: List.generate(
                                    5,
                                    (index) => Icon(
                                          Icons.star,
                                          color: Colors.amber,
                                          size: 16,
                                        )),
                              ),
                              SizedBox(width: 8),
                              Text(
                                isEnglish ? '4.9 stars (1000 reviews)' :'4.9 כוכבים (1000 ביקורות)',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontFamily: 'SFHebrew',
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 30),
                          // Features section
                          _buildFeature('Smart Food Scanning', 'סריקת מזון חכמה'),
                          _buildFeature('Personalized Goal', 'יעד מותאם אישית'),
                          _buildFeature('Clear Progress Tracking', 'מעקב ברור אחר ההתקדמות שלך'),
                          SizedBox(height: 30),
                          // Subscription plans
                          _buildPlanCards(context),
                          SizedBox(height: 30),
                          // Subscribe button
                          Container(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () async {
                                if (selectedPackage != null) {
                                  bool success = await revenue_cat
                                      .purchasePackage(selectedPackage!.identifier);
                                  if (success) {
                                    // Update purchaserInfo
                                    if (Platform.isAndroid) {
                                      await Purchases.syncPurchases();
                                    }
                                    await revenue_cat.loadCustomerInfo();
                                    purchaserInfo = await revenue_cat.customerInfo;
                                    // Trigger UI update
                                    safeSetState(() {});
                                    bool __isSubscribed =
                                        revenue_cat.isSubscribed() || kDebugMode;
                                    if (__isSubscribed) {
                                      context.pop();
                                      return;
                                    }
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                padding: EdgeInsets.symmetric(
                                    vertical: 18, horizontal: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                animationDuration: Duration(milliseconds: 500),
                                visualDensity:
                                    VisualDensity.adaptivePlatformDensity,
                                elevation: 3,
                                shadowColor: Colors.black.withOpacity(0.3),
                              ),
                              child: Text(
                                isEnglish ? 'Start My Journey' :'התחל את המסע שלי',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'SFHebrew',
                                ),
                              ),
                            ),
                          ),
                          // Visibility(
                          //   visible: false,
                          //   child: Center(
                          //     child: TextButton(
                          //       onPressed: () async {
                          //         // Navigate to manage subscription on play store using link or deep link
                          //         await navigateToManageSubscriptionOnPlayStore();
                          //       },
                          //       style: TextButton.styleFrom(
                          //         padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                          //         animationDuration: Duration(milliseconds: 500),
                          //         visualDensity: VisualDensity.adaptivePlatformDensity,
                          //       ),
                          //       child: Text(
                          //         FFLocalizations.of(context).getText('wgtil88u'),
                          //         style: TextStyle(color: Colors.grey, fontSize: 14),
                          //       ),
                          //     ),
                          //   ),
                          // ),
                          // Add footer with links - Required by Apple
                          _buildFooterLinks(isEnglish),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
    );
  }

  // Updated feature builder for simple checkmark design
  Widget _buildFeature(String titleEn, String titleHe) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            Icons.check,
            color: Colors.black,
            size: 20,
          ),
          SizedBox(width: 12),
          Text(
            isEnglish ? titleEn : titleHe,
            style: TextStyle(
              fontSize: 16,
              color: Colors.black,
              fontFamily: 'SFHebrew',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCards(BuildContext context) {
    print('offerings!.current! ${offerings}');
    if (offerings?.current == null) {
      return Center(child: Text('No subscription plans available.'));
    }
    if (offerings?.current?.availablePackages == null ||
        offerings!.current!.availablePackages.isEmpty) {
      return Container();
    }
    if (selectedPackage == null) {
      selectedPackage = offerings!.current?.monthly ??
          offerings!.current!.availablePackages.first;
    }

    return Column(
      children: offerings!.current!.availablePackages.map((Package package) {
        bool isYearly = package.identifier.contains('year') ||
            package.identifier.contains('annual') == true;
        return _buildPlanCard(
          Key(package.identifier),
          context,
          package: package,
          onTap: () {
            selectedPackage = package;
            safeSetState(() {});
          },
          isYearly: isYearly,
        );
      }).toList(),
    );
  }

  String getTrialPeriod1(bool isYearly) {
    try {
      final trialDay = selectedPackage
              ?.storeProduct.introductoryPrice?.periodNumberOfUnits ??
          3;
      final PeriodUnit? unit =
          selectedPackage?.storeProduct.introductoryPrice?.periodUnit;

      final _unit = unit == PeriodUnit.day
          ? FFLocalizations.of(context).getText('wgtiod9u')
          : unit == PeriodUnit.week
              ? FFLocalizations.of(context).getText('wgtioe0u')
              : unit == PeriodUnit.month
                  ? FFLocalizations.of(context).getText('wgtiof1u')
                  : FFLocalizations.of(context).getText('wgtiod9u');
      return '$trialDay $_unit';
    } catch (e) {
      return FFLocalizations.of(context).getText('wgtiog2u');
    }
  }

  Widget _buildPlanCard(
    Key _Key,
    BuildContext context,{
    VoidCallback? onTap,
    required Package package,
    required bool isYearly,
  }) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    // print('printData active ${purchaserInfo?.entitlements.active.toString()}');
    // print('printData active1 ${purchaserInfo?.entitlements.active.values.toList().toString()}');
    // print('printData active2 ${package.identifier}');
    String title = getPlanTitle(package);
    String price = package.storeProduct.priceString;
    bool isSelected = selectedPackage?.identifier == package.identifier;
    bool isPurchased = false;
    if ((purchaserInfo?.entitlements.active ?? {}).isEmpty) {
      isPurchased = false;
    } else {
      if (Platform.isAndroid) {
        isPurchased = purchaserInfo!.entitlements.active.values.any((entitlement) =>
            '${entitlement.productIdentifier}:${entitlement.productPlanIdentifier}' ==
            package.storeProduct.identifier);
      } else {
        isPurchased =
            purchaserInfo!.entitlements.active.values.any((entitlement) {
          print(
              'entitlementData ${entitlement.productIdentifier} ${entitlement.productPlanIdentifier}');
          return (entitlement.productPlanIdentifier ==
                  package.storeProduct.identifier ||
              entitlement.productIdentifier == package.storeProduct.identifier);
        });
      }
    }

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(vertical: 6),
      child: GestureDetector(
        onTap: onTap,
        key: _Key,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isPurchased
                    ? Colors.green.shade100
                    : isSelected
                        ? Colors.green.shade50
                        : Colors.white,
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: isPurchased
                      ? Colors.green
                      : isSelected
                          ? Colors.green
                          : Colors.black,
                  width: isSelected ? 2 : 2,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isYearly
                              ? (isEnglish ? 'Annual Plan' : 'מנוי שנתי')
                              : (isEnglish ? 'Monthly Plan' : 'מנוי חודשי'),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily: 'SFHebrew',
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          _getPriceText(package, isYearly, isEnglish),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                            fontFamily: 'SFHebrew',
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    _getPerMonthPriceText(package, isYearly, isEnglish),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                      fontFamily: 'SFHebrew',
                    ),
                  ),
                ],
              ),
            ),
            if (isYearly) ...[
              Positioned(
                left: 40,
                top: -10,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isEnglish ? '7 days free trial' : '7 ימי נסיון חינם',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'SFHebrew',
                    ),
                  ),
                ),
              ),
            ],
            if (isYearly)
              Positioned(
                right: 30,
                bottom: -8,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isEnglish ? '58% OFF' : '58% הנחה',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'SFHebrew',
                    ),
                  ),
                ),
              ),
            if (isPurchased)
              Positioned(
                bottom: -4,
                left: 28,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    FFLocalizations.of(context).getText('wgtin08u'),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontFamily: 'SFHebrew',
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Add footer links method (keeping existing implementation)
  Widget _buildFooterLinks(bool isEnglish) {
    return Column(
      children: [
        Divider(),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: () async {
                // await launchUrl(
                //   Uri.parse(appPrivacy),
                //   mode: LaunchMode.inAppWebView,
                // );
                context.pushNamed(
                  'teams_privacy',
                  queryParameters: {
                    'privacy': serializeParam(
                      true,
                      ParamType.bool,
                    ),
                    'url': serializeParam(
                      appPrivacy,
                      ParamType.String,
                    ),
                  }.withoutNulls,
                );
              },
              child: Text(
                isEnglish ? 'Privacy Policy' : 'מדיניות פרטיות',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontFamily: 'SFHebrew',
                ),
              ),
            ),
            SizedBox(width: 20),
            TextButton(
              onPressed: () async {
                // await launchUrl(
                //   Uri.parse(appTerms),
                //   mode: LaunchMode.inAppWebView,
                // );
                context.pushNamed(
                  'teams_privacy',
                  queryParameters: {
                    'privacy': serializeParam(
                      false,
                      ParamType.bool,
                    ),
                    'url': serializeParam(
                      appTerms,
                      ParamType.String,
                    ),
                  }.withoutNulls,
                );
              },
              child: Text(
                isEnglish ? 'Terms of Use (EULA)' : 'תנאי שימוש (EULA)',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontFamily: 'SFHebrew',
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
      ],
    );
  }

  String getPerMonthPriceString() =>
      FFLocalizations.of(context).getText('wgtioh3u').replaceAll(
          '{{price}}', (selectedPackage?.storeProduct.priceString ?? ''));

  String getTrialPeriod(bool isYearly) {
    try {
      final trialDay = selectedPackage
              ?.storeProduct.introductoryPrice?.periodNumberOfUnits ??
          3;
      final PeriodUnit? unit =
          selectedPackage?.storeProduct.introductoryPrice?.periodUnit;

      final _unit = unit == PeriodUnit.day
          ? FFLocalizations.of(context).getText('wgtiod9u')
          : unit == PeriodUnit.week
              ? FFLocalizations.of(context).getText('wgtioe0u')
              : unit == PeriodUnit.month
                  ? FFLocalizations.of(context).getText('wgtiof1u')
                  : FFLocalizations.of(context).getText('wgtiod9u');
      return '$trialDay $_unit';
    } catch (e) {
      return FFLocalizations.of(context).getText('wgtiog2u');
    }
  }

  String getPlanTitle(Package package) {
    if (package.identifier.contains('month')) {
      return FFLocalizations.of(context).getText('wgtioi4u');
    } else if (package.identifier.contains('year') ||
        package.identifier.contains('annual')) {
      return FFLocalizations.of(context).getText('wgtioj5u');
    } else {
      return package.storeProduct.title;
    }
  }

  Future<void> navigateToManageSubscriptionOnPlayStore() async {
    const String appStoreNav =
        'itms-apps://apps.apple.com/account/subscriptions';
    const String playStoreNav =
        'https://play.google.com/store/account/subscriptions';
    String url = Platform.isAndroid ? playStoreNav : appStoreNav;

    bool _ = await canLaunchUrl(Uri.parse(url));
    if (_) {
      await launchUrl(
        Uri(path: url),
        mode: LaunchMode.inAppWebView,
      );
    } else {
      print("Don't know how to open this URL: $url");
    }
  }

  /// check if the user has an active subscription
  Future<bool> isUserSubscribed() async {
    if (Platform.isAndroid) {
      await Purchases.syncPurchases();
    }
    CustomerInfo customerInfo = await Purchases.getCustomerInfo();
    return (customerInfo.entitlements.active['premium_plans']?.isActive ??
            false) &&
        customerInfo.activeSubscriptions.isNotEmpty;
  }

  String _getPriceText(Package package, bool isYearly, bool isEnglish) {
    String priceString = package.storeProduct.priceString;
    if (isEnglish) {
      return isYearly ? '$priceString per year' : '$priceString per year';
    } else {
      return isYearly ? '$priceString לשנה' : '$priceString לשנה';
    }
  }

  String _getPerMonthPriceText(Package package, bool isYearly, bool isEnglish) {
    double price = package.storeProduct.price;
    double monthlyPrice = isYearly ? price / 12 : price;

    String formattedPrice = formatPrice(monthlyPrice, package.storeProduct.currencyCode);

    if (isEnglish) {
      return '${formattedPrice} per month';
    } else {
      return '$formattedPrice לחודש';
    }
  }
  String formatPrice(double price, String currencyCode) {
    NumberFormat format = NumberFormat.simpleCurrency(name: currencyCode);
    return format.format(price);
  }
}

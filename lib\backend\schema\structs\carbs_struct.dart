// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class CarbsStruct extends FFFirebaseStruct {
  CarbsStruct({
    String? carbsRequired,
    String? carbsContained,
    String? carbsPercentage,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _carbsRequired = carbsRequired,
        _carbsContained = carbsContained,
        _carbsPercentage = carbsPercentage,
        super(firestoreUtilData);

  // "carbs_required" field.
  String? _carbsRequired;
  String get carbsRequired => _carbsRequired ?? '';
  set carbsRequired(String? val) => _carbsRequired = val;

  bool hasCarbsRequired() => _carbsRequired != null;

  // "carbs_contained" field.
  String? _carbsContained;
  String get carbsContained => _carbsContained ?? '';
  set carbsContained(String? val) => _carbsContained = val;

  bool hasCarbsContained() => _carbsContained != null;

  // "carbs_percentage" field.
  String? _carbsPercentage;
  String get carbsPercentage => _carbsPercentage ?? '';
  set carbsPercentage(String? val) => _carbsPercentage = val;

  bool hasCarbsPercentage() => _carbsPercentage != null;

  static CarbsStruct fromMap(Map<String, dynamic> data) => CarbsStruct(
        carbsRequired: castToType<String?>(data['carbs_required']),
        carbsContained: castToType<String?>(data['carbs_contained']),
        carbsPercentage: castToType<String?>(data['carbs_percentage']),
      );

  static CarbsStruct? maybeFromMap(dynamic data) =>
      data is Map ? CarbsStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'carbs_required': _carbsRequired,
        'carbs_contained': _carbsContained,
        'carbs_percentage': _carbsPercentage,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'carbs_required': serializeParam(
          _carbsRequired,
          ParamType.String,
        ),
        'carbs_contained': serializeParam(
          _carbsContained,
          ParamType.String,
        ),
        'carbs_percentage': serializeParam(
          _carbsPercentage,
          ParamType.String,
        ),
      }.withoutNulls;

  static CarbsStruct fromSerializableMap(Map<String, dynamic> data) =>
      CarbsStruct(
        carbsRequired: deserializeParam(
          data['carbs_required'],
          ParamType.String,
          false,
        ),
        carbsContained: deserializeParam(
          data['carbs_contained'],
          ParamType.String,
          false,
        ),
        carbsPercentage: deserializeParam(
          data['carbs_percentage'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'CarbsStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is CarbsStruct &&
        carbsRequired == other.carbsRequired &&
        carbsContained == other.carbsContained &&
        carbsPercentage == other.carbsPercentage;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([carbsRequired, carbsContained, carbsPercentage]);
}

CarbsStruct createCarbsStruct({
  String? carbsRequired,
  String? carbsContained,
  String? carbsPercentage,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    CarbsStruct(
      carbsRequired: carbsRequired,
      carbsContained: carbsContained,
      carbsPercentage: carbsPercentage,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

CarbsStruct? updateCarbsStruct(
  CarbsStruct? carbs, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    carbs
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addCarbsStructData(
  Map<String, dynamic> firestoreData,
  CarbsStruct? carbs,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (carbs == null) {
    return;
  }
  if (carbs.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && carbs.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final carbsData = getCarbsFirestoreData(carbs, forFieldValue);
  final nestedData = carbsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = carbs.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getCarbsFirestoreData(
  CarbsStruct? carbs, [
  bool forFieldValue = false,
]) {
  if (carbs == null) {
    return {};
  }
  final firestoreData = mapToFirestore(carbs.toMap());

  // Add any Firestore field values
  carbs.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getCarbsListFirestoreData(
  List<CarbsStruct>? carbss,
) =>
    carbss?.map((e) => getCarbsFirestoreData(e, true)).toList() ?? [];

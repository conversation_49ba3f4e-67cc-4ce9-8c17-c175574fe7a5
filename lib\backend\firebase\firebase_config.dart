import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
            apiKey: "AIzaSyBhO6JDoYC2Lf8fOnRLjcILd3GCDC8gNwY",
            authDomain: "cal-count-a-i-gzhesm.firebaseapp.com",
            projectId: "cal-count-a-i-gzhesm",
            storageBucket: "cal-count-a-i-gzhesm.firebasestorage.app",
            messagingSenderId: "55847766910",
            appId: "1:55847766910:web:483f290e9b4c61dfe5812f"));
  } else {
    await Firebase.initializeApp();
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cal_counti_a_i/backend/api_requests/api_calls.dart';
import 'package:cal_counti_a_i/backend/schema/structs/meal_items_struct.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_icon_button.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:cal_counti_a_i/pages/Ingredients/edit_ingredients.dart';
import 'package:cal_counti_a_i/pages/Ingredients/edit_meal_name.dart';
import 'package:cal_counti_a_i/pages/Ingredients/fix_result.dart';
import 'package:cal_counti_a_i/pages/meal_details/meal_details_model.dart';
import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'dart:developer' as developer;

import '../../backend/schema/structs/meal_detail_struct.dart';
import '../../componentes/ingredient_item/nutrient_type.dart';

class DishNutritionDetails extends StatefulWidget {
  final MealDetailStruct? mMealItem;
  final String? imageUrl;

  const DishNutritionDetails({
    super.key,
    required this.mMealItem,
    required this.imageUrl,
  });

  @override
  State<DishNutritionDetails> createState() => _DishNutritionDetailsState();
}

class _DishNutritionDetailsState extends State<DishNutritionDetails>
    with TickerProviderStateMixin {
  late MealDetailsModel _model;
  final scaffoldKey = GlobalKey<ScaffoldState>();

  int dishCount = 1;
  TextEditingController? caloriesCtrl;
  TextEditingController? proteinCtrl;
  TextEditingController? carbsCtrl;
  TextEditingController? fatsCtrl;
  int healthScore = 0;
  List<MealItemsStruct> dishIngredients = [];
  bool isLoading = true;
  double baseCalories = 0.0;
  double baseProtein = 0.0;
  double baseCarbs = 0.0;
  double baseFats = 0.0;
  double totalCalories = 0.0;
  double totalProtein = 0.0;
  double totalCarbs = 0.0;
  double totalFats = 0.0;
  String? mealName;
  bool isEnglish = false;

  // Store ingredient-based totals for reset option
  double ingredientCalories = 0.0;
  double ingredientProtein = 0.0;
  double ingredientCarbs = 0.0;
  double ingredientFats = 0.0;

  @override
  void initState() {
    super.initState();
    developer.log('initState started', name: 'DishNutritionDetails');
    _model = createModel(context, () => MealDetailsModel());

    mealName = widget.mMealItem?.name ?? 'Unnamed Meal';
    healthScore = widget.mMealItem?.healthScore ?? 0;
    dishCount = widget.mMealItem?.quantity ?? 1;

    try {
      final mealQuantity = _parseToDouble(widget.mMealItem?.quantity) == 0.0
          ? 1.0
          : _parseToDouble(widget.mMealItem?.quantity);
      baseCalories =
          _parseToDouble(widget.mMealItem?.totalCalories) / mealQuantity;
      baseProtein =
          _parseToDouble(widget.mMealItem?.totalProteins) / mealQuantity;
      baseCarbs = _parseToDouble(widget.mMealItem?.totalCarbs) / mealQuantity;
      baseFats = _parseToDouble(widget.mMealItem?.totalFats) / mealQuantity;
    } catch (e) {
      developer.log('Error parsing base values: $e',
          name: 'DishNutritionDetails');
    }

    _updateTotals();

    caloriesCtrl = TextEditingController(text: _formatNumber(totalCalories));
    proteinCtrl = TextEditingController(text: _formatNumber(totalProtein));
    carbsCtrl = TextEditingController(text: _formatNumber(totalCarbs));
    fatsCtrl = TextEditingController(text: _formatNumber(totalFats));

    _fetchMealDetails();
    developer.log('initState completed', name: 'DishNutritionDetails');
  }

  String _formatNumber(double value) {
    if (value == value.floorToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(1);
  }

  double _parseToDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      String cleaned =
          value.replaceAll('קלוריות', '').replaceAll('גרם', '').trim();
      final parsed = double.tryParse(cleaned) ?? 0.0;
      if (parsed == 0.0 && cleaned.isNotEmpty) {
        developer.log(
            'Failed to parse String to double: "$value" (cleaned: "$cleaned")',
            name: 'DishNutritionDetails');
      }
      return parsed;
    }
    developer.log(
        'Unsupported type for parsing to double: ${value.runtimeType}',
        name: 'DishNutritionDetails');
    return 0.0;
  }

  void _updateTotals() {
    totalCalories = baseCalories * dishCount;
    totalProtein = baseProtein * dishCount;
    totalCarbs = baseCarbs * dishCount;
    totalFats = baseFats * dishCount;
    _updateControllers();
  }

  void _updateControllers() {
    caloriesCtrl?.text = _formatNumber(totalCalories);
    proteinCtrl?.text = _formatNumber(totalProtein);
    carbsCtrl?.text = _formatNumber(totalCarbs);
    fatsCtrl?.text = _formatNumber(totalFats);
  }

  // Calculate ingredient-based totals for reset option
  void _calculateIngredientTotals() {
    ingredientCalories = 0.0;
    ingredientProtein = 0.0;
    ingredientCarbs = 0.0;
    ingredientFats = 0.0;

    for (final ingredient in dishIngredients) {
      if (ingredient.isEnable) {
        ingredientCalories += _parseToDouble(ingredient.calories);
        ingredientProtein += _parseToDouble(ingredient.proteins);
        ingredientCarbs += _parseToDouble(ingredient.carbs);
        ingredientFats += _parseToDouble(ingredient.fats);
      }
    }

    // Ensure non-negative values
    ingredientCalories = ingredientCalories < 0 ? 0.0 : ingredientCalories;
    ingredientProtein = ingredientProtein < 0 ? 0.0 : ingredientProtein;
    ingredientCarbs = ingredientCarbs < 0 ? 0.0 : ingredientCarbs;
    ingredientFats = ingredientFats < 0 ? 0.0 : ingredientFats;
  }

  // Reset totals to ingredient-based values
  void _resetToIngredientTotals() {
    setState(() {
      // Reset base values to widget's meal item totals, normalized by quantity
      final mealQuantity = _parseToDouble(widget.mMealItem?.quantity) == 0.0
          ? 1.0
          : _parseToDouble(widget.mMealItem?.quantity);
      baseCalories =
          _parseToDouble(widget.mMealItem?.totalCalories) / mealQuantity;
      baseProtein =
          _parseToDouble(widget.mMealItem?.totalProteins) / mealQuantity;
      baseCarbs = _parseToDouble(widget.mMealItem?.totalCarbs) / mealQuantity;
      baseFats = _parseToDouble(widget.mMealItem?.totalFats) / mealQuantity;

      // Reset all ingredients to enabled
      dishIngredients = dishIngredients
          .map((ingredient) => ingredient.copyWith(isEnable: true))
          .toList();
      _model.mealDetail = _model.mealDetail?.copyWith(
        items: _model.mealDetail?.items
            .map((item) => item.copyWith(isEnable: true))
            .toList(),
      );

      _calculateIngredientTotals();
      _updateTotals();
      developer.log('Reset to ingredient totals: Calories=$baseCalories',
          name: 'DishNutritionDetails');
    });
  }

  Future<void> _fetchMealDetails() async {
    logFirebaseEvent('dish_nutrition_details_backend_call');
    setState(() => isLoading = true);

    final mealId = widget.mMealItem?.id;
    if (mealId == null || mealId.toString().trim().isEmpty) {
      developer.log('Error: mealId is null or empty',
          name: 'DishNutritionDetails');
      setState(() => isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Cannot load ingredients: Invalid meal ID'
                : 'לא ניתן לטעון רכיבים: מזהה ארוחה לא תקין')),
      );
      return;
    }

    final parsedMealId = int.tryParse(mealId.toString());
    if (parsedMealId == null) {
      developer.log('Error: mealId "$mealId" is not a valid integer',
          name: 'DishNutritionDetails');
      setState(() => isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Cannot load ingredients: Invalid meal ID format'
                : 'לא ניתן לטעון רכיבים: פורמט מזהה ארוחה לא תקין')),
      );
      return;
    }

    developer.log(
        'Fetching MealDetailCall with mealId: $parsedMealId, accessToken: ${FFAppState().authToken}',
        name: 'DishNutritionDetails');
    final apiResult = await MealDetailCall.call(
      accessToken: FFAppState().authToken,
      mealId: parsedMealId,
    );

    developer.log(
        'MealDetailCall response: ${jsonEncode(apiResult.jsonBody)}, Status: ${apiResult.statusCode}',
        name: 'DishNutritionDetails');

    if (apiResult.succeeded) {
      logFirebaseEvent('dish_nutrition_details_update_page_state');
      setState(() {
        _model.mealDetail = MealDetailCall.data(apiResult.jsonBody);
        final items = _model.mealDetail?.items ?? [];
        dishIngredients = items.map((item) {
          return MealItemsStruct(
            id: item.id.isNotEmpty
                ? item.id
                : 'unknown_${item.name.isNotEmpty ? item.name : 'item_${items.indexOf(item)}'}',
            name: item.name.isNotEmpty ? item.name : 'Unknown',
            quantity: item.quantity,
            calories: item.calories,
            fats: item.fats,
            proteins: item.proteins,
            carbs: item.carbs,
            spices: item.spices,
            expanded: item.expanded,
            isEnable: item.isEnable ?? true,
            firestoreUtilData: item.firestoreUtilData,
          );
        }).toList();
        dishCount = _model.mealDetail?.quantity ?? 1;
        // Use server totals as base values, normalized by quantity
        final mealQuantity = _parseToDouble(_model.mealDetail?.quantity) == 0.0
            ? 1.0
            : _parseToDouble(_model.mealDetail?.quantity);
        baseCalories =
            _parseToDouble(_model.mealDetail?.totalCalories) / mealQuantity;
        baseProtein =
            _parseToDouble(_model.mealDetail?.totalProteins) / mealQuantity;
        baseCarbs =
            _parseToDouble(_model.mealDetail?.totalCarbs) / mealQuantity;
        baseFats = _parseToDouble(_model.mealDetail?.totalFats) / mealQuantity;
        _calculateIngredientTotals();
        _updateTotals();
        isLoading = false;
      });
    } else {
      setState(() => isLoading = false);
      final errorMessage =
          apiResult.jsonBody['message'] ?? 'Failed to load ingredients';
      developer.log('MealDetailCall failed: $errorMessage',
          name: 'DishNutritionDetails');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Failed to load ingredients: $errorMessage'
                : 'נכשל בטעינת הרכיבים: $errorMessage')),
      );
    }
  }

  void _addIngredientNutrition(MealItemsStruct ingredient) {
    setState(() {
      // Update isEnable in mealDetail items
      final items = _model.mealDetail?.items ?? [];
      final updatedItems = items.map((item) {
        if (item.id == ingredient.id ||
            (item.id.isEmpty && item.name == ingredient.name)) {
          return MealItemsStruct(
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            calories: item.calories,
            fats: item.fats,
            proteins: item.proteins,
            carbs: item.carbs,
            spices: item.spices,
            expanded: item.expanded,
            isEnable: true,
            firestoreUtilData: item.firestoreUtilData,
          );
        }
        return item;
      }).toList();
      _model.mealDetail = MealDetailStruct(
        id: _model.mealDetail?.id,
        name: _model.mealDetail?.name,
        totalCalories: _model.mealDetail?.totalCalories,
        totalFats: _model.mealDetail?.totalFats,
        totalProteins: _model.mealDetail?.totalProteins,
        totalCarbs: _model.mealDetail?.totalCarbs,
        items: updatedItems,
        healthScore: _model.mealDetail?.healthScore,
      );

      // Update dishIngredients
      final index =
          dishIngredients.indexWhere((ing) => ing.id == ingredient.id);
      if (index >= 0) {
        dishIngredients[index] =
            dishIngredients[index].copyWith(isEnable: true);
      } else {
        dishIngredients.add(ingredient.copyWith(isEnable: true));
      }

      // Add ingredient's nutrition incrementally
      baseCalories += _parseToDouble(ingredient.calories);
      baseProtein += _parseToDouble(ingredient.proteins);
      baseCarbs += _parseToDouble(ingredient.carbs);
      baseFats += _parseToDouble(ingredient.fats);

      // Ensure non-negative values
      baseCalories = baseCalories < 0 ? 0.0 : baseCalories;
      baseProtein = baseProtein < 0 ? 0.0 : baseProtein;
      baseCarbs = baseCarbs < 0 ? 0.0 : baseCarbs;
      baseFats = baseFats < 0 ? 0.0 : baseFats;

      _calculateIngredientTotals();
      _updateTotals();
      developer.log('Added ingredient: Calories=$baseCalories',
          name: 'DishNutritionDetails');
    });
  }

  void _removeIngredientNutrition(MealItemsStruct ingredient) {
    setState(() {
      // Update isEnable in mealDetail items
      final items = _model.mealDetail?.items ?? [];
      final updatedItems = items.map((item) {
        if (item.id == ingredient.id ||
            (item.id.isEmpty && item.name == ingredient.name)) {
          return MealItemsStruct(
            id: item.id,
            name: item.name,
            quantity: item.quantity,
            calories: item.calories,
            fats: item.fats,
            proteins: item.proteins,
            carbs: item.carbs,
            spices: item.spices,
            expanded: item.expanded,
            isEnable: false,
            firestoreUtilData: item.firestoreUtilData,
          );
        }
        return item;
      }).toList();
      _model.mealDetail = MealDetailStruct(
        id: _model.mealDetail?.id,
        name: _model.mealDetail?.name,
        totalCalories: _model.mealDetail?.totalCalories,
        totalFats: _model.mealDetail?.totalFats,
        totalProteins: _model.mealDetail?.totalProteins,
        totalCarbs: _model.mealDetail?.totalCarbs,
        items: updatedItems,
        healthScore: _model.mealDetail?.healthScore,
      );

      // Update dishIngredients
      final index =
          dishIngredients.indexWhere((ing) => ing.id == ingredient.id);
      if (index >= 0) {
        dishIngredients[index] =
            dishIngredients[index].copyWith(isEnable: false);
      }

      // Subtract ingredient's nutrition incrementally
      baseCalories -= _parseToDouble(ingredient.calories);
      baseProtein -= _parseToDouble(ingredient.proteins);
      baseCarbs -= _parseToDouble(ingredient.carbs);
      baseFats -= _parseToDouble(ingredient.fats);

      // Ensure non-negative values
      baseCalories = baseCalories < 0 ? 0.0 : baseCalories;
      baseProtein = baseProtein < 0 ? 0.0 : baseProtein;
      baseCarbs = baseCarbs < 0 ? 0.0 : baseCarbs;
      baseFats = baseFats < 0 ? 0.0 : baseFats;

      // NEW: If all ingredients are disabled, set all base values to 0
      final allDisabled = dishIngredients.every((ing) => ing.isEnable == false);
      if (dishIngredients.isNotEmpty && allDisabled) {
        baseCalories = 0.0;
        baseProtein = 0.0;
        baseCarbs = 0.0;
        baseFats = 0.0;
      }

      _calculateIngredientTotals();
      _updateTotals();
      developer.log('Removed ingredient: Calories=$baseCalories',
          name: 'DishNutritionDetails');
    });
  }

  Future<void> _editNutrient(
    NutrientType nutrientType,
    double currentValue,
  ) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditIngredients(
          nutrientType: nutrientType,
          initialValue: currentValue / dishCount,
          maxValue: getNutritionLeftVal(nutrientType),
        ),
      ),
    );

    if (result != null && result is double) {
      setState(() {
        switch (nutrientType) {
          case NutrientType.calories:
            baseCalories = result; // Per-portion value
            break;
          case NutrientType.protein:
            baseProtein = result;
            break;
          case NutrientType.carbs:
            baseCarbs = result;
            break;
          case NutrientType.fats:
            baseFats = result;
            break;
        }
        _updateTotals();
        developer.log('Edited $nutrientType: Base value set to $result',
            name: 'DishNutritionDetails');
      });
    }
  }

  double getNutritionLeftVal(NutrientType nutrientType) {
    switch (nutrientType) {
      case NutrientType.calories:
        return FFAppState().savedUserData.caloriesRequired.toDouble();
      case NutrientType.protein:
        return FFAppState().savedUserData.proteinsRequired.toDouble();
      case NutrientType.carbs:
        return FFAppState().savedUserData.carbsRequired.toDouble();
      case NutrientType.fats:
        return FFAppState().savedUserData.fatsRequired.toDouble();
    }
  }

  Future<void> _updateMeal() async {
    logFirebaseEvent('dish_nutrition_details_update_meal');

    final mealId = widget.mMealItem?.id;
    if (mealId == null || mealId.toString().trim().isEmpty) {
      developer.log('Error: meal_id is null or empty',
          name: 'DishNutritionDetails');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Cannot update meal: Invalid meal ID'
                : 'לא ניתן לעדכן ארוחה: מזהה ארוחה לא תקין')),
      );
      return;
    }

    final parsedMealId = int.tryParse(mealId.toString());
    if (parsedMealId == null) {
      developer.log('Error: meal_id "$mealId" is not a valid integer',
          name: 'DishNutritionDetails');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Cannot update meal: Invalid meal ID format'
                : 'לא ניתן לעדכן ארוחה: פורמט מזהה ארוחה לא תקין')),
      );
      return;
    }

    if (mealName == null || mealName!.trim().isEmpty) {
      developer.log('Error: meal_name is null or empty',
          name: 'DishNutritionDetails');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Cannot update meal: Meal name is required'
                : 'לא ניתן לעדכן ארוחה: שם הארוחה נדרש')),
      );
      return;
    }

    final updateItems =
        (_model.mealDetail?.items ?? []).asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final itemId = item.id.isNotEmpty
          ? item.id
          : 'unknown_${item.name.isNotEmpty ? item.name : 'item_$index'}';
      return <String, Object>{
        'meal_item_id': itemId,
        'is_enable': item.isEnable,
      };
    }).toList();

    final requestBody = <String, Object>{
      'meal_id': parsedMealId,
      'total_calories': totalCalories, // baseCalories * dishCount
      'total_fats': totalFats, // baseFats * dishCount
      'total_proteins': totalProtein, // baseProtein * dishCount
      'total_carbs': totalCarbs, // baseCarbs * dishCount
      'name': mealName.toString(),
      'quantity': dishCount.toDouble(),
      'update_items': updateItems,
    };
    developer.log('UpdateMeal request: ${jsonEncode(requestBody)}',
        name: 'DishNutritionDetails');

    showDialog(
      context: context,
      builder: (dialogContext) => Dialog(
        elevation: 0,
        insetPadding: EdgeInsets.zero,
        backgroundColor: Colors.transparent,
        alignment: const AlignmentDirectional(0.0, 0.0)
            .resolve(Directionality.of(context)),
        child: WebViewAware(
          child: GestureDetector(
            onTap: () {
              FocusScope.of(dialogContext).unfocus();
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: const SizedBox(
                height: 80.0, width: 80.0, child: CircularProgressWidget()),
          ),
        ),
      ),
    );

    final response = await UpdateMealCall.call(
      accessToken: FFAppState().authToken,
      jsonJson: requestBody,
    );

    Navigator.pop(context);

    developer.log(
        'UpdateMeal response: ${jsonEncode(response.jsonBody)}, Status: ${response.statusCode}',
        name: 'DishNutritionDetails');

    if (response.succeeded && response.jsonBody['status'] == 200) {
      logFirebaseEvent('dish_nutrition_details_update_success');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Meal data updated successfully'
                : 'נתוני הארוחה עודכנו בהצלחה')),
      );
      await Future.delayed(const Duration(milliseconds: 50));
      Navigator.of(context).pop(true);
    } else {
      logFirebaseEvent('dish_nutrition_details_update_failed');
      final errorMessage = response.jsonBody['message'] ?? 'Unknown error';
      developer.log('UpdateMeal failed: $errorMessage',
          name: 'DishNutritionDetails');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(isEnglish
                ? 'Failed to update meal: $errorMessage'
                : 'נכשל בעדכון הארוחה: $errorMessage')),
      );
    }
  }

  @override
  void dispose() {
    _model.dispose();
    caloriesCtrl?.dispose();
    proteinCtrl?.dispose();
    carbsCtrl?.dispose();
    fatsCtrl?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
      name: 'DishNutritionDetail',
      builder: (context) => Scaffold(
        key: scaffoldKey,
        backgroundColor: Colors.white,
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      key: ValueKey(widget.imageUrl ?? ''),
                      fadeInDuration: const Duration(milliseconds: 600),
                      fadeOutDuration: const Duration(milliseconds: 600),
                      imageUrl: widget.imageUrl ?? '',
                      width: double.infinity,
                      height: 280.0,
                      fit: BoxFit.cover,
                      errorWidget: (context, error, stackTrace) => Image.asset(
                        'assets/images/error_image.png',
                        width: double.infinity,
                        height: 250.0,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 12),
                          child: AppBar(
                            leading: IconButton(
                              icon: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: Colors.grey[300],
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: const Icon(Icons.arrow_back,
                                    color: Colors.white),
                              ),
                              onPressed: () => Navigator.of(context).pop(),
                            ),
                            elevation: 0,
                            backgroundColor: Colors.transparent,
                            titleSpacing: 0,
                            title: Text(
                              isEnglish ? 'Nutrition' : 'תזונה',
                              style: FlutterFlowTheme.of(context)
                                  .headlineMedium
                                  .copyWith(color: Colors.white, fontSize: 18),
                            ),
                            centerTitle: false,
                            actions: [
                              FlutterFlowIconButton(
                                borderRadius: 30.0,
                                borderWidth: 1.0,
                                buttonSize: 40.0,
                                icon: const Icon(
                                  Icons.delete_forever_outlined,
                                  color: Colors.red,
                                  size: 24.0,
                                ),
                                onPressed: () async {
                                  final confirmDialogResponse =
                                      await showDialog<bool>(
                                            context: context,
                                            builder: (alertDialogContext) {
                                              return WebViewAware(
                                                child: AlertDialog(
                                                  title: Text(isEnglish
                                                      ? 'Delete Meal?'
                                                      : 'למחוק ארוחה?'),
                                                  content: Text(isEnglish
                                                      ? 'Are you sure you want to delete meal data? This action cannot be undone, and all your calories, carbs, fiber, and related data will be permanently removed.'
                                                      : 'האם אתה בטוח שברצונך למחוק את נתוני הארוחה? פעולה זו אינה ניתנת לביטול וכל הקלוריות, הפחמימות, הסיבים התזונתיים והנתונים הקשורים יימחקו לצמיתות.'),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.pop(
                                                              alertDialogContext,
                                                              false),
                                                      child: Text(FFLocalizations
                                                              .of(context)
                                                          .getText(
                                                              '5efik18d' /* Cancel */)),
                                                    ),
                                                    TextButton(
                                                      onPressed: () =>
                                                          Navigator.pop(
                                                              alertDialogContext,
                                                              true),
                                                      child: Text(FFLocalizations
                                                              .of(context)
                                                          .getText(
                                                              '4efik18d' /* Confirm */)),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ) ??
                                          false;

                                  if (confirmDialogResponse) {
                                    logFirebaseEvent('IconButton_delete_meal');
                                    showDialog(
                                      context: context,
                                      builder: (dialogContext) {
                                        return Dialog(
                                          elevation: 0,
                                          insetPadding: EdgeInsets.zero,
                                          backgroundColor: Colors.transparent,
                                          alignment: const AlignmentDirectional(
                                                  0.0, 0.0)
                                              .resolve(
                                                  Directionality.of(context)),
                                          child: WebViewAware(
                                            child: GestureDetector(
                                              onTap: () {
                                                FocusScope.of(dialogContext)
                                                    .unfocus();
                                                FocusManager
                                                    .instance.primaryFocus
                                                    ?.unfocus();
                                              },
                                              child: const SizedBox(
                                                  height: 80.0,
                                                  width: 80.0,
                                                  child:
                                                      CircularProgressWidget()),
                                            ),
                                          ),
                                        );
                                      },
                                    );

                                    _model.deleteMealResponse =
                                        await DeleteMealCall.call(
                                      accessToken: FFAppState().authToken,
                                      jsonJson: <String, Object>{
                                        'meal_id': widget.mMealItem?.id ?? ''
                                      },
                                    );

                                    await Future.delayed(
                                        const Duration(milliseconds: 50));
                                    Navigator.pop(context);

                                    if (_model.deleteMealResponse?.succeeded ??
                                        false) {
                                      logFirebaseEvent(
                                          'Container_navigate_back');
                                      await Future.delayed(
                                          const Duration(milliseconds: 50));
                                      Navigator.of(context).pop(true);
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                            content: Text(isEnglish
                                                ? 'Failed to delete meal.'
                                                : 'נכשל במחיקת הארוחה.')),
                                      );
                                    }
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 120),
                        Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(25),
                                topLeft: Radius.circular(25)),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 18.0, vertical: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 20),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    GestureDetector(
                                      onTap: () async {
                                        final editedName = await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  EditMealName(
                                                      currentName: mealName)),
                                        );
                                        if (editedName != null &&
                                            editedName is String) {
                                          setState(() {
                                            mealName = editedName;
                                            developer.log(
                                                'Meal name updated to: $mealName',
                                                name: 'DishNutritionDetails');
                                          });
                                        }
                                      },
                                      child: Row(
                                        children: [
                                          SizedBox(
                                            width: 150,
                                            child: Text(
                                              valueOrDefault<String>(
                                                  mealName, '-'),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .headlineMedium
                                                      .copyWith(
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 20,
                                                          overflow: TextOverflow
                                                              .ellipsis),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Icon(Icons.edit,
                                              size: 20,
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .primary),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 30),
                                    Expanded(
                                      child: Container(
                                        width: 120,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(30),
                                          border: Border.all(
                                              color: Colors.black, width: 1),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.remove),
                                              onPressed: () {
                                                setState(() {
                                                  if (dishCount > 1) {
                                                    dishCount--;
                                                    _updateTotals();
                                                  }
                                                });
                                              },
                                            ),
                                            Text(
                                              dishCount.toString(),
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .headlineMedium
                                                      .override(
                                                          fontWeight:
                                                              FontWeight.normal,
                                                          fontSize: 18),
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.add),
                                              onPressed: () {
                                                setState(() {
                                                  dishCount++;
                                                  _updateTotals();
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 18),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                        color:
                                            FlutterFlowTheme.of(context).grey),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 3, horizontal: 12),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.local_fire_department,
                                            color: Colors.black, size: 28),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: TextField(
                                            onTap: () => _editNutrient(
                                                NutrientType.calories,
                                                totalCalories),
                                            readOnly: true,
                                            cursorColor: Colors.black,
                                            controller: caloriesCtrl,
                                            keyboardType: TextInputType.number,
                                            style: FlutterFlowTheme.of(context)
                                                .headlineMedium
                                                .override(
                                                    fontSize: 16,
                                                    fontWeight:
                                                        FontWeight.normal),
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(
                                                      context)
                                                  .headlineMedium
                                                  .copyWith(
                                                      fontWeight:
                                                          FontWeight.normal,
                                                      fontSize: 18,
                                                      color:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .primaryText),
                                              border: InputBorder.none,
                                              labelText: isEnglish
                                                  ? 'Calories'
                                                  : 'קלוריות',
                                            ),
                                          ),
                                        ),
                                        IconButton(
                                          icon:
                                              const Icon(Icons.edit, size: 18),
                                          onPressed: () => _editNutrient(
                                              NutrientType.calories,
                                              totalCalories),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          border: Border.all(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .grey),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SvgPicture.asset(
                                                  'assets/images/Frame.svg',
                                                  width: 18.0,
                                                  height: 18.0,
                                                  fit: BoxFit.contain),
                                              const SizedBox(width: 10),
                                              Flexible(
                                                child: TextField(
                                                  onTap: () => _editNutrient(
                                                      NutrientType.protein,
                                                      totalProtein),
                                                  readOnly: true,
                                                  cursorColor: Colors.black,
                                                  controller: proteinCtrl,
                                                  keyboardType:
                                                      TextInputType.number,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .copyWith(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                  decoration: InputDecoration(
                                                    labelStyle: FlutterFlowTheme
                                                            .of(context)
                                                        .headlineMedium
                                                        .copyWith(
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            fontSize: 18,
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .primaryText),
                                                    border: InputBorder.none,
                                                    labelText: isEnglish
                                                        ? 'Protein'
                                                        : 'חלבון',
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          border: Border.all(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .grey),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SvgPicture.asset(
                                                'assets/images/wheat-barley_svgrepo.com.svg',
                                                width: 20.0,
                                                height: 20.0,
                                                color: Colors.orange,
                                                fit: BoxFit.cover,
                                              ),
                                              const SizedBox(width: 10),
                                              Flexible(
                                                child: TextField(
                                                  onTap: () => _editNutrient(
                                                      NutrientType.carbs,
                                                      totalCarbs),
                                                  readOnly: true,
                                                  cursorColor: Colors.black,
                                                  controller: carbsCtrl,
                                                  keyboardType:
                                                      TextInputType.number,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .copyWith(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                  decoration: InputDecoration(
                                                    labelStyle: FlutterFlowTheme
                                                            .of(context)
                                                        .headlineMedium
                                                        .copyWith(
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            fontSize: 18,
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .primaryText),
                                                    border: InputBorder.none,
                                                    labelText: isEnglish
                                                        ? 'Carbs'
                                                        : 'פחמימות',
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          border: Border.all(
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .grey),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 10),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              const Icon(Icons.opacity,
                                                  color: Colors.blue, size: 18),
                                              const SizedBox(width: 10),
                                              Flexible(
                                                child: TextField(
                                                  onTap: () => _editNutrient(
                                                      NutrientType.fats,
                                                      totalFats),
                                                  readOnly: true,
                                                  cursorColor: Colors.black,
                                                  controller: fatsCtrl,
                                                  keyboardType:
                                                      TextInputType.number,
                                                  style: FlutterFlowTheme.of(
                                                          context)
                                                      .bodyLarge
                                                      .copyWith(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold),
                                                  decoration: InputDecoration(
                                                    labelStyle: FlutterFlowTheme
                                                            .of(context)
                                                        .headlineMedium
                                                        .copyWith(
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            fontSize: 18,
                                                            color: FlutterFlowTheme
                                                                    .of(context)
                                                                .primaryText),
                                                    border: InputBorder.none,
                                                    labelText: isEnglish
                                                        ? 'Fats'
                                                        : 'שומנים',
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 18),
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                        color:
                                            FlutterFlowTheme.of(context).grey),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10.0, horizontal: 8),
                                    child: Row(
                                      children: [
                                        Container(
                                          height: 50,
                                          width: 50,
                                          decoration: BoxDecoration(
                                            color: FlutterFlowTheme.of(context)
                                                .lightpink
                                                .withOpacity(0.2),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          child: const Icon(Icons.favorite,
                                              color: Colors.pink, size: 28),
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                          child: Container(
                                            padding: EdgeInsets.only(
                                                left: 4, right: 4),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      isEnglish
                                                          ? 'Health score'
                                                          : 'מדד בריאות',
                                                      style:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .bodyLarge
                                                              .override(
                                                                  fontSize: 14),
                                                    ),
                                                    Text(
                                                      '${healthScore.toString()}/10',
                                                      style:
                                                          FlutterFlowTheme.of(
                                                                  context)
                                                              .bodyLarge
                                                              .override(
                                                                  fontSize: 14),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 10),
                                                LinearProgressIndicator(
                                                  value: healthScore / 10.0,
                                                  backgroundColor:
                                                      Colors.grey[300],
                                                  color: Colors.green,
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 18),
                                Row(
                                  children: [
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        isEnglish ? 'Ingredients' : 'רכיבים',
                                        style: FlutterFlowTheme.of(context)
                                            .titleMedium
                                            .copyWith(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18),
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    Spacer(),
                                    // Align(
                                    //   alignment: Alignment.centerRight,
                                    //   child: (baseCalories ==
                                    //               _parseToDouble(widget
                                    //                   .mMealItem
                                    //                   ?.totalCalories) &&
                                    //           baseProtein ==
                                    //               _parseToDouble(widget
                                    //                   .mMealItem
                                    //                   ?.totalProteins) &&
                                    //           baseCarbs ==
                                    //               _parseToDouble(widget
                                    //                   .mMealItem?.totalCarbs) &&
                                    //           baseFats ==
                                    //               _parseToDouble(widget
                                    //                   .mMealItem?.totalFats) &&
                                    //           dishIngredients.every(
                                    //               (ingredient) =>
                                    //                   ingredient.isEnable))
                                    //       ? const SizedBox.shrink()
                                    //       : GestureDetector(
                                    //           onTap: _resetToIngredientTotals,
                                    //           child: Text(
                                    //             isEnglish
                                    //                 ? 'Reset to Ingredients'
                                    //                 : 'אפס לפי רכיבים',
                                    //             style:
                                    //                 FlutterFlowTheme.of(context)
                                    //                     .titleMedium
                                    //                     .copyWith(
                                    //                         fontWeight:
                                    //                             FontWeight.bold,
                                    //                         fontSize: 14),
                                    //           ),
                                    //         ),
                                    // ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                isLoading
                                    ? const Center(
                                        child: CircularProgressWidget())
                                    : Builder(
                                        builder: (context) {
                                          final ingredientsItems = _model
                                                  .mealDetail?.items
                                                  ?.toList() ??
                                              [];
                                          if (ingredientsItems.isEmpty) {
                                            return Center(
                                              child: Text(isEnglish
                                                  ? 'No ingredients available.'
                                                  : 'אין רכיבים זמינים.'),
                                            );
                                          }
                                          return ingredientItems(
                                              ingredientsItems, isEnglish);
                                        },
                                      ),
                                const SizedBox(height: 24),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withOpacity(0.8),
                        Colors.white.withOpacity(0.8),
                        Colors.transparent
                      ],
                      stops: [0.0, 0.4, 0.4],
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => FixResult(
                                      mealId: widget.mMealItem!.id.toString())),
                            );
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                                color: Colors.black, width: 1.2),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30)),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            foregroundColor: Colors.black,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.auto_awesome,
                                  size: 20, color: Colors.black),
                              const SizedBox(width: 8),
                              Text(
                                isEnglish ? 'Fix Results' : 'תקן תוצאות',
                                style: FlutterFlowTheme.of(context)
                                    .titleMedium
                                    .copyWith(
                                        fontSize: 16,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _updateMeal,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF232029),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30)),
                            elevation: 0,
                          ),
                          child: Text(
                            isEnglish ? 'Save' : 'שמור',
                            style: FlutterFlowTheme.of(context)
                                .titleMedium
                                .copyWith(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: FFAppState().bottomPadding),
          ],
        ),
      ),
    );
  }

  GridView ingredientItems(
      List<MealItemsStruct> ingredientsItems, bool isEnglish) {
    return GridView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
      ),
      itemCount: ingredientsItems.length,
      itemBuilder: (context, index) {
        final ingredient = ingredientsItems[index];
        final isEnabled = ingredient.isEnable ?? true;
        return GestureDetector(
          onTap: () {
            if (isEnabled) {
              _removeIngredientNutrition(ingredient);
            } else {
              _addIngredientNutrition(ingredient);
            }
          },
          child: Stack(
            key: ValueKey(ingredient.id),
            clipBehavior: Clip.none,
            children: [
              Container(
                width: double.infinity,
                margin:
                    const EdgeInsets.only(top: 4, right: 6, left: 2, bottom: 2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black12,
                        blurRadius: 6,
                        spreadRadius: 0,
                        offset: Offset(0, 2)),
                  ],
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        isEnglish
                            ? (ingredient.name.isNotEmpty
                                ? ingredient.name
                                : 'Unknown')
                            : (ingredient.name.isNotEmpty
                                ? ingredient.name
                                : 'לא ידוע'),
                        textAlign: TextAlign.center,
                        style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                              color: isEnabled
                                  ? FlutterFlowTheme.of(context).primaryText
                                  : Colors.grey[600],
                            ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        '${_parseToDouble(ingredient.calories).toStringAsFixed(0)}',
                        style:
                            FlutterFlowTheme.of(context).titleMedium.copyWith(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: isEnabled
                                      ? FlutterFlowTheme.of(context).primaryText
                                      : Colors.grey[600],
                                ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              if (!isEnabled)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(12)),
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        height: 25,
                        margin: const EdgeInsets.only(
                            top: 4, right: 6, left: 2, bottom: 2),
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context)
                              .primary
                              .withOpacity(0.8),
                          borderRadius: const BorderRadius.only(
                              bottomRight: Radius.circular(12),
                              bottomLeft: Radius.circular(12)),
                        ),
                        child: Center(
                          child: Text(
                            isEnglish ? 'Add' : 'הוסף',
                            style: FlutterFlowTheme.of(context)
                                .titleSmall
                                .override(color: Colors.white, fontSize: 12),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              else
                Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    icon: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: const [
                            BoxShadow(color: Colors.black12, blurRadius: 3)
                          ]),
                      child: Icon(Icons.close,
                          size: 18, color: FlutterFlowTheme.of(context).error),
                    ),
                    onPressed: () => _removeIngredientNutrition(ingredient),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/auth/firebase_auth/auth_util.dart';

import '../../../error_service.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/instant_timer.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'splash_model.dart';
export 'splash_model.dart';

class SplashWidget extends StatefulWidget {
  const SplashWidget({super.key});

  @override
  State<SplashWidget> createState() => _SplashWidgetState();
}

class _SplashWidgetState extends State<SplashWidget>
    with TickerProviderStateMixin {
  late SplashModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SplashModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('splash_custom_action');
      await actions.getDeviceSize(
        context,
      );
      logFirebaseEvent('splash_start_periodic_action');
      _model.instantTimer = InstantTimer.periodic(
        duration: Duration(milliseconds: 30),
        callback: (timer) async {
          logFirebaseEvent('splash_update_page_state');
          _model.timerCounter = _model.timerCounter! + 1;
          _model.progress = _model.progress! + 0.003;
          safeSetState(() {});
          if (_model.timerCounter == 99) {
            logFirebaseEvent('splash_stop_periodic_action');
            _model.instantTimer?.cancel();
            logFirebaseEvent('splash_update_page_state');
            _model.progress = 1.0;
            safeSetState(() {});
            logFirebaseEvent('splash_wait__delay');
            await Future.delayed(const Duration(milliseconds: 1000));
            if (loggedIn) {
              logFirebaseEvent('splash_wait__delay');
              await Future.delayed(const Duration(milliseconds: 100));
              if (loggedIn) {
                logFirebaseEvent('splash_navigate_to');

                context.pushNamed('dashboard');
              } else {
                logFirebaseEvent('splash_navigate_to');

                context.pushNamed('entry_screen');
              }
            } else {
              logFirebaseEvent('splash_navigate_to');

              context.pushNamed('entry_screen');
            }
            try {
              ErrorService.setupUser(
                currentUserReference?.id??'',
                currentUserDocument?.email ?? '',
                currentUserDocument?.displayName ?? '',
              );
            } catch (e) {
              print(e);
            }
          } else {
            return;
          }
        },
        startImmediately: true,
      );
    });

    animationsMap.addAll({
      'textOnPageLoadAnimation': AnimationInfo(
        loop: true,
        reverse: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 100.0.ms,
            duration: 800.0.ms,
            begin: 1.0,
            end: 0.0,
          ),
        ],
      ),
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return MeasuredWidget(
        name: 'Splash',
        builder: (context) => GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primary,
        body: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Image.asset(
                      'assets/images/AppIcon.png',
                      width: double.infinity,
                      height: 250.0,
                      fit: BoxFit.contain,
                    ),
                  ),
                  Text(
                    FFLocalizations.of(context).getText(
                      '2xe2fqe6' /* CalCounti AI */,
                    ),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'SFHebrew',
                          color: FlutterFlowTheme.of(context).alternate,
                          fontSize: 30.0,
                          letterSpacing: 0.0,
                          fontWeight: FontWeight.w600,
                        ),
                  ).animateOnPageLoad(
                      animationsMap['textOnPageLoadAnimation']!),
                ],
              ),
            ),
            if (valueOrDefault<bool>(
              FFAppState().appVersion != null && FFAppState().appVersion != '',
              false,
            ))
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 5.0),
                child: AnimatedDefaultTextStyle(
                  style: FlutterFlowTheme.of(context).labelMedium.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                      ),
                  duration: Duration(milliseconds: 600),
                  curve: Curves.easeIn,
                  child: Text(
                    FFAppState().appVersion,
                  ),
                ),
              ),
            Container(
              width: 1.0,
              height: FFAppState().bottomPadding,
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).primary,
              ),
            ),
          ],
        ),
      ),
    ));
  }
}

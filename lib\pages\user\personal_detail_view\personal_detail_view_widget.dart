import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';

import '/backend/api_requests/api_calls.dart';
import '/componentes/p_detail_item/p_detail_item_widget.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/instant_timer.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'personal_detail_view_model.dart';
export 'personal_detail_view_model.dart';

class PersonalDetailViewWidget extends StatefulWidget {
  const PersonalDetailViewWidget({super.key});

  @override
  State<PersonalDetailViewWidget> createState() =>
      _PersonalDetailViewWidgetState();
}

class _PersonalDetailViewWidgetState extends State<PersonalDetailViewWidget> {
  late PersonalDetailViewModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PersonalDetailViewModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('personal_detail_view_start_periodic_acti');
      _model.instantTimer = InstantTimer.periodic(
        duration: Duration(milliseconds: 1000),
        callback: (timer) async {
          logFirebaseEvent('personal_detail_view_update_page_state');
          _model.mValue = _model.mValue + 1;
          safeSetState(() {});
        },
        startImmediately: true,
      );
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return MeasuredWidget(
        name: 'PersonalDetailView',
        builder: (context) => GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: responsiveVisibility(
              context: context,
              tablet: false,
              tabletLandscape: false,
              desktop: false,
            )
                ? AppBar(
                    backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                    automaticallyImplyLeading: false,
                    actions: [],
                    flexibleSpace: FlexibleSpaceBar(
                      titlePadding: const EdgeInsets.all(0),
                      title: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            0.0,
                            valueOrDefault<double>(
                              FFAppState().topPadding + 16,
                              0.0,
                            ),
                            0.0,
                            0.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            FlutterFlowIconButton(
                              borderRadius: 8.0,
                              buttonSize: 40.0,
                              icon: Icon(
                                Icons.chevron_left_rounded,
                                color: FlutterFlowTheme.of(context).primary,
                                size: 24.0,
                              ),
                              onPressed: () async {
                                logFirebaseEvent('IconButton_navigate_back');
                                context.safePop();
                              },
                            ),
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    '886cglx7' /* Personal Details */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .headlineMedium
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        color: FlutterFlowTheme.of(context).primary,
                                        letterSpacing: 0.0,
                                      ),
                                ),
                              ),
                            ),
                            FlutterFlowIconButton(
                              borderRadius: 8.0,
                              buttonSize: 40.0,
                              icon: Icon(
                                Icons.edit_outlined,
                                color: FlutterFlowTheme.of(context).primary,
                                size: 24.0,
                              ),
                              onPressed: () async {
                                // // TODO: Custom code for await the screen
                                logFirebaseEvent('IconButton_navigate_to');

                                await context.pushNamed('personal_detail');
                                safeSetState(() {});
                              },
                            ),
                          ],
                        ),
                      ),
                      centerTitle: false,
                      expandedTitleScale: 1.0,
                    ),
                    toolbarHeight: 80.0,
                    elevation: 2.0,
                  )
                : null,
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                      child: Container(
                        decoration: BoxDecoration(),
                        child: ListView(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          shrinkWrap: true,
                          scrollDirection: Axis.vertical,
                          children: [
                            wrapWithModel(
                              model: _model.pDetailItemModel4,
                              updateCallback: () => safeSetState(() {}),
                              child: PDetailItemWidget(
                                title: valueOrDefault<String>(
                                  FFLocalizations.of(context).getVariableText(
                                    enText: 'Email',
                                    heText: 'אֶלֶקטרוֹנִי',
                                  ),
                                  'Email',
                                ),
                                valueData: (String var1) {
                                  return "$var1";
                                }(FFAppState().savedUserData.email),
                                onTap: () {
                                  return Future.value();
                                },
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 1.5,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryText,
                              ),
                            ),
                            wrapWithModel(
                              model: _model.pDetailItemModel1,
                              updateCallback: () => safeSetState(() {}),
                              child: PDetailItemWidget(
                                title: valueOrDefault<String>(
                                  FFLocalizations.of(context).getVariableText(
                                    enText: 'Date of Birth',
                                    heText: 'תאריך לידה',
                                  ),
                                  'Date of Birth',
                                ),
                                valueData: dateTimeFormat(
                                  "yMMMd",
                                  functions.stringToDateTime(
                                      FFAppState().savedUserData.dob),
                                  locale: FFLocalizations.of(context).languageCode,
                                ),
                                onTap: () async {
                                  logFirebaseEvent('p_detail_item_navigate_to');

                                  await context.pushNamed('personal_detail');

                                  logFirebaseEvent(
                                      'p_detail_item_update_app_state');

                                  safeSetState(() {});
                                },
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 1.5,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryText,
                              ),
                            ),
                            wrapWithModel(
                              model: _model.pDetailItemModel2,
                              updateCallback: () => safeSetState(() {}),
                              child: PDetailItemWidget(
                                valueData: FFAppState().savedUserData.gender,
                                title: valueOrDefault<String>(
                                  FFLocalizations.of(context).getVariableText(
                                    enText: 'Gender',
                                    heText: 'מגדר',
                                  ),
                                  'Gender',
                                ),
                                onTap: () async {
                                  logFirebaseEvent('p_detail_item_navigate_to');
                                  await context.pushNamed('personal_detail');
                                  safeSetState(() {});
                                },
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 1.5,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryText,
                              ),
                            ),
                            wrapWithModel(
                              model: _model.pDetailItemModel3,
                              updateCallback: () => safeSetState(() {}),
                              child: PDetailItemWidget(
                                title: valueOrDefault<String>(
                                  FFLocalizations.of(context).getVariableText(
                                    enText: 'Height',
                                    heText: 'גובה',
                                  ),
                                  'Height',
                                ),
                                valueData: (String var1, String var2) {
                                  return "$var1 $var2";
                                }(FFAppState().savedUserData.heightValue.toString(),
                                    FFAppState().savedUserData.heightUnit),
                                onTap: () async {
                                  logFirebaseEvent('p_detail_item_navigate_to');
                                  await context.pushNamed('personal_detail');
                                  safeSetState(() {});
                                },
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 1.5,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryText,
                              ),
                            ),
                            wrapWithModel(
                              model: _model.pDetailItemModel4,
                              updateCallback: () => safeSetState(() {}),
                              child: PDetailItemWidget(
                                title: valueOrDefault<String>(
                                  FFLocalizations.of(context).getVariableText(
                                    enText: 'Weight',
                                    heText: 'משקל',
                                  ),
                                  'Weight',
                                ),
                                valueData: (String var1, String var2) {
                                  return "$var1 $var2";
                                }(FFAppState().savedUserData.weightValue.toString(),
                                    FFAppState().savedUserData.weightUnit),
                                onTap: () async {
                                  logFirebaseEvent('p_detail_item_navigate_to');
                                  await context.pushNamed('personal_detail');
                                  safeSetState(() {});
                                },
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              height: 1.5,
                              decoration: BoxDecoration(
                                color: FlutterFlowTheme.of(context).secondaryText,
                              ),
                            ),
                          ].divide(SizedBox(height: 8.0)),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(
                        0.0,
                        0.0,
                        0.0,
                        valueOrDefault<double>(
                          FFAppState().bottomPadding + 30,
                          30.0,
                        )),
                    child: FFButtonWidget(
                      onPressed: () async {
                        var _shouldSetState = false;
                        logFirebaseEvent('Button_haptic_feedback');
                        HapticFeedback.lightImpact();
                        logFirebaseEvent('Button_backend_call');
                        _model.apiResultUpdateProfile =
                            await UpdateUserInfoCall.call(
                          accessToken: FFAppState().authToken,
                          jsonJson: functions
                              .getUserUpdateRequestJson(FFAppState().savedUserData),
                        );

                        _shouldSetState = true;
                        if ((_model.apiResultUpdateProfile?.succeeded ?? true)) {
                          logFirebaseEvent('Button_navigate_back');
                          context.safePop();
                          logFirebaseEvent('Button_show_snack_bar');
                          ScaffoldMessenger.of(context).clearSnackBars();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                FFLocalizations.of(context).getText(
                                  '6ajj0u1y' /* Your details updated successfully! */,
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'SFHebrew',
                                      color: FlutterFlowTheme.of(context).success,
                                      letterSpacing: 0.0,
                                    ),
                              ),
                              duration: Duration(milliseconds: 3000),
                              backgroundColor:
                                  FlutterFlowTheme.of(context).alternate,
                            ),
                          );
                        } else {
                          logFirebaseEvent('Button_show_snack_bar');
                          ScaffoldMessenger.of(context).clearSnackBars();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                getJsonField(
                                  (_model.apiResultUpdateProfile?.jsonBody ?? ''),
                                  r'''$.error''',
                                ).toString(),
                                style: FlutterFlowTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'SFHebrew',
                                      color: FlutterFlowTheme.of(context).error,
                                      letterSpacing: 0.0,
                                    ),
                              ),
                              duration: Duration(milliseconds: 3000),
                              backgroundColor:
                                  FlutterFlowTheme.of(context).alternate,
                            ),
                          );
                          if (_shouldSetState) safeSetState(() {});
                          return;
                        }

                        if (_shouldSetState) safeSetState(() {});
                      },
                      text: FFLocalizations.of(context).getText(
                        '6ajj0u2y' /* Update */,
                      ),
                      options: FFButtonOptions(
                        width: MediaQuery.sizeOf(context).width * 0.4,
                        height: 50.0,
                        padding:
                            EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                        iconPadding:
                            EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                        color: FlutterFlowTheme.of(context).primary,
                        textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                              fontFamily: 'SFHebrew',
                              color: Colors.white,
                              letterSpacing: 0.0,
                            ),
                        elevation: 0.0,
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
    );
  }
}

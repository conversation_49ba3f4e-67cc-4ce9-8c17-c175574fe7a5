// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class CaloriesStruct extends FFFirebaseStruct {
  CaloriesStruct({
    String? caloriesPercentage,
    int? caloriesRequired,
    int? caloriesContained,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _caloriesPercentage = caloriesPercentage,
        _caloriesRequired = caloriesRequired,
        _caloriesContained = caloriesContained,
        super(firestoreUtilData);

  // "calories_percentage" field.
  String? _caloriesPercentage;
  String get caloriesPercentage => _caloriesPercentage ?? '';
  set caloriesPercentage(String? val) => _caloriesPercentage = val;

  bool hasCaloriesPercentage() => _caloriesPercentage != null;

  // "calories_required" field.
  int? _caloriesRequired;
  int get caloriesRequired => _caloriesRequired ?? 0;
  set caloriesRequired(int? val) => _caloriesRequired = val;

  void incrementCaloriesRequired(int amount) =>
      caloriesRequired = caloriesRequired + amount;

  bool hasCaloriesRequired() => _caloriesRequired != null;

  // "calories_contained" field.
  int? _caloriesContained;
  int get caloriesContained => _caloriesContained ?? 0;
  set caloriesContained(int? val) => _caloriesContained = val;

  void incrementCaloriesContained(int amount) =>
      caloriesContained = caloriesContained + amount;

  bool hasCaloriesContained() => _caloriesContained != null;

  static CaloriesStruct fromMap(Map<String, dynamic> data) => CaloriesStruct(
        caloriesPercentage: castToType<String?>(data['calories_percentage']),
        caloriesRequired: castToType<int>(data['calories_required']),
        caloriesContained: castToType<int>(data['calories_contained']),
      );

  static CaloriesStruct? maybeFromMap(dynamic data) =>
      data is Map ? CaloriesStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'calories_percentage': _caloriesPercentage,
        'calories_required': _caloriesRequired,
        'calories_contained': _caloriesContained,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'calories_percentage': serializeParam(
          _caloriesPercentage,
          ParamType.String,
        ),
        'calories_required': serializeParam(
          _caloriesRequired,
          ParamType.int,
        ),
        'calories_contained': serializeParam(
          _caloriesContained,
          ParamType.int,
        ),
      }.withoutNulls;

  static CaloriesStruct fromSerializableMap(Map<String, dynamic> data) =>
      CaloriesStruct(
        caloriesPercentage: deserializeParam(
          data['calories_percentage'],
          ParamType.String,
          false,
        ),
        caloriesRequired: deserializeParam(
          data['calories_required'],
          ParamType.int,
          false,
        ),
        caloriesContained: deserializeParam(
          data['calories_contained'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'CaloriesStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is CaloriesStruct &&
        caloriesPercentage == other.caloriesPercentage &&
        caloriesRequired == other.caloriesRequired &&
        caloriesContained == other.caloriesContained;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([caloriesPercentage, caloriesRequired, caloriesContained]);
}

CaloriesStruct createCaloriesStruct({
  String? caloriesPercentage,
  int? caloriesRequired,
  int? caloriesContained,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    CaloriesStruct(
      caloriesPercentage: caloriesPercentage,
      caloriesRequired: caloriesRequired,
      caloriesContained: caloriesContained,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

CaloriesStruct? updateCaloriesStruct(
  CaloriesStruct? calories, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    calories
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addCaloriesStructData(
  Map<String, dynamic> firestoreData,
  CaloriesStruct? calories,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (calories == null) {
    return;
  }
  if (calories.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && calories.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final caloriesData = getCaloriesFirestoreData(calories, forFieldValue);
  final nestedData = caloriesData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = calories.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getCaloriesFirestoreData(
  CaloriesStruct? calories, [
  bool forFieldValue = false,
]) {
  if (calories == null) {
    return {};
  }
  final firestoreData = mapToFirestore(calories.toMap());

  // Add any Firestore field values
  calories.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getCaloriesListFirestoreData(
  List<CaloriesStruct>? caloriess,
) =>
    caloriess?.map((e) => getCaloriesFirestoreData(e, true)).toList() ?? [];

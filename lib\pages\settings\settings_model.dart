import '/backend/api_requests/api_calls.dart';
import '/componentes/language_switch/language_switch_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'settings_widget.dart' show SettingsWidget;
import 'package:flutter/material.dart';

class SettingsModel extends FlutterFlowModel<SettingsWidget> {
  ///  State fields for stateful widgets in this page.

  // Model for language_switch component.
  late LanguageSwitchModel languageSwitchModel;
  // Stores action output result for [Backend Call - API (User Logout)] action in Button widget.
  ApiCallResponse? logoutRes;
  // Stores action output result for [Backend Call - API (User Delete)] action in Button widget.
  ApiCallResponse? userDeleteRes;

  ApiCallResponse? apiResultUpdateProfile;

  @override
  void initState(BuildContext context) {
    languageSwitchModel = createModel(context, () => LanguageSwitchModel());
  }

  @override
  void dispose() {
    languageSwitchModel.dispose();
  }
}

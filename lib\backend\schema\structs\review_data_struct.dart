// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class ReviewDataStruct extends FFFirebaseStruct {
  ReviewDataStruct({
    int? id,
    String? username,
    String? note,
    double? rate,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _id = id,
        _username = username,
        _note = note,
        _rate = rate,
        super(firestoreUtilData);

  // "id" field.
  int? _id;
  int get id => _id ?? 0;
  set id(int? val) => _id = val;

  void incrementId(int amount) => id = id + amount;

  bool hasId() => _id != null;

  // "username" field.
  String? _username;
  String get username => _username ?? '';
  set username(String? val) => _username = val;

  bool hasUsername() => _username != null;

  // "note" field.
  String? _note;
  String get note => _note ?? '';
  set note(String? val) => _note = val;

  bool hasNote() => _note != null;

  // "rate" field.
  double? _rate;
  double get rate => _rate ?? 0.0;
  set rate(double? val) => _rate = val;

  void incrementRate(double amount) => rate = rate + amount;

  bool hasRate() => _rate != null;

  static ReviewDataStruct fromMap(Map<String, dynamic> data) =>
      ReviewDataStruct(
        id: castToType<int>(data['id']),
        username: castToType<String?>(data['username']),
        note: castToType<String?>(data['note']),
        rate: castToType<double>(data['rate']),
      );

  static ReviewDataStruct? maybeFromMap(dynamic data) => data is Map
      ? ReviewDataStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'id': _id,
        'username': _username,
        'note': _note,
        'rate': _rate,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'id': serializeParam(
          _id,
          ParamType.int,
        ),
        'username': serializeParam(
          _username,
          ParamType.String,
        ),
        'note': serializeParam(
          _note,
          ParamType.String,
        ),
        'rate': serializeParam(
          _rate,
          ParamType.double,
        ),
      }.withoutNulls;

  static ReviewDataStruct fromSerializableMap(Map<String, dynamic> data) =>
      ReviewDataStruct(
        id: deserializeParam(
          data['id'],
          ParamType.int,
          false,
        ),
        username: deserializeParam(
          data['username'],
          ParamType.String,
          false,
        ),
        note: deserializeParam(
          data['note'],
          ParamType.String,
          false,
        ),
        rate: deserializeParam(
          data['rate'],
          ParamType.double,
          false,
        ),
      );

  @override
  String toString() => 'ReviewDataStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is ReviewDataStruct &&
        id == other.id &&
        username == other.username &&
        note == other.note &&
        rate == other.rate;
  }

  @override
  int get hashCode => const ListEquality().hash([id, username, note, rate]);
}

ReviewDataStruct createReviewDataStruct({
  int? id,
  String? username,
  String? note,
  double? rate,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    ReviewDataStruct(
      id: id,
      username: username,
      note: note,
      rate: rate,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

ReviewDataStruct? updateReviewDataStruct(
  ReviewDataStruct? reviewData, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    reviewData
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addReviewDataStructData(
  Map<String, dynamic> firestoreData,
  ReviewDataStruct? reviewData,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (reviewData == null) {
    return;
  }
  if (reviewData.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && reviewData.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final reviewDataData = getReviewDataFirestoreData(reviewData, forFieldValue);
  final nestedData = reviewDataData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = reviewData.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getReviewDataFirestoreData(
  ReviewDataStruct? reviewData, [
  bool forFieldValue = false,
]) {
  if (reviewData == null) {
    return {};
  }
  final firestoreData = mapToFirestore(reviewData.toMap());

  // Add any Firestore field values
  reviewData.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getReviewDataListFirestoreData(
  List<ReviewDataStruct>? reviewDatas,
) =>
    reviewDatas?.map((e) => getReviewDataFirestoreData(e, true)).toList() ?? [];

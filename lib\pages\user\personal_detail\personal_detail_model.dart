import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/form_field_controller.dart';
import 'personal_detail_widget.dart' show PersonalDetailWidget;
import 'package:flutter/material.dart';

class PersonalDetailModel extends FlutterFlowModel<PersonalDetailWidget> {
  ///  Local state fields for this page.

  DateTime? dobDateTime;

  ///  State fields for stateful widgets in this page.

  final formKey = GlobalKey<FormState>();
  // State field(s) for gender widget.
  FormFieldController<String>? genderValueController;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}

  /// Additional helper methods.
  String? get genderValue => genderValueController?.value;
}

import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/backend/schema/enums/enums.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math';

class LineChartUI extends StatefulWidget {
  const LineChartUI({
    super.key,
    this.width,
    this.height,
    this.goalDuration,
    this.nutritionDuration,
    required this.isGoals,
    required this.foodOverviewDetail,
  });

  final double? width;
  final double? height;
  final GoalDuration? goalDuration;
  final NutritionDuration? nutritionDuration;
  final bool isGoals;
  final FoodOverviewDetailStruct foodOverviewDetail;

  @override
  State<LineChartUI> createState() => _LineChartUIState();
}

class _LineChartUIState extends State<LineChartUI> {
  int getGapDuration() {
    if (widget.isGoals) {
      switch (widget.goalDuration) {
        case GoalDuration.oneWeek:
          return 1;
        case GoalDuration.twoWeek:
          return 1;
        case GoalDuration.oneMonth:
          return 1;
        case GoalDuration.threeMonth:
          return 1;
        default:
          return 1;
      }
    } else {
      return 1; // Always show daily data for nutrition
    }
  }

  DateTime getStartDate() {
    final _now = DateTime.now();
    final now = DateTime(_now.year, _now.month, _now.day);

    if (widget.isGoals) {
      switch (widget.goalDuration) {
        case GoalDuration.oneWeek:
          return now.subtract(const Duration(days: 7));
        case GoalDuration.twoWeek:
          return now.subtract(const Duration(days: 14));
        case GoalDuration.oneMonth:
          return now.subtract(const Duration(days: 30));
        case GoalDuration.threeMonth:
          return now.subtract(const Duration(days: 90));
        default:
          return now.subtract(const Duration(days: 7));
      }
    } else {
      switch (widget.nutritionDuration) {
        case NutritionDuration.thisWeek:
          int daysSinceMonday = now.weekday - 1;
          return now.subtract(Duration(days: daysSinceMonday));
        case NutritionDuration.lastWeek:
          int daysSinceLastMonday = now.weekday - 1 + 7;
          return now.subtract(Duration(days: daysSinceLastMonday));
        case NutritionDuration.twoWeekAgo:
          int daysSinceTwoWeeksAgoMonday = now.weekday - 1 + 14;
          return now.subtract(Duration(days: daysSinceTwoWeeksAgoMonday));
        case NutritionDuration.threeWeekAgo:
          int daysSinceThreeWeeksAgoMonday = now.weekday - 1 + 21;
          return now.subtract(Duration(days: daysSinceThreeWeeksAgoMonday));
        default:
          return now.subtract(const Duration(days: 7));
      }
    }
  }

  DateTime getEndDate() {
    final _now = DateTime.now();
    final now = DateTime(_now.year, _now.month, _now.day);

    if (widget.isGoals) {
      return now;
    } else {
      switch (widget.nutritionDuration) {
        case NutritionDuration.thisWeek:
          int daysUntilSunday = 7 - now.weekday;
          return now.add(Duration(days: daysUntilSunday));
        case NutritionDuration.lastWeek:
          int daysSinceLastMonday = now.weekday - 1 + 7;
          return now
              .subtract(Duration(days: daysSinceLastMonday))
              .add(Duration(days: 6));
        case NutritionDuration.twoWeekAgo:
          int daysSinceTwoWeeksAgoMonday = now.weekday - 1 + 14;
          return now
              .subtract(Duration(days: daysSinceTwoWeeksAgoMonday))
              .add(Duration(days: 6));
        case NutritionDuration.threeWeekAgo:
          int daysSinceThreeWeeksAgoMonday = now.weekday - 1 + 21;
          return now
              .subtract(Duration(days: daysSinceThreeWeeksAgoMonday))
              .add(Duration(days: 6));
        default:
          return now;
      }
    }
  }

  List<String> getDates() {
    List<DateTime?> allDates = [];
    final DateTime startDate = getStartDate();
    final DateTime endDate = getEndDate();

    print('Nutrition Duration: ${widget.nutritionDuration}');
    print('Start Date: $startDate');
    print('End Date: $endDate');

    if (widget.isGoals) {
      allDates = widget.foodOverviewDetail.goals
          .where((g) {
            final scanDate = DateTime.tryParse(g.scanDate)?.toLocal();
            if (scanDate == null) return false;
            final normalizedScanDate =
                DateTime(scanDate.year, scanDate.month, scanDate.day);
            final normalizedStartDate =
                DateTime(startDate.year, startDate.month, startDate.day);
            final normalizedEndDate =
                DateTime(endDate.year, endDate.month, endDate.day);
            return (normalizedScanDate.isAfter(normalizedStartDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedStartDate)) &&
                (normalizedScanDate.isBefore(normalizedEndDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedEndDate));
          })
          .map((g) => DateTime.tryParse(g.scanDate)?.toLocal())
          .toList();
    } else {
      Set<DateTime> uniqueDates = {};

      uniqueDates.addAll(widget.foodOverviewDetail.nutritions.proteins
          .where((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            if (scanDate == null) return false;
            final normalizedScanDate =
                DateTime(scanDate.year, scanDate.month, scanDate.day);
            final normalizedStartDate =
                DateTime(startDate.year, startDate.month, startDate.day);
            final normalizedEndDate =
                DateTime(endDate.year, endDate.month, endDate.day);
            return (normalizedScanDate.isAfter(normalizedStartDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedStartDate)) &&
                (normalizedScanDate.isBefore(normalizedEndDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedEndDate));
          })
          .map((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            return scanDate != null
                ? DateTime(scanDate.year, scanDate.month, scanDate.day)
                : null;
          })
          .where((date) => date != null)
          .cast<DateTime>());

      uniqueDates.addAll(widget.foodOverviewDetail.nutritions.carbs
          .where((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            if (scanDate == null) return false;
            final normalizedScanDate =
                DateTime(scanDate.year, scanDate.month, scanDate.day);
            final normalizedStartDate =
                DateTime(startDate.year, startDate.month, startDate.day);
            final normalizedEndDate =
                DateTime(endDate.year, endDate.month, endDate.day);
            return (normalizedScanDate.isAfter(normalizedStartDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedStartDate)) &&
                (normalizedScanDate.isBefore(normalizedEndDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedEndDate));
          })
          .map((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            return scanDate != null
                ? DateTime(scanDate.year, scanDate.month, scanDate.day)
                : null;
          })
          .where((date) => date != null)
          .cast<DateTime>());

      uniqueDates.addAll(widget.foodOverviewDetail.nutritions.fats
          .where((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            if (scanDate == null) return false;
            final normalizedScanDate =
                DateTime(scanDate.year, scanDate.month, scanDate.day);
            final normalizedStartDate =
                DateTime(startDate.year, startDate.month, startDate.day);
            final normalizedEndDate =
                DateTime(endDate.year, endDate.month, endDate.day);
            return (normalizedScanDate.isAfter(normalizedStartDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedStartDate)) &&
                (normalizedScanDate.isBefore(normalizedEndDate) ||
                    normalizedScanDate.isAtSameMomentAs(normalizedEndDate));
          })
          .map((n) {
            final scanDate = DateTime.tryParse(n.scanDate)?.toLocal();
            return scanDate != null
                ? DateTime(scanDate.year, scanDate.month, scanDate.day)
                : null;
          })
          .where((date) => date != null)
          .cast<DateTime>());

      allDates = uniqueDates.toList();
    }

    List<DateTime> completeDates = [];
    for (DateTime date = startDate;
        date.isBefore(endDate.add(const Duration(days: 1)));
        date = date.add(const Duration(days: 1))) {
      completeDates.add(date);
      if (!(allDates.any((_date) =>
          _date != null &&
          date.year == _date.year &&
          date.month == _date.month &&
          date.day == _date.day))) {
        allDates.add(date);
      }
    }

    final gap = getGapDuration();
    allDates.removeWhere((date) => date == null);
    allDates.sort((a, b) => a!.compareTo(b!));
    List<DateTime?> data = allDates
        .asMap()
        .entries
        .where((entry) => entry.key % gap == 0)
        .map((e) => e.value)
        .toList();

    final result = data
        .where((element) => element != null)
        .toList()
        .map((date) => date!.toIso8601String())
        .toList();
    print('Filtered Dates: $result');
    return result;
  }

  List<List<double>> getData() {
    if (widget.isGoals) {
      List<double> allData = [];
      final DateTime startDate = getStartDate();
      final DateTime endDate = getEndDate();

      List<DateTime> completeDates = [];
      for (DateTime date = startDate;
          date.isBefore(endDate.add(const Duration(days: 1)));
          date = date.add(const Duration(days: 1))) {
        completeDates.add(date);
      }

      Map<DateTime, double> dataMap = Map.fromIterable(
        completeDates,
        key: (date) => date,
        value: (date) => 0.0,
      );

      for (var goal in widget.foodOverviewDetail.dailyLogs) {
        DateTime? _goalDate = DateTime.tryParse(goal.logDate)?.toLocal();
        if (_goalDate != null) {
          DateTime? goalDate =
              DateTime(_goalDate.year, _goalDate.month, _goalDate.day);
          final normalizedStartDate =
              DateTime(startDate.year, startDate.month, startDate.day);
          final normalizedEndDate =
              DateTime(endDate.year, endDate.month, endDate.day);
          if ((goalDate.isAfter(normalizedStartDate) ||
                  goalDate.isAtSameMomentAs(normalizedStartDate)) &&
              (goalDate.isBefore(normalizedEndDate) ||
                  goalDate.isAtSameMomentAs(normalizedEndDate))) {
            double loggedWeight = goal.totalWeight.toDouble();
            if (loggedWeight > 0) {
              dataMap[goalDate] = loggedWeight;
            }
          }
        }
      }

      double lastKnownWeight = 0.0;
      allData = completeDates.map((date) {
        if (dataMap[date] != null && dataMap[date]! > 0) {
          lastKnownWeight = dataMap[date]!;
          return lastKnownWeight;
        }
        return lastKnownWeight;
      }).toList();
      return [allData];
    } else {
      List<DateTime> completeDates = [];
      final DateTime startDate = getStartDate();
      final DateTime endDate = getEndDate();

      for (DateTime date = startDate;
          date.isBefore(endDate.add(const Duration(days: 1)));
          date = date.add(const Duration(days: 1))) {
        completeDates.add(date);
      }

      Map<DateTime, double> proteinMap = Map.fromIterable(
        completeDates,
        key: (date) => date,
        value: (date) => 0.0,
      );
      Map<DateTime, double> caloriesMap = Map.fromIterable(
        completeDates,
        key: (date) => date,
        value: (date) => 0.0,
      );
      Map<DateTime, double> carbMap = Map.fromIterable(
        completeDates,
        key: (date) => date,
        value: (date) => 0.0,
      );
      Map<DateTime, double> fatMap = Map.fromIterable(
        completeDates,
        key: (date) => date,
        value: (date) => 0.0,
      );

      for (var protein in widget.foodOverviewDetail.nutritions.proteins) {
        DateTime? _proteinDate = DateTime.tryParse(protein.scanDate)?.toLocal();
        if (_proteinDate != null) {
          DateTime? proteinDate =
              DateTime(_proteinDate.year, _proteinDate.month, _proteinDate.day);
          final normalizedStartDate =
              DateTime(startDate.year, startDate.month, startDate.day);
          final normalizedEndDate =
              DateTime(endDate.year, endDate.month, endDate.day);
          if ((proteinDate.isAfter(normalizedStartDate) ||
                  proteinDate.isAtSameMomentAs(normalizedStartDate)) &&
              (proteinDate.isBefore(normalizedEndDate) ||
                  proteinDate.isAtSameMomentAs(normalizedEndDate))) {
            proteinMap[proteinDate] = protein.totalProteinsPerDay.toDouble();
            print(
                'Protein Data - Date: $proteinDate, Value: ${protein.totalProteinsPerDay}');
          }
        }
      }

      for (var calories in widget.foodOverviewDetail.nutritions.calories) {
        DateTime? _caloriesDate =
            DateTime.tryParse(calories.scanDate)?.toLocal();
        if (_caloriesDate != null) {
          DateTime? caloriesDate = DateTime(
              _caloriesDate.year, _caloriesDate.month, _caloriesDate.day);
          final normalizedStartDate =
              DateTime(startDate.year, startDate.month, startDate.day);
          final normalizedEndDate =
              DateTime(endDate.year, endDate.month, endDate.day);
          if ((caloriesDate.isAfter(normalizedStartDate) ||
                  caloriesDate.isAtSameMomentAs(normalizedStartDate)) &&
              (caloriesDate.isBefore(normalizedEndDate) ||
                  caloriesDate.isAtSameMomentAs(normalizedEndDate))) {
            caloriesMap[caloriesDate] = calories.totalCaloriesPerDay.toDouble();
            print(
                'Calories Data - Date: $caloriesDate, Value: ${calories.totalCaloriesPerDay}');
          }
        }
      }

      for (var carb in widget.foodOverviewDetail.nutritions.carbs) {
        DateTime? _carbDate = DateTime.tryParse(carb.scanDate)?.toLocal();
        if (_carbDate != null) {
          DateTime? carbDate =
              DateTime(_carbDate.year, _carbDate.month, _carbDate.day);
          final normalizedStartDate =
              DateTime(startDate.year, startDate.month, startDate.day);
          final normalizedEndDate =
              DateTime(endDate.year, endDate.month, endDate.day);
          if ((carbDate.isAfter(normalizedStartDate) ||
                  carbDate.isAtSameMomentAs(normalizedStartDate)) &&
              (carbDate.isBefore(normalizedEndDate) ||
                  carbDate.isAtSameMomentAs(normalizedEndDate))) {
            carbMap[carbDate] = carb.totalCarbsPerDay.toDouble();
            print(
                'Carb Data - Date: $carbDate, Value: ${carb.totalCarbsPerDay}');
          }
        }
      }

      for (var fat in widget.foodOverviewDetail.nutritions.fats) {
        DateTime? _fatDate = DateTime.tryParse(fat.scanDate)?.toLocal();
        if (_fatDate != null) {
          DateTime? fatDate =
              DateTime(_fatDate.year, _fatDate.month, _fatDate.day);
          final normalizedStartDate =
              DateTime(startDate.year, startDate.month, startDate.day);
          final normalizedEndDate =
              DateTime(endDate.year, endDate.month, endDate.day);
          if ((fatDate.isAfter(normalizedStartDate) ||
                  fatDate.isAtSameMomentAs(normalizedStartDate)) &&
              (fatDate.isBefore(normalizedEndDate) ||
                  fatDate.isAtSameMomentAs(normalizedEndDate))) {
            fatMap[fatDate] = fat.totalFatsPerDay.toDouble();
            print('Fat Data - Date: $fatDate, Value: ${fat.totalFatsPerDay}');
          }
        }
      }

      List<double> proteinData = completeDates
          .map((date) => proteinMap[date]?.toDouble() ?? 0.0)
          .toList();
      List<double> caloriesData = completeDates
          .map((date) => caloriesMap[date]?.toDouble() ?? 0.0)
          .toList();
      List<double> carbData = completeDates
          .map((date) => carbMap[date]?.toDouble() ?? 0.0)
          .toList();
      List<double> fatData =
          completeDates.map((date) => fatMap[date]?.toDouble() ?? 0.0).toList();

      print('Protein Data: $proteinData');
      print('Calories Data: $caloriesData');
      print('Carb Data: $carbData');
      print('Fat Data: $fatData');

      // return [caloriesData, proteinData, carbData, fatData];
      return [caloriesData];
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    final dates = getDates();
    final dataLists = getData();

    print('Dates: $dates');
    print('Data Lists: $dataLists');

    if (dates.isEmpty || dataLists.isEmpty || dataLists.first.isEmpty) {
      return const Center(child: Text('No data available'));
    }

    String weightUnit = FFAppState().savedUserData.weightUnit ?? 'g';
    double maxY = 0;
    if (widget.isGoals) {
      maxY = dataLists[0].reduce((a, b) => a > b ? a : b);
      maxY = max(maxY, 100);
    } else {
      for (int i = 0; i < dataLists[0].length; i++) {
        // double dailyTotal = dataLists[0][i] + dataLists[1][i] + dataLists[2][i];
        double dailyTotal = dataLists[0][i];
        maxY = max(maxY, dailyTotal);
      }
    }

    final int interval = (dates.length / 10).ceil();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: widget.width ?? double.infinity,
            height: widget.height ?? 200.0,
            child: widget.isGoals
                ? LineChart(
                    LineChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: true,
                        drawHorizontalLine: true,
                        horizontalInterval: maxY > 10 ? maxY / 5 : 1,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            dashArray: [5, 5],
                            color: Colors.grey.withOpacity(0.5),
                            strokeWidth: 1,
                          );
                        },
                        getDrawingVerticalLine: (value) {
                          return FlLine(
                            dashArray: [5, 5],
                            color: Colors.grey.withOpacity(0.1),
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              if (value < 0) return const SizedBox.shrink();
                              return Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: Text(
                                  value.toInt().toString(),
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                ),
                              );
                            },
                            reservedSize: 50,
                            interval: maxY > 10 ? maxY / 5 : 1,
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            interval: 2, // Show every 2nd date to create space
                            getTitlesWidget: (value, meta) {
                              final int index = value.toInt();
                              if (index >= 0 && index < dates.length) {
                                final dateFormat = widget.goalDuration ==
                                        GoalDuration.threeMonth
                                    ? DateFormat(
                                        'd/M', isEnglish ? 'en_US' : 'he_IL')
                                    : DateFormat('dd MMM',
                                        isEnglish ? 'en_US' : 'he_IL');
                                return SideTitleWidget(
                                  axisSide: meta.axisSide,
                                  angle: getLabelRotation(),
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8),
                                    child: Text(
                                      dateFormat
                                          .format(DateTime.parse(dates[index])),
                                      style: const TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                            reservedSize: 30,
                          ),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: false),
                      minY: 0,
                      maxY: maxY + (maxY * 0.2),
                      lineTouchData: LineTouchData(
                        enabled: true,
                        touchTooltipData: LineTouchTooltipData(
                          getTooltipItems: (touchedSpots) {
                            return touchedSpots.map((spot) {
                              return LineTooltipItem(
                                '${spot.y.toStringAsFixed(0)} ${isEnglish ? weightUnit : 'קילוגרם'}',
                                const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              );
                            }).toList();
                          },
                          tooltipRoundedRadius: 8,
                          tooltipPadding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                        ),
                      ),
                      extraLinesData: ExtraLinesData(
                        horizontalLines: [
                          HorizontalLine(
                            y: 120,
                            color: Colors.grey.withOpacity(0.5),
                            strokeWidth: 1,
                            dashArray: [5, 5],
                            // label: HorizontalLineLabel(
                            //   show: true,
                            //   alignment: Alignment.topRight,
                            //   padding:
                            //       const EdgeInsets.only(right: 5, bottom: 5),
                            //   style: const TextStyle(
                            //     color: Colors.grey,
                            //     fontSize: 12,
                            //   ),
                            //   labelResolver: (line) => 'Goal: 2000 Cal',
                            // ),
                          ),
                        ],
                      ),
                      lineBarsData: [
                        LineChartBarData(
                          preventCurveOverShooting: true,
                          spots: List.generate(
                            dates.length,
                            (index) => FlSpot(
                              index.toDouble(),
                              max(0, dataLists[0][index]),
                            ),
                          ),
                          isCurved: true,
                          curveSmoothness: 0.35,
                          // color: Colors.black,
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFFA18CD1), // Lavender-purple
                              Color(0xFFFF5F6D), // Vibrant pink-red
                              Color(0xFFFFC371), // Warm orange-yellow
                              Color(0xFF47E5BC), // Aqua mint
                              Color(0xFF6A82FB), // Vivid purple-blue
                              Color(0xFFFEC163), // Gold-yellow
                              Color(0xFFFD6585), // Coral pink
                              Color(0xFFA18CD1), // Lavender-purple
                              Color(0xFFFF61A6), // Hot pink
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          barWidth: 2,
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, bar, index) =>
                                FlDotCirclePainter(
                              radius: 2,
                              color: Colors.grey,
                              strokeWidth: 1,
                              strokeColor: Colors.white,
                            ),
                          ),
                          belowBarData: BarAreaData(
                              show: true,
                              gradient: LinearGradient(
                                  colors: [
                                    Colors.grey.shade200,
                                    Colors.grey.shade100,
                                    Colors.grey.shade50
                                  ],
                                  end: Alignment.bottomCenter,
                                  begin: Alignment.topCenter)),
                        ),
                      ],
                    ),
                  )
                : BarChart(
                    BarChartData(
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: false,
                        drawHorizontalLine: true,
                        horizontalInterval: maxY > 10 ? maxY / 5 : 1,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: Colors.grey.shade300,
                            strokeWidth: 1,
                          );
                        },
                      ),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              return Container(
                                padding:
                                    const EdgeInsets.only(left: 2, right: 2),
                                child: Text(
                                  value < 0
                                      ? "0"
                                      : "${value.toStringAsFixed(0)} Cal",
                                  style: const TextStyle(fontSize: 10),
                                ),
                              );
                            },
                            reservedSize: 45,
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              final int index = value.toInt();
                              if (index >= 0 && index < dates.length) {
                                return Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Text(
                                    DateFormat(
                                            'E', isEnglish ? 'en_US' : 'he_IL')
                                        .format(DateTime.parse(dates[index])),
                                    style: const TextStyle(fontSize: 10),
                                  ),
                                );
                              }
                              return const SizedBox.shrink();
                            },
                          ),
                          drawBelowEverything: true,
                        ),
                        topTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                        rightTitles: const AxisTitles(
                            sideTitles: SideTitles(showTitles: false)),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: const Border(
                          left: BorderSide(color: Colors.black, width: 1),
                          bottom: BorderSide(color: Colors.black, width: 1),
                          top: BorderSide.none,
                          right: BorderSide.none,
                        ),
                      ),
                      minY: 0,
                      maxY: maxY + (maxY * 0.1),
                      barGroups: List.generate(
                        dates.length,
                        (index) => BarChartGroupData(
                          x: index,
                          barRods: [
                            BarChartRodData(
                              toY: max(0, dataLists[0][index]),
                              rodStackItems: [
                                BarChartRodStackItem(
                                  0,
                                  max(0, dataLists[0][index]),
                                  _getBarColor(
                                      index), // Use a function to get a unique color
                                ),
                              ],
                              width: 20,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4),
                              ),
                              borderSide: const BorderSide(
                                color: Colors.white,
                                width: 0.5,
                              ),
                              backDrawRodData: BackgroundBarChartRodData(
                                show: true,
                                toY: max(0, dataLists[0][index]),
                                color: Colors.black.withOpacity(0.15),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          // if (!widget.isGoals)
          //   Padding(
          //     padding: const EdgeInsets.only(top: 10.0),
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         Row(
          //           children: [
          //             Container(
          //               width: 12,
          //               height: 12,
          //               decoration: const BoxDecoration(
          //                 color: Color(0xFFFF5C5C),
          //                 shape: BoxShape.circle,
          //               ),
          //             ),
          //             const SizedBox(width: 4),
          //             Text(
          //               isEnglish ? 'proteins' : "חלבון",
          //               style: const TextStyle(
          //                 fontSize: 12,
          //                 color: Colors.black,
          //               ),
          //             ),
          //           ],
          //         ),
          //         const SizedBox(width: 16),
          //         Row(
          //           children: [
          //             Container(
          //               width: 12,
          //               height: 12,
          //               decoration: const BoxDecoration(
          //                 color: Color(0xFFFFD54F),
          //                 shape: BoxShape.circle,
          //               ),
          //             ),
          //             const SizedBox(width: 4),
          //             Text(
          //               FFLocalizations.of(context).getText(
          //                 '2ia5fkbm' /* Carbs */,
          //               ),
          //               style: const TextStyle(
          //                 fontSize: 12,
          //                 color: Colors.black,
          //               ),
          //             ),
          //           ],
          //         ),
          //         const SizedBox(width: 16),
          //         Row(
          //           children: [
          //             Container(
          //               width: 12,
          //               height: 12,
          //               decoration: const BoxDecoration(
          //                 color: Color(0xFF4D9FFF),
          //                 shape: BoxShape.circle,
          //               ),
          //             ),
          //             const SizedBox(width: 4),
          //             Text(
          //               FFLocalizations.of(context).getText(
          //                 'gwkjxrgf' /* Fats */,
          //               ),
          //               style: const TextStyle(
          //                 fontSize: 12,
          //                 color: Colors.black,
          //               ),
          //             ),
          //           ],
          //         ),
          //       ],
          //     ),
          //   ),
        ],
      ),
    );
  }

  double getLabelRotation() {
    if (widget.isGoals && widget.goalDuration == GoalDuration.oneWeek) {
      return 0; // No rotation for one week
    }
    return valueToRadian(45); // 45 degrees for other durations
  }

  double valueToRadian(double degree) => degree * (pi / 180);

  double getBottomTitleInterval() {
    if (widget.isGoals) {
      switch (widget.goalDuration) {
        case GoalDuration.oneWeek:
          return 1;
        case GoalDuration.twoWeek:
          return 2;
        case GoalDuration.oneMonth:
          return 5;
        case GoalDuration.threeMonth:
          return 10;
        default:
          return 2;
      }
    }
    return 2;
  }

  Color _getBarColor(int index) {
    const List<Color> colors = [
      Color(0xFFA18CD1), // Lavender-purple
      Color(0xFFFF5F6D), // Vibrant pink-red
      Color(0xFFFFC371), // Warm orange-yellow
      Color(0xFF47E5BC), // Aqua mint
      Color(0xFF6A82FB), // Vivid purple-blue
      Color(0xFFFEC163), // Gold-yellow
      Color(0xFFFD6585), // Coral pink
      Color(0xFFA18CD1), // Lavender-purple
      Color(0xFFFF61A6), // Hot pink
    ];
    return colors[index % colors.length];
  }
}

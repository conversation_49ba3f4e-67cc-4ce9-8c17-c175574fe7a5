// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class GoalsStruct extends FFFirebaseStruct {
  GoalsStruct({
    String? scanDate,
    int? totalCaloriesPerDay,
    int? mealCount,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _scanDate = scanDate,
        _totalCaloriesPerDay = totalCaloriesPerDay,
        _mealCount = mealCount,
        super(firestoreUtilData);

  // "scan_date" field.
  String? _scanDate;
  String get scanDate => _scanDate ?? '';
  set scanDate(String? val) => _scanDate = val;

  bool hasScanDate() => _scanDate != null;

  // "total_calories_per_day" field.
  int? _totalCaloriesPerDay;
  int get totalCaloriesPerDay => _totalCaloriesPerDay ?? 0;
  set totalCaloriesPerDay(int? val) => _totalCaloriesPerDay = val;

  void incrementTotalCaloriesPerDay(int amount) =>
      totalCaloriesPerDay = totalCaloriesPerDay + amount;

  bool hasTotalCaloriesPerDay() => _totalCaloriesPerDay != null;

  // "meal_count" field.
  int? _mealCount;
  int get mealCount => _mealCount ?? 0;
  set mealCount(int? val) => _mealCount = val;

  void incrementMealCount(int amount) => mealCount = mealCount + amount;

  bool hasMealCount() => _mealCount != null;

  static GoalsStruct fromMap(Map<String, dynamic> data) => GoalsStruct(
        scanDate: castToType<String?>(data['scan_date']),
        totalCaloriesPerDay: castToType<int>(data['total_calories_per_day']),
        mealCount: castToType<int>(data['meal_count']),
      );

  static GoalsStruct? maybeFromMap(dynamic data) =>
      data is Map ? GoalsStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'scan_date': _scanDate,
        'total_calories_per_day': _totalCaloriesPerDay,
        'meal_count': _mealCount,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'scan_date': serializeParam(
          _scanDate,
          ParamType.String,
        ),
        'total_calories_per_day': serializeParam(
          _totalCaloriesPerDay,
          ParamType.int,
        ),
        'meal_count': serializeParam(
          _mealCount,
          ParamType.int,
        ),
      }.withoutNulls;

  static GoalsStruct fromSerializableMap(Map<String, dynamic> data) =>
      GoalsStruct(
        scanDate: deserializeParam(
          data['scan_date'],
          ParamType.String,
          false,
        ),
        totalCaloriesPerDay: deserializeParam(
          data['total_calories_per_day'],
          ParamType.int,
          false,
        ),
        mealCount: deserializeParam(
          data['meal_count'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'GoalsStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is GoalsStruct &&
        scanDate == other.scanDate &&
        totalCaloriesPerDay == other.totalCaloriesPerDay &&
        mealCount == other.mealCount;
  }

  @override
  int get hashCode =>
      const ListEquality().hash([scanDate, totalCaloriesPerDay, mealCount]);
}

GoalsStruct createGoalsStruct({
  String? scanDate,
  int? totalCaloriesPerDay,
  int? mealCount,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    GoalsStruct(
      scanDate: scanDate,
      totalCaloriesPerDay: totalCaloriesPerDay,
      mealCount: mealCount,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

GoalsStruct? updateGoalsStruct(
  GoalsStruct? goals, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    goals
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addGoalsStructData(
  Map<String, dynamic> firestoreData,
  GoalsStruct? goals,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (goals == null) {
    return;
  }
  if (goals.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && goals.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final goalsData = getGoalsFirestoreData(goals, forFieldValue);
  final nestedData = goalsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = goals.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getGoalsFirestoreData(
  GoalsStruct? goals, [
  bool forFieldValue = false,
]) {
  if (goals == null) {
    return {};
  }
  final firestoreData = mapToFirestore(goals.toMap());

  // Add any Firestore field values
  goals.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getGoalsListFirestoreData(
  List<GoalsStruct>? goalss,
) =>
    goalss?.map((e) => getGoalsFirestoreData(e, true)).toList() ?? [];

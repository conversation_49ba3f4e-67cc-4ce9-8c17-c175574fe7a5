import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:pdfx/pdfx.dart';

import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'teams_privacy_model.dart';
export 'teams_privacy_model.dart';

class TeamsPrivacyWidget extends StatefulWidget {
  const TeamsPrivacyWidget({
    super.key,
    bool? privacy,
    required this.url,
  }) : privacy = privacy ?? false;

  final bool privacy;
  final String? url;

  @override
  State<TeamsPrivacyWidget> createState() => _TeamsPrivacyWidgetState();
}

class _TeamsPrivacyWidgetState extends State<TeamsPrivacyWidget> {
  late TeamsPrivacyModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  static const int _initialPage = 1;
  late PdfController _pdfController;
  String pdfPath = 'assets/privacy_policy.pdf';
  int _currentPage = _initialPage;
  int _totalPages = 0;
  bool _showSwipeHint = true;
  bool isEnglish = false;

  @override
  void initState() {
    pdfPath = widget.privacy
        ? 'assets/privacy_policy.pdf'
        : 'assets/terms_of_service.pdf';
    super.initState();
    _model = createModel(context, () => TeamsPrivacyModel());
    _pdfController = PdfController(
      document: PdfDocument.openAsset(pdfPath),
      initialPage: _initialPage,
    );

    /// Hide swipe hint after 5 seconds
    Future.delayed(Duration(seconds: 3), () {
      if (mounted) {
        setState(() => _showSwipeHint = false);
      }
    });
  }

  @override
  void dispose() {
    _model.dispose();
    _pdfController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'TermsPrivacy',
        builder: (context) => GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        appBar: AppBar(
          backgroundColor: FlutterFlowTheme.of(context).alternate,
          automaticallyImplyLeading: false,
          actions: [
            IconButton(
              icon: const Icon(Icons.navigate_before),
              onPressed: () {
                _pdfController.previousPage(
                  curve: Curves.ease,
                  duration: const Duration(milliseconds: 100),
                );
              },
            ),
            PdfPageNumber(
              controller: _pdfController,
              builder: (_, loadingState, page, pagesCount) {
                _currentPage = page;
                _totalPages = pagesCount ?? 0;
                return Container(
                  alignment: Alignment.center,
                  child: Text(
                    '$page/$pagesCount',
                    style: const TextStyle(fontSize: 22),
                  ),
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.navigate_next),
              onPressed: () {
                _pdfController.nextPage(
                  curve: Curves.ease,
                  duration: const Duration(milliseconds: 100),
                );
              },
            ),
          ],
          flexibleSpace: FlexibleSpaceBar(
            titlePadding: const EdgeInsets.all(0),
            title: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(
                0.0,
                valueOrDefault<double>(
                  FFAppState().topPadding + 16,
                  0.0,
                ),
                0.0,
                0.0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  FlutterFlowIconButton(
                    borderColor: Colors.transparent,
                    borderRadius: 30.0,
                    borderWidth: 1.0,
                    buttonSize: 50.0,
                    icon: Icon(
                      Icons.chevron_left_rounded,
                      color: FlutterFlowTheme.of(context).primary,
                      size: 30.0,
                    ),
                    onPressed: () async {
                      logFirebaseEvent('IconButton_navigate_back');
                      context.pop();
                    },
                  ),
                  Expanded(
                    child: Text(
                      valueOrDefault<String>(
                        widget.privacy
                            ? valueOrDefault<String>(
                                FFLocalizations.of(context).getVariableText(
                                  enText: 'Privacy',
                                  heText: 'פרטיות',
                                ),
                                'Privacy',
                              )
                            : valueOrDefault<String>(
                                FFLocalizations.of(context).getVariableText(
                                  enText: 'Terms',
                                  heText: 'תנאים',
                                ),
                                'Terms',
                              ),
                        'Terms & Privacy',
                      ),
                      style:
                          FlutterFlowTheme.of(context).headlineMedium.override(
                                fontFamily: 'SFHebrew',
                                color: FlutterFlowTheme.of(context).primary,
                                fontSize: 16.0,
                                letterSpacing: 0.0,
                              ),
                    ),
                  ),
                ],
              ),
            ),
            centerTitle: false,
            expandedTitleScale: 1.0,
          ),
          toolbarHeight: 80.0,
          elevation: 2.0,
        ),
        body: Stack(
          children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).secondaryBackground,
              ),
              child: PdfView(
                controller: _pdfController,
                scrollDirection: Axis.horizontal,
                pageSnapping: false,
                physics: const BouncingScrollPhysics(),
                builders: PdfViewBuilders<DefaultBuilderOptions>(
                  options: const DefaultBuilderOptions(),
                  documentLoaderBuilder: (_) => const Center(
                    child: SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressWidget(),
                    ),
                  ),
                  pageLoaderBuilder: (_) => const Center(
                    child: SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressWidget(),
                    ),
                  ),
                  pageBuilder: (context, pageImage, index, document) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: PdfPageImageProvider(
                        pageImage,
                        index,
                        document.id,
                      ),
                      minScale: PhotoViewComputedScale.contained * 1.0,
                      maxScale: PhotoViewComputedScale.covered * 2.0,
                      initialScale: PhotoViewComputedScale.contained * 1.2,
                      heroAttributes:
                          PhotoViewHeroAttributes(tag: '${document.id}-$index'),
                    );
                  },
                ),
                onPageChanged: (page) {
                  setState(() {
                    _currentPage = page;
                    _showSwipeHint = false;
                  });
                },
              ),
            ),

            /// Dot Indicator
            if (_totalPages > 1)
              Positioned(
                bottom: 16.0,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _totalPages,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4.0),
                      width: _currentPage == index + 1 ? 12.0 : 8.0,
                      height: 8.0,
                      decoration: BoxDecoration(
                        color: _currentPage == index + 1
                            ? FlutterFlowTheme.of(context).primary
                            : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              ),

            /// Swipe Hint
            if (_showSwipeHint)
              Positioned(
                bottom: 60.0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8.0),
                  margin: const EdgeInsets.symmetric(horizontal: 20.0),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Text(
                    isEnglish
                        ? 'Swipe left or right to navigate pages'
                        : "החלק שמאלה או ימינה כדי לנווט בין הדפים",
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'SFHebrew',
                          color: Colors.white,
                          fontSize: 14.0,
                        ),
                  ),
                ),
              ),
          ],
        ),
      ),
    ));
  }
}

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.calcountai.app"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!--    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>-->
    <!--    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />-->
    <!--    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"-->
    <!--        tools:node="remove"/>-->
    <!--    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>-->
    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="https" />
        </intent>
    </queries>
    <application
        android:label="קל-קאונטי"
        tools:replace="android:label"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true">

        <meta-data
            android:name="com.posthog.posthog.API_KEY"
            android:value="phc_CzDzxMZ3I199ik5QYyIUKYIYycFD8qopW8Mk5KRuUSP" />
        <meta-data
            android:name="com.posthog.posthog.POSTHOG_HOST"
            android:value="https://us.i.posthog.com" />
        <meta-data
            android:name="com.posthog.posthog.TRACK_APPLICATION_LIFECYCLE_EVENTS"
            android:value="true" />
        <meta-data
            android:name="com.posthog.posthog.DEBUG"
            android:value="true" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <!-- Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame. -->
            <meta-data
              android:name="io.flutter.embedding.android.SplashScreenDrawable"
              android:resource="@drawable/launch_background"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <!-- Deep linking -->
            <meta-data android:name="flutter_deeplinking_enabled" android:value="true" />
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="calcountiai" android:host="calcountiai.com" />
            </intent-filter>

        </activity>

        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />



    </application>
</manifest>

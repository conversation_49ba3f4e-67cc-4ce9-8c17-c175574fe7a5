import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/app_state.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/internationalization.dart';
import 'package:flutter/material.dart';

class EditMealName extends StatefulWidget {
  final String? currentName;

  const EditMealName({super.key, this.currentName});

  @override
  State<EditMealName> createState() => _EditMealNameState();
}

class _EditMealNameState extends State<EditMealName> {
  late TextEditingController _nameController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.currentName ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'EditMeal',
        builder: (context) => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).grey,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
          ),
          backgroundColor: Colors.white,
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isEnglish ? 'Edit Meal Name' : 'ערוך שם ארוחה',
                  style: FlutterFlowTheme.of(context).headlineLarge.override(
                        fontFamily: 'SFHebrew',
                        color: FlutterFlowTheme.of(context).primary,
                        fontSize: 24.0,
                        letterSpacing: 0.0,
                      ),
                ),
                const SizedBox(height: 36),
                TextField(
                  controller: _nameController,
                  cursorColor: FlutterFlowTheme.of(context).primary,
                  style: FlutterFlowTheme.of(context).labelLarge,
                  decoration: InputDecoration(
                    labelText: isEnglish ? 'Meal Name' : 'שם ארוחה',
                    labelStyle: TextStyle(
                      fontSize: 16,
                      color: FlutterFlowTheme.of(context).primary,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.red),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Colors.red),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                  ),
                ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final editedName = _nameController.text.trim();
                      if (editedName.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              isEnglish
                                  ? 'Meal name cannot be empty'
                                  : 'שם הארוחה לא יכול להיות ריק',
                            ),
                          ),
                        );
                        return;
                      }
                      Navigator.of(context).pop(editedName);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: FlutterFlowTheme.of(context).primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      isEnglish ? 'Save' : 'שמור',
                      style: FlutterFlowTheme.of(context).titleMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                    ),
                  ),
                ),
                SizedBox(height: FFAppState().bottomPadding + 8),
              ],
            ),
          ),
        )
    );
  }
}

import '/backend/schema/enums/enums.dart';
import '/componentes/center_image/center_image_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'goal_item_model.dart';
export 'goal_item_model.dart';

class GoalItemWidget extends StatefulWidget {
  const GoalItemWidget({
    super.key,
    required this.id,
    this.onTap,
    double? selectedValue,
  }) : this.selectedValue = selectedValue ?? 0.1;

  final int? id;
  final Future Function()? onTap;
  final double selectedValue;

  @override
  State<GoalItemWidget> createState() => _GoalItemWidgetState();
}

class _GoalItemWidgetState extends State<GoalItemWidget> {
  late GoalItemModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => GoalItemModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 75.0,
      decoration: BoxDecoration(),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            width: 60.0,
            height: 60.0,
            decoration: BoxDecoration(),
            child: Builder(
              builder: (context) {
                if (valueOrDefault<bool>(
                  widget.id == 1,
                  false,
                )) {
                  return wrapWithModel(
                    model: _model.centerImageModel1,
                    updateCallback: () => safeSetState(() {}),
                    child: CenterImageWidget(
                      imageType: ImageType.kg1,
                      isSelected: widget.selectedValue >= 0.1,
                    ),
                  );
                } else if (valueOrDefault<bool>(
                  widget.id == 2,
                  false,
                )) {
                  return wrapWithModel(
                    model: _model.centerImageModel2,
                    updateCallback: () => safeSetState(() {}),
                    child: CenterImageWidget(
                      imageType: ImageType.kg2,
                      isSelected: widget.selectedValue >= 0.2,
                    ),
                  );
                } else if (valueOrDefault<bool>(
                  widget.id == 3,
                  false,
                )) {
                  return wrapWithModel(
                    model: _model.centerImageModel3,
                    updateCallback: () => safeSetState(() {}),
                    updateOnChange: true,
                    child: CenterImageWidget(
                      imageType: ImageType.kg8,
                      isSelected: widget.selectedValue >= 0.8,
                    ),
                  );
                } else if (widget.id == 4) {
                  return wrapWithModel(
                    model: _model.centerImageModel4,
                    updateCallback: () => safeSetState(() {}),
                    child: CenterImageWidget(
                      imageType: ImageType.kg10,
                      isSelected: widget.selectedValue >= 1.0,
                    ),
                  );
                } else {
                  return wrapWithModel(
                    model: _model.centerImageModel5,
                    updateCallback: () => safeSetState(() {}),
                    child: CenterImageWidget(
                      imageType: () {
                        if (widget.id == 1) {
                          return ImageType.kg1;
                        } else if (widget.id == 2) {
                          return ImageType.kg2;
                        } else if (widget.id == 3) {
                          return ImageType.kg8;
                        } else {
                          return ImageType.kg10;
                        }
                      }(),
                      isSelected: widget.selectedValue >= 4.0,
                    ),
                  );
                }
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.all(10.0),
            child: Container(
              width: MediaQuery.sizeOf(context).width * 0.005,
              height: 40.0,
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).primary,
              ),
            ),
          ),
          Text(
            valueOrDefault<String>(
              () {
                if (widget.id == 1) {
                  return '0.1 ק\"ג';
                } else if (widget.id == 2) {
                  return '0.2 ק\"ג';
                } else if (widget.id == 3) {
                  return '0.8 ק\"ג';
                } else {
                  return '1 ק\"ג';
                }
              }(),
              '0.2 ק\"ג',
            ),
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  letterSpacing: 0.0,
                ),
          ),
        ],
      ),
    );
  }
}

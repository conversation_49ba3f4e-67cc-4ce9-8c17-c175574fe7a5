import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';

class NutritionFactItem extends StatelessWidget {
  final String label;
  final String value;
  final String? unit;

  const NutritionFactItem({
    super.key,
    required this.label,
    required this.value,
    this.unit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
          // color: FlutterFlowTheme.of(context).lightgrey,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: FlutterFlowTheme.of(context).grey)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              label,
              style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                    fontWeight: FontWeight.normal,
                  ),
            ),
          ),
          SizedBox(
            // width: 100,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                unit != null ? '$value$unit' : value,
                style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                    fontWeight: FontWeight.w700,
                    overflow: TextOverflow.ellipsis),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

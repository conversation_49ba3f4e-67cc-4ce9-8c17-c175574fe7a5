import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import '../../backend/get_storage/storage_local.dart';
import '../../backend/schema/structs/food_item_struct.dart';
import '../../flutter_flow/flutter_flow_icon_button.dart';
import 'nutrition_fact_item.dart';
import 'package:cal_counti_a_i/pages/ingredients/edit_ingredients.dart';
import 'package:cal_counti_a_i/componentes/ingredient_item/nutrient_type.dart';
import 'package:cal_counti_a_i/pages/ingredients/edit_meal_name.dart';
import '../../backend/api_requests/api_calls.dart';
import 'package:flutter/foundation.dart' show listEquals;
import '../../serving_size_translator.dart';
import '../../nutrient_name_translator.dart';

class FoodNutritionDetails extends StatefulWidget {
  final FoodItem foodItem;
  final int? mealId;

  const FoodNutritionDetails({
    super.key,
    required this.foodItem,
    this.mealId,
  });

  @override
  State<FoodNutritionDetails> createState() => _FoodNutritionDetailsState();
}

class _FoodNutritionDetailsState extends State<FoodNutritionDetails> with SingleTickerProviderStateMixin {
  double servingAmount = 1.0;
  late TabController _tabController;

  late List<String> servingSizes;
  String? _currentLanguage;
  bool _languageInitialized = false;
  late ServingType _initialServingType;
  int _initialTabIndex = 0;
  late List<Map<String, String>> nutritionFacts;
  late List<Map<String, String>> baseNutritionFacts;
  late TextEditingController caloriesCtrl;
  late TextEditingController proteinCtrl;
  late TextEditingController carbsCtrl;
  late TextEditingController fatsCtrl;
  String? foodName;
  double baseCalories = 0.0;
  double baseProtein = 0.0;
  double baseCarbs = 0.0;
  double baseFats = 0.0;
  late StorageOperations storageOps;

  String _formatLabel(String key) {
    String formatted =
        key.replaceAllMapped(RegExp(r'([A-Z])'), (match) => ' ${match.group(1)}').replaceAll('_', ' ').trim();
    return formatted.split(' ').map((word) => word[0].toUpperCase() + word.substring(1).toLowerCase()).join(' ');
  }

  String _getUnit(String key) {
    if (key.toLowerCase().contains('fat') || key.toLowerCase().contains('fiber')) {
      return 'g';
    } else if (key.toLowerCase().contains('cholesterol') || key.toLowerCase().contains('sodium')) {
      return 'mg';
    }
    return '';
  }

  List<Map<String, String>> _generateNutritionFacts(ServingType servingType) {
    final excludedKeys = {'calories', 'protein', 'carbs', 'fat'};
    List<Map<String, String>> facts = [];
    final servingJson = servingType.toJson();
    servingJson.forEach((key, value) {
      if (!excludedKeys.contains(key) && key != 'id' && key != 'label') {
        facts.add({
          'label': _formatLabel(key),
          'value': value?.toString() ?? '0',
          'unit': _getUnit(key),
        });
      }
    });
    return facts;
  }

  String _formatNumber(double value) {
    if (value == value.floorToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(1);
  }

  void _scaleNutritionValues() {
    setState(() {
      double scaledCalories = baseCalories * servingAmount;
      double scaledProtein = baseProtein * servingAmount;
      double scaledCarbs = baseCarbs * servingAmount;
      double scaledFats = baseFats * servingAmount;

      caloriesCtrl.text = _formatNumber(scaledCalories);
      proteinCtrl.text = _formatNumber(scaledProtein);
      carbsCtrl.text = _formatNumber(scaledCarbs);
      fatsCtrl.text = _formatNumber(scaledFats);

      nutritionFacts = baseNutritionFacts.map((fact) {
        double baseValue = double.tryParse(fact['value']!) ?? 0.0;
        double scaledValue = baseValue * servingAmount;
        String englishLabel = fact['label']!;
        // _currentLanguage might be null during initState, translate will fallback to English
        String translatedLabel = NutrientNameTranslator.translate(englishLabel, _currentLanguage ?? 'en');
        return {
          'label': translatedLabel,
          'value': _formatNumber(scaledValue),
          'unit': fact['unit']!,
        };
      }).toList();
    });
  }

  @override
  void initState() {
    super.initState();
    storageOps = StorageOperations();

    final data = widget.foodItem.data;

    // Initialize nutritional values from FoodItem data
    baseCalories = data.calories?.toDouble() ?? 0.0;
    baseProtein = data.protein?.toDouble() ?? 0.0;
    baseCarbs = data.carbs?.toDouble() ?? 0.0;
    baseFats = data.fats?.toDouble() ?? 0.0;
    servingAmount = data.servings?.toDouble() ?? 1.0;

    print('FoodNutritionDetails: Initialized servingAmount: $servingAmount, '
        'Calories: $baseCalories, Protein: $baseProtein, Carbs: $baseCarbs, Fats: $baseFats');

    // Initialize TextEditingControllers with scaled values
    caloriesCtrl = TextEditingController(text: _formatNumber(baseCalories * servingAmount));
    proteinCtrl = TextEditingController(text: _formatNumber(baseProtein * servingAmount));
    carbsCtrl = TextEditingController(text: _formatNumber(baseCarbs * servingAmount));
    fatsCtrl = TextEditingController(text: _formatNumber(baseFats * servingAmount));

    // Initialize serving sizes (will be translated in didChangeDependencies)
    servingSizes =
        data.servingTypes.isNotEmpty ? data.servingTypes.map((st) => st.label ?? 'Serving').toList() : ['Serving'];

    // Find the initial serving type based on servingTypeId
    // _initialServingType and _initialTabIndex are now instance variables, initialized below:
    if (data.servingTypes.isNotEmpty && data.servingTypeId != null) {
      try {
        _initialServingType = data.servingTypes.firstWhere(
          (st) => st.id == data.servingTypeId,
          orElse: () => data.servingTypes[0],
        );
        _initialTabIndex = data.servingTypes.indexWhere((st) => st.id == data.servingTypeId);
        if (_initialTabIndex == -1) _initialTabIndex = 0;
      } catch (e) {
        _initialServingType = data.servingTypes[0];
        _initialTabIndex = 0;
      }
    } else {
      _initialServingType = ServingType(
        id: 'default',
        label: 'Serving',
        // Will be translated in didChangeDependencies
        calories: baseCalories,
        protein: baseProtein,
        carbs: baseCarbs,
        fat: baseFats,
      );
    }

    // Initialize base nutrition facts
    baseNutritionFacts = _generateNutritionFacts(_initialServingType);
    nutritionFacts = List.from(baseNutritionFacts);

    // Initialize TabController
    _tabController = TabController(
      length: servingSizes.length,
      initialIndex: _initialTabIndex,
      vsync: this,
    );

    // Update nutritional values based on the initial serving type
    if (data.servingTypes.isNotEmpty) {
      // Keep original condition
      baseCalories = _initialServingType.calories?.toDouble() ?? baseCalories;
      baseProtein = _initialServingType.protein?.toDouble() ?? baseProtein;
      baseCarbs = _initialServingType.carbs?.toDouble() ?? baseCarbs;
      baseFats = _initialServingType.fat?.toDouble() ?? baseFats;
      _scaleNutritionValues();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    String newLanguage = FFLocalizations.of(context).languageCode;

    // If it's the first run (languageInitialized is false) or if the language has actually changed
    if (!_languageInitialized || _currentLanguage != newLanguage) {
      _currentLanguage = newLanguage; // Update the current language tracking
      bool isCurrentLanguageEnglish = _currentLanguage == 'en';

      // Update foodName based on the new language
      foodName = isCurrentLanguageEnglish
          ? (widget.foodItem.enName.isNotEmpty ? widget.foodItem.enName : widget.foodItem.heName)
          : (widget.foodItem.heName.isNotEmpty ? widget.foodItem.heName : widget.foodItem.enName);

      final data = widget.foodItem.data;
      List<String> newTranslatedServingSizes;

      // Translate serving sizes for the TabBar
      if (data.servingTypes.isNotEmpty) {
        newTranslatedServingSizes =
            data.servingTypes.map((st) => ServingSizeTranslator.translate(st.label, _currentLanguage!)).toList();
      } else {
        newTranslatedServingSizes = [ServingSizeTranslator.translate('Serving', _currentLanguage!)];
      }

      // Update the servingSizes list that the TabBar uses if it has changed
      if (!listEquals(servingSizes, newTranslatedServingSizes)) {
        servingSizes = newTranslatedServingSizes;
        // If TabController's length depends on servingSizes.length and it changed,
        // TabController might need to be recreated or updated.
        // For now, assuming length remains constant and only content changes.
      }

      // Translate and update the label of _initialServingType if it's the default one and has changed
      if (_initialServingType.id == 'default') {
        String newLabelForInitialServingType = ServingSizeTranslator.translate('Serving', _currentLanguage!);
        if (_initialServingType.label != newLabelForInitialServingType) {
          _initialServingType = ServingType(
            id: _initialServingType.id,
            label: newLabelForInitialServingType,
            // Use the translated label
            calories: _initialServingType.calories,
            protein: _initialServingType.protein,
            carbs: _initialServingType.carbs,
            fat: _initialServingType.fat,
            // Ensure all other fields of ServingType are copied if any
          );
        }
      }

      // _scaleNutritionValues will:
      // 1. Use the (now potentially updated) _currentLanguage to translate baseNutritionFacts.
      // 2. Update caloriesCtrl, proteinCtrl, etc.
      // 3. Call setState() to rebuild the UI.
      _scaleNutritionValues();

      _languageInitialized = true; // Mark that language-dependent setup is complete for this language
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    caloriesCtrl.dispose();
    proteinCtrl.dispose();
    carbsCtrl.dispose();
    fatsCtrl.dispose();
    super.dispose();
  }

  String _formatServingAmount(double value) {
    if (value == value.floorToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(1);
  }

  Future<void> _editNutrient(NutrientType nutrientType, TextEditingController controller) async {
    double initialValue = double.tryParse(controller.text) ?? 0.0;
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditIngredients(
          nutrientType: nutrientType,
          initialValue: initialValue / servingAmount,
          maxValue: 2000.0,
        ),
      ),
    );

    if (result != null && result is double) {
      setState(() {
        switch (nutrientType) {
          case NutrientType.calories:
            baseCalories = result;
            break;
          case NutrientType.protein:
            baseProtein = result;
            break;
          case NutrientType.carbs:
            baseCarbs = result;
            break;
          case NutrientType.fats:
            baseFats = result;
            break;
        }
        _scaleNutritionValues();
      });
    }
  }

  Future<void> _editFoodName() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditMealName(currentName: foodName),
      ),
    );

    if (result != null && result is String) {
      setState(() {
        foodName = result;
      });
    }
  }

  Future<void> _logFoodItem() async {
    try {
      bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

      // Validate servingAmount
      if (servingAmount <= 0 || servingAmount > 100) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(isEnglish ? 'Please select a valid serving amount (1-100).' : 'אנא בחר כמות מנות תקינה (1-100).'),
          ),
        );
        return;
      }

      // Get the selected serving type
      final selectedServingType = widget.foodItem.data.servingTypes.isNotEmpty
          ? widget.foodItem.data.servingTypes[_tabController.index]
          : ServingType(
              id: 'default',
              label: 'Serving',
              calories: baseCalories,
              protein: baseProtein,
              carbs: baseCarbs,
              fat: baseFats,
            );

      // Prepare the meal data with user-selected values
      final mealData = {
        if (widget.mealId != null) "meal_id": widget.mealId,
        "type": "manual",
        "serving_type": selectedServingType.label,
        "total_calories": (baseCalories * servingAmount).toStringAsFixed(1),
        "total_fats": (baseFats * servingAmount).toStringAsFixed(1),
        "total_proteins": (baseProtein * servingAmount).toStringAsFixed(1),
        "total_carbs": (baseCarbs * servingAmount).toStringAsFixed(1),
        "name": foodName ?? (isEnglish ? widget.foodItem.enName : widget.foodItem.heName),
        "quantity": servingAmount,
        "update_items": [
          {
            "is_enable": true,
            "data": widget.foodItem.data.servingTypes.map((st) {
              final isSelected = st.id == selectedServingType.id;
              return {
                "id": st.id,
                "fat": isSelected ? baseFats : (st.fat ?? baseFats),
                "iron": st.iron ?? 0,
                "carbs": isSelected ? baseCarbs : (st.carbs ?? baseCarbs),
                "fiber": st.fiber ?? 0,
                "label": st.label,
                "sugar": st.sugar ?? 0,
                "sodium": st.sodium ?? 0,
                "calcium": st.calcium ?? 0,
                "protein": isSelected ? baseProtein : (st.protein ?? baseProtein),
                "calories": isSelected ? baseCalories : (st.calories ?? baseCalories),
                "vitaminA": st.vitaminA ?? 0,
                "vitaminC": st.vitaminC ?? 0,
                "potassium": st.potassium ?? 0,
                "formalName": st.formalName ?? st.label,
                "metricUnit": st.metricUnit ?? "g",
                "cholesterol": st.cholesterol ?? 0,
                "metricAmount": st.metricAmount ?? 0,
                "saturatedFat": st.saturatedFat ?? 0,
                "numberOfUnits": st.numberOfUnits ?? 1,
                "monoUnsaturatedFat": st.monoUnsaturatedFat ?? 0,
                "polyUnsaturatedFat": st.polyUnsaturatedFat ?? 0,
              };
            }).toList(),
          }
        ],
      };
      if (widget.foodItem != null && widget.mealId == null) {
        storageOps.saveFoodItem(widget.foodItem);
      }
      // Log the payload for debugging
      print('Request Payload: ${jsonEncode(mealData)}');

      // Call the UpdateMeal API
      final response = await UpdateMealCall.call(
        accessToken: FFAppState().authToken,
        jsonJson: mealData,
      );

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEnglish
                  ? (widget.mealId != null ? 'Meal updated successfully' : 'Meal added successfully')
                  : (widget.mealId != null ? 'הארוחה עודכנה בהצלחה' : 'הארוחה נוספה בהצלחה'),
            ),
          ),
        );
        Navigator.of(context).pop(true);
        // context.goNamed(
        //   'dashboard',
        //   queryParameters: {
        //     'refresh': 'true',
        //   },
        // );
      } else {
        final errorMessage =
            UpdateMealCall.error(response.jsonBody) ?? (isEnglish ? 'Failed to update meal.' : 'נכשל בעדכון הארוחה.');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
          ),
        );
      }
    } catch (e) {
      print('Error in _logFoodItem: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(FFLocalizations.of(context).languageCode == 'en'
              ? 'An unexpected error occurred.'
              : 'אירעה שגיאה בלתי צפויה.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    return MeasuredWidget(
        name: 'FoodNutritionDetails',
        builder: (context) => Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.black),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                elevation: 0,
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
                titleSpacing: 0,
                title: Text(
                  isEnglish ? 'Nutrition' : 'תזונה',
                  style: FlutterFlowTheme.of(context).headlineMedium.copyWith(color: Colors.black, fontSize: 18),
                ),
                centerTitle: false,
                actions: [
                  if (widget.mealId != null)
                    FlutterFlowIconButton(
                      borderRadius: 30.0,
                      borderWidth: 1.0,
                      buttonSize: 40.0,
                      icon: const Icon(
                        Icons.delete_forever_outlined,
                        color: Colors.red,
                        size: 24.0,
                      ),
                      onPressed: () async {
                        final confirmDialogResponse = await showDialog<bool>(
                              context: context,
                              builder: (alertDialogContext) {
                                return WebViewAware(
                                  child: AlertDialog(
                                    title: Text(isEnglish ? 'Delete Meal?' : 'למחוק ארוחה?'),
                                    content: Text(isEnglish
                                        ? 'Are you sure you want to delete this meal? This action cannot be undone, and all related data will be permanently removed.'
                                        : 'האם אתה בטוח שברצונך למחוק את הארוחה הזו? פעולה זו אינה ניתנת לביטול וכל הנתונים הקשורים יימחקו לצמיתות.'),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(alertDialogContext, false),
                                        child: Text(FFLocalizations.of(context).getText('5efik18d' /* Cancel */)),
                                      ),
                                      TextButton(
                                        onPressed: () => Navigator.pop(alertDialogContext, true),
                                        child: Text(FFLocalizations.of(context).getText('4efik18d' /* Confirm */)),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ) ??
                            false;

                        if (confirmDialogResponse) {
                          logFirebaseEvent('FOOD_NUTRITION_DETAILS_DELETE_MEAL');
                          final dialogContext = context;
                          showDialog(
                            context: dialogContext,
                            barrierDismissible: false,
                            builder: (dialogContext) {
                              return Dialog(
                                  backgroundColor: Colors.transparent,
                                  elevation: 0,
                                  insetPadding: EdgeInsets.zero,
                                  child: Center(
                                      child: Container(
                                          width: 80,
                                          height: 80,
                                          padding: const EdgeInsets.all(15),
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: const CircularProgressWidget())));
                            },
                          );

                          final deleteMealResponse = await DeleteMealCall.call(
                            accessToken: FFAppState().authToken,
                            jsonJson: {'meal_id': widget.mealId},
                          );

                          Navigator.pop(dialogContext); // Dismiss loading dialog

                          if (deleteMealResponse.succeeded) {
                            logFirebaseEvent('FOOD_NUTRITION_DETAILS_DELETE_SUCCESS');
                            Navigator.of(context).pop(true); // Return to dashboard and trigger refresh
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(isEnglish ? 'Failed to delete meal.' : 'נכשל במחיקת הארוחה.'),
                              ),
                            );
                          }
                        }
                      },
                    ),
                ],
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                GestureDetector(
                                  onTap: _editFoodName,
                                  child: Row(
                                    children: [
                                      Text(
                                        foodName ?? 'Loading...',
                                        style: FlutterFlowTheme.of(context).headlineLarge.copyWith(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 20,
                                            ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(width: 8),
                                      Icon(
                                        Icons.edit,
                                        size: 20,
                                        color: FlutterFlowTheme.of(context).primary,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Text(
                              isEnglish ? 'Serving Size Measurement' : 'מדידת גודל מנה',
                              style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                                    fontWeight: FontWeight.w500,
                                    color: FlutterFlowTheme.of(context).primaryText,
                                    fontSize: 16,
                                  ),
                            ),
                            const SizedBox(height: 15),
                            SizedBox(
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: List.generate(servingSizes.length, (index) {
                                    final selected = _tabController.index == index;
                                    return Padding(
                                      padding: const EdgeInsets.only(right: 8.0),
                                      child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _tabController.index = index;
                                            final servingType = widget.foodItem.data.servingTypes.isNotEmpty
                                                ? widget.foodItem.data.servingTypes[index]
                                                : ServingType(
                                                    id: 'default',
                                                    label: 'Serving',
                                                    calories: baseCalories,
                                                    protein: baseProtein,
                                                    carbs: baseCarbs,
                                                    fat: baseFats,
                                                  );
                                            baseCalories = servingType.calories?.toDouble() ?? 0.0;
                                            baseProtein = servingType.protein?.toDouble() ?? 0.0;
                                            baseCarbs = servingType.carbs?.toDouble() ?? 0.0;
                                            baseFats = servingType.fat?.toDouble() ?? 0.0;
                                            baseNutritionFacts = _generateNutritionFacts(servingType);
                                            _scaleNutritionValues();
                                          });
                                        },
                                        child: AnimatedContainer(
                                          duration: const Duration(milliseconds: 180),
                                          curve: Curves.easeInOut,
                                          // height: 36,
                                          padding: const EdgeInsets.only(
                                            left: 16,
                                            right: 16,
                                            top: 3,
                                            bottom: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: selected ? Colors.black : Colors.white,
                                            borderRadius: BorderRadius.circular(24),
                                            border: Border.all(color: Colors.black, width: 1),
                                          ),
                                          child: Text(
                                            servingSizes[index],
                                            style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                                                  color: selected ? Colors.white : Colors.black,
                                                  fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                                                  fontSize: 14,
                                                ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }),
                                ),
                              ),
                            ),
                            const SizedBox(height: 18),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isEnglish ? 'Serving Amount' : 'כמות מנה',
                                  style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: FlutterFlowTheme.of(context).primaryText,
                                      ),
                                ),
                                const SizedBox(width: 10),
                                Row(
                                  children: [
                                    Container(
                                      width: 120,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(30),
                                        border: Border.all(color: Colors.black, width: 1),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.remove),
                                            onPressed: () {
                                              setState(() {
                                                if (servingAmount > 1.0) {
                                                  servingAmount -= 1.0;
                                                  _scaleNutritionValues();
                                                }
                                              });
                                            },
                                          ),
                                          Text(
                                            _formatServingAmount(servingAmount),
                                            style: FlutterFlowTheme.of(context).headlineMedium.override(
                                                  fontWeight: FontWeight.normal,
                                                  fontSize: 18,
                                                ),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.add),
                                            onPressed: () {
                                              setState(() {
                                                servingAmount += 1.0;
                                                _scaleNutritionValues();
                                              });
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 25),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: FlutterFlowTheme.of(context).grey),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
                                child: Row(
                                  children: [
                                    const Icon(Icons.local_fire_department, color: Colors.black, size: 28),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () => _editNutrient(NutrientType.calories, caloriesCtrl),
                                        child: AbsorbPointer(
                                          child: TextField(
                                            readOnly: true,
                                            cursorColor: Colors.black,
                                            controller: caloriesCtrl,
                                            keyboardType: TextInputType.number,
                                            style: FlutterFlowTheme.of(context).headlineMedium.override(
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.normal,
                                                ),
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(context).headlineMedium.copyWith(
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 24,
                                                    color: FlutterFlowTheme.of(context).primaryText,
                                                  ),
                                              border: InputBorder.none,
                                              labelText: isEnglish ? 'Calories' : 'קלוריות',
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 10),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                /// Protein
                                widgetProtein(context, isEnglish),
                                const SizedBox(width: 8),

                                /// Carbs
                                widgetCarbs(context, isEnglish),
                                const SizedBox(width: 8),

                                /// Fats
                                widgetFats(context, isEnglish),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Text(
                              isEnglish ? 'Other nutrition facts' : 'עובדות תזונה נוספות',
                              style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const SizedBox(height: 8),
                            ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: nutritionFacts.length,
                              itemBuilder: (context, index) {
                                final fact = nutritionFacts[index];
                                return NutritionFactItem(
                                  label: NutrientNameTranslator.translate(fact['label']!, _currentLanguage ?? 'en'),
                                  value: fact['value']!,
                                  unit: fact['unit']!,
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _logFoodItem,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 14),
                        ),
                        child: Text(
                          isEnglish
                              ? (widget.mealId != null ? 'Update' : 'Log')
                              : (widget.mealId != null ? 'עדכן' : 'עדכן'),
                          style: FlutterFlowTheme.of(context).titleMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                              ),
                        ),
                      ),
                    ),
                    SizedBox(height: FFAppState().bottomPadding)
                  ],
                ),
              ),
            ));
  }

  Widget widgetFats(BuildContext context, bool isEnglish) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: FlutterFlowTheme.of(context).grey),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.opacity, color: Colors.blue, size: 18),
              const SizedBox(width: 10),
              Flexible(
                child: GestureDetector(
                  onTap: () => _editNutrient(NutrientType.fats, fatsCtrl),
                  child: AbsorbPointer(
                    child: TextField(
                      readOnly: true,
                      cursorColor: Colors.black,
                      controller: fatsCtrl,
                      keyboardType: TextInputType.number,
                      style: FlutterFlowTheme.of(context).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                      decoration: InputDecoration(
                        labelStyle: FlutterFlowTheme.of(context).headlineMedium.copyWith(
                              fontWeight: FontWeight.normal,
                              fontSize: 18,
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                        border: InputBorder.none,
                        labelText: isEnglish ? 'Fats' : 'שומנים',
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget widgetCarbs(BuildContext context, bool isEnglish) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: FlutterFlowTheme.of(context).grey),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/images/wheat-barley_svgrepo.com.svg',
                width: 20.0,
                height: 20.0,
                color: Colors.orange,
                fit: BoxFit.cover,
              ),
              const SizedBox(width: 10),
              Flexible(
                child: GestureDetector(
                  onTap: () => _editNutrient(NutrientType.carbs, carbsCtrl),
                  child: AbsorbPointer(
                    child: TextField(
                      readOnly: true,
                      cursorColor: Colors.black,
                      controller: carbsCtrl,
                      keyboardType: TextInputType.number,
                      style: FlutterFlowTheme.of(context).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                      decoration: InputDecoration(
                        labelStyle: FlutterFlowTheme.of(context).headlineMedium.copyWith(
                              fontWeight: FontWeight.normal,
                              fontSize: 18,
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                        border: InputBorder.none,
                        labelText: isEnglish ? 'Carbs' : 'פחמימות',
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget widgetProtein(BuildContext context, bool isEnglish) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: FlutterFlowTheme.of(context).grey),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/images/Frame.svg',
                width: 18.0,
                height: 18.0,
                fit: BoxFit.contain,
              ),
              const SizedBox(width: 10),
              Flexible(
                child: GestureDetector(
                  onTap: () => _editNutrient(NutrientType.protein, proteinCtrl),
                  child: AbsorbPointer(
                    child: TextField(
                      readOnly: true,
                      cursorColor: Colors.black,
                      controller: proteinCtrl,
                      keyboardType: TextInputType.number,
                      style: FlutterFlowTheme.of(context).bodyLarge.copyWith(fontWeight: FontWeight.bold),
                      decoration: InputDecoration(
                        labelStyle: FlutterFlowTheme.of(context).headlineMedium.copyWith(
                              fontWeight: FontWeight.normal,
                              fontSize: 18,
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                        border: InputBorder.none,
                        labelText: isEnglish ? 'Protein' : 'חלבון',
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';

class NutritionsDataStruct extends FFFirebaseStruct {
  NutritionsDataStruct({
    List<NutritionsDetailStruct>? fats,
    List<NutritionsDetailStruct>? carbs,
    List<NutritionsDetailStruct>? proteins,
    List<NutritionsDetailStruct>? calories,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _fats = fats,
        _carbs = carbs,
        _proteins = proteins,
        _calories = calories,
        super(firestoreUtilData);

  // "fats" field.
  List<NutritionsDetailStruct>? _fats;
  List<NutritionsDetailStruct> get fats => _fats ?? const [];
  set fats(List<NutritionsDetailStruct>? val) => _fats = val;

  void updateFats(Function(List<NutritionsDetailStruct>) updateFn) {
    updateFn(_fats ??= []);
  }

  bool hasFats() => _fats != null;

  // "carbs" field.
  List<NutritionsDetailStruct>? _carbs;
  List<NutritionsDetailStruct> get carbs => _carbs ?? const [];
  set carbs(List<NutritionsDetailStruct>? val) => _carbs = val;

  void updateCarbs(Function(List<NutritionsDetailStruct>) updateFn) {
    updateFn(_carbs ??= []);
  }

  bool hasCarbs() => _carbs != null;

  // "proteins" field.
  List<NutritionsDetailStruct>? _proteins;
  List<NutritionsDetailStruct> get proteins => _proteins ?? const [];
  set proteins(List<NutritionsDetailStruct>? val) => _proteins = val;

  void updateProteins(Function(List<NutritionsDetailStruct>) updateFn) {
    updateFn(_proteins ??= []);
  }

  bool hasProteins() => _proteins != null;


  // "calories" field.
  List<NutritionsDetailStruct>? _calories;
  List<NutritionsDetailStruct> get calories => _calories ?? const [];
  set calories(List<NutritionsDetailStruct>? val) => _calories = val;

  void updateCalories(Function(List<NutritionsDetailStruct>) updateFn) {
    updateFn(_calories ??= []);
  }

  bool hasCalories() => _calories != null;

  static NutritionsDataStruct fromMap(Map<String, dynamic> data) =>
      NutritionsDataStruct(
        fats: getStructList(
          data['fats'],
          NutritionsDetailStruct.fromMap,
        ),
        carbs: getStructList(
          data['carbs'],
          NutritionsDetailStruct.fromMap,
        ),
        proteins: getStructList(
          data['proteins'],
          NutritionsDetailStruct.fromMap,
        ),
        calories: getStructList(
          data['calories'],
          NutritionsDetailStruct.fromMap,
        ),
      );

  static NutritionsDataStruct? maybeFromMap(dynamic data) => data is Map
      ? NutritionsDataStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'fats': _fats?.map((e) => e.toMap()).toList(),
        'carbs': _carbs?.map((e) => e.toMap()).toList(),
        'proteins': _proteins?.map((e) => e.toMap()).toList(),
        'calories': _calories?.map((e) => e.toMap()).toList(),
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'fats': serializeParam(
          _fats,
          ParamType.DataStruct,
          isList: true,
        ),
        'carbs': serializeParam(
          _carbs,
          ParamType.DataStruct,
          isList: true,
        ),
        'proteins': serializeParam(
          _proteins,
          ParamType.DataStruct,
          isList: true,
        ),
        'calories': serializeParam(
          _calories,
          ParamType.DataStruct,
          isList: true,
        ),
      }.withoutNulls;

  static NutritionsDataStruct fromSerializableMap(Map<String, dynamic> data) =>
      NutritionsDataStruct(
        fats: deserializeStructParam<NutritionsDetailStruct>(
          data['fats'],
          ParamType.DataStruct,
          true,
          structBuilder: NutritionsDetailStruct.fromSerializableMap,
        ),
        carbs: deserializeStructParam<NutritionsDetailStruct>(
          data['carbs'],
          ParamType.DataStruct,
          true,
          structBuilder: NutritionsDetailStruct.fromSerializableMap,
        ),
        proteins: deserializeStructParam<NutritionsDetailStruct>(
          data['proteins'],
          ParamType.DataStruct,
          true,
          structBuilder: NutritionsDetailStruct.fromSerializableMap,
        ),
        calories: deserializeStructParam<NutritionsDetailStruct>(
          data['calories'],
          ParamType.DataStruct,
          true,
          structBuilder: NutritionsDetailStruct.fromSerializableMap,
        ),
      );

  @override
  String toString() => 'NutritionsDataStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is NutritionsDataStruct &&
        listEquality.equals(fats, other.fats) &&
        listEquality.equals(carbs, other.carbs) &&
        listEquality.equals(proteins, other.proteins) &&
        listEquality.equals(calories, other.calories);
  }

  @override
  int get hashCode => const ListEquality().hash([fats, carbs, proteins, calories]);
}

NutritionsDataStruct createNutritionsDataStruct({
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    NutritionsDataStruct(
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

NutritionsDataStruct? updateNutritionsDataStruct(
  NutritionsDataStruct? nutritionsData, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    nutritionsData
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addNutritionsDataStructData(
  Map<String, dynamic> firestoreData,
  NutritionsDataStruct? nutritionsData,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (nutritionsData == null) {
    return;
  }
  if (nutritionsData.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && nutritionsData.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final nutritionsDataData =
      getNutritionsDataFirestoreData(nutritionsData, forFieldValue);
  final nestedData =
      nutritionsDataData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = nutritionsData.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getNutritionsDataFirestoreData(
  NutritionsDataStruct? nutritionsData, [
  bool forFieldValue = false,
]) {
  if (nutritionsData == null) {
    return {};
  }
  final firestoreData = mapToFirestore(nutritionsData.toMap());

  // Add any Firestore field values
  nutritionsData.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getNutritionsDataListFirestoreData(
  List<NutritionsDataStruct>? nutritionsDatas,
) =>
    nutritionsDatas
        ?.map((e) => getNutritionsDataFirestoreData(e, true))
        .toList() ??
    [];

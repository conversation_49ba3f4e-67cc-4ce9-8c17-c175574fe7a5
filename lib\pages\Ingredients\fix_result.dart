import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/app_state.dart';
import 'package:cal_counti_a_i/backend/api_requests/api_calls.dart';
import 'package:cal_counti_a_i/backend/firebase_analytics/analytics.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/internationalization.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:cal_counti_a_i/pages/dashboard/dashboard_widget.dart'; // Import DashboardWidget
import 'package:flutter/material.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:go_router/go_router.dart';

import '../../main.dart';

class FixResult extends StatefulWidget {
  final String mealId;

  const FixResult({super.key, required this.mealId});

  @override
  State<FixResult> createState() => _FixResultState();
}

class _FixResultState extends State<FixResult> {
  TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    developer.log('FixResult initState: mealId = ${widget.mealId}', name: 'FixResult');
  }

  Future<void> _regenerateMeal(BuildContext context) async {
    logFirebaseEvent('fix_result_regenerate_meal');

    final userPrompt = _controller.text.trim();
    if (userPrompt.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            FFLocalizations.of(context).languageCode == 'en'
                ? 'Please enter a prompt to regenerate the meal'
                : 'אנא הזן הנחיה כדי ליצור מחדש את הארוחה',
          ),
        ),
      );
      return;
    }

    final parsedMealId = int.tryParse(widget.mealId);
    if (parsedMealId == null) {
      developer.log('Error: mealId "${widget.mealId}" is not a valid integer', name: 'FixResult');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            FFLocalizations.of(context).languageCode == 'en'
                ? 'Cannot regenerate meal: Invalid meal ID'
                : 'לא ניתן ליצור מחדש את הארוחה: מזהה ארוחה לא תקין',
          ),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (dialogContext) {
        return Dialog(
          elevation: 0,
          insetPadding: EdgeInsets.zero,
          backgroundColor: Colors.transparent,
          alignment: const AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
          child: WebViewAware(
            child: GestureDetector(
              onTap: () {
                FocusScope.of(dialogContext).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: const SizedBox(
                height: 80.0,
                width: 80.0,
                child: CircularProgressWidget(),
              ),
            ),
          ),
        );
      },
    );

    final response = await RegenerateMealCall.call(
      accessToken: FFAppState().authToken,
      mealId: parsedMealId,
      userAdjustmentPrompt: userPrompt,
    );

    Navigator.pop(context);

    developer.log('RegenerateMeal response: ${jsonEncode(response.jsonBody)}, Status: ${response.statusCode}',
        name: 'FixResult');

    if (response.succeeded && response.jsonBody['status'] == 200) {
      logFirebaseEvent('fix_result_regenerate_success');
      await Future.delayed(const Duration(milliseconds: 50));
      // Navigate to dashboard using GoRouter
      GoRouter.of(context).go('/dashboard');
    } else {
      logFirebaseEvent('fix_result_regenerate_failure');
      final errorMessage = response.jsonBody?['message'] ?? 'Unknown error';
      developer.log('RegenerateMeal failed: $errorMessage', name: 'FixResult');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            FFLocalizations.of(context).languageCode == 'en'
                ? 'Failed to regenerate meal: $errorMessage'
                : 'נכשל ביצירת הארוחה מחדש: $errorMessage',
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'FixResults',
        builder: (context) => Scaffold(
              appBar: AppBar(
                leading: IconButton(
                  icon: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: Icon(Icons.arrow_back_ios_new, color: Colors.black),
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                elevation: 0,
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
              ),
              backgroundColor: Colors.white,
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.auto_awesome, color: Colors.black, size: 28),
                        SizedBox(width: 8),
                        Text(
                          isEnglish ? 'Fix result' : 'תקן תוצאה',
                          style: FlutterFlowTheme.of(context).headlineLarge.override(
                                fontFamily: 'SFHebrew',
                                color: FlutterFlowTheme.of(context).primary,
                                fontSize: 24.0,
                                letterSpacing: 0.0,
                              ),
                        ),
                      ],
                    ),
                    SizedBox(height: 24),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.black, width: 2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      child: TextField(
                        controller: _controller,
                        maxLines: null,
                        style: TextStyle(fontSize: 16, color: Colors.black),
                        decoration: InputDecoration(
                          hintText: isEnglish ? "Type Food Items..." : "הקלד פרטי מזון...",
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(vertical: 5),
                          isCollapsed: true,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Container(
                      decoration: BoxDecoration(color: Colors.grey[100], borderRadius: BorderRadius.circular(15)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                        child: Text(isEnglish
                            ? "Example: The pasta is missing the marinara sauce and Parmesan cheese."
                            : "דוגמה: הפסטה חסרה את רוטב המרינרה ואת גבינת הפרמזן."),
                      ),
                    ),
                    Spacer(),
                    Center(
                      child: GestureDetector(
                        onTap: () => _regenerateMeal(context),
                        child: Container(
                          width: double.infinity,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Center(
                            child: Text(
                              isEnglish ? 'Generate' : 'תקן',
                              style: TextStyle(
                                color: FlutterFlowTheme.of(context).primaryBackground,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: FFAppState().bottomPadding + 8),
                  ],
                ),
              ),
            ));
  }
}

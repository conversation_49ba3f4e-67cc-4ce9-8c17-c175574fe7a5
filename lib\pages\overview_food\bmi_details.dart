import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class BmiDetails extends StatefulWidget {
  final double bmi;
  final String status;
  final Color statusColor;

  const BmiDetails({
    Key? key,
    required this.bmi,
    required this.status,
    required this.statusColor,
  }) : super(key: key);

  @override
  State<BmiDetails> createState() => _BmiDetailsState();
}

class _BmiDetailsState extends State<BmiDetails> {
  // Helper for BMI legend
  Widget _bmiLegendDot(BuildContext context, Color color, String label) {
    return Row(
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 5),
        Text(
          label,
          style: FlutterFlowTheme.of(context).bodySmall,
        ),
      ],
    );
  }

  double bmiToBarPosition(double bmi, bool isEnglish) {
    if (isEnglish) {
      // English layout (left to right)
      if (bmi <= 15) return 0.0;
      if (bmi <= 20) return (bmi - 15) / (20 - 15) * 0.33;
      if (bmi <= 25) return 0.33 + (bmi - 20) / (25 - 20) * 0.33;
      if (bmi <= 30) return 0.66 + (bmi - 25) / (30 - 25) * 0.34;
      return 1.0;
    } else {
      // Hebrew layout (right to left) - reverse the positioning
      if (bmi <= 15) return 1.0;
      if (bmi <= 20) return 1.0 - (bmi - 15) / (20 - 15) * 0.33;
      if (bmi <= 25) return 0.67 - (bmi - 20) / (25 - 20) * 0.33;
      if (bmi <= 30) return 0.34 - (bmi - 25) / (30 - 25) * 0.34;
      return 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final bmi = widget.bmi;
    final status = widget.status;
    final statusColor = widget.statusColor;
    final bool isEnglish = Localizations.localeOf(context).languageCode == 'en';

    return MeasuredWidget(
        name: 'BMIDetail',
        builder: (context) => Scaffold(
              appBar: AppBar(
                centerTitle: true,
                title: Text(
                  'BMI',
                  style: FlutterFlowTheme.of(context).headlineMedium.copyWith(color: Colors.black, fontSize: 18),
                ),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.black),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                backgroundColor: Colors.white,
                elevation: 0,
              ),
              backgroundColor: Colors.white,
              body: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // BMI Calculation Section (custom UI, not BMICard)
                    Row(
                      children: [
                        Text(
                          isEnglish ? 'Your weight is' : 'המשקל שלך הוא',
                          style: FlutterFlowTheme.of(context)
                              .bodyMedium
                              .copyWith(color: Colors.black, fontWeight: FontWeight.normal),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusColor,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            isEnglish ? status : 'השמנת יתר',
                            // Use your _hebrewStatus if needed
                            style: FlutterFlowTheme.of(context).bodyMedium.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      bmi.toStringAsFixed(2),
                      style: FlutterFlowTheme.of(context)
                          .displayMedium
                          .copyWith(fontWeight: FontWeight.bold, fontSize: 32),
                    ),
                    const SizedBox(height: 12),
                    // Gradient bar with indicator
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Stack(
                        alignment: Alignment.centerLeft,
                        children: [
                          Container(
                            height: 14,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              gradient: LinearGradient(
                                colors: isEnglish
                                    ? [
                                        Color(0xFF3A8DFF), // Underweight - blue
                                        Color(0xFF2ECC71), // Healthy - green
                                        Color(0xFFFFA726), // Overweight - orange
                                        Color(0xFFE53935), // Obese - red
                                      ]
                                    : [
                                        Color(0xFFE53935), // Obese - red (right side for Hebrew)
                                        Color(0xFFFFA726), // Overweight - orange
                                        Color(0xFF2ECC71), // Healthy - green
                                        Color(0xFF3A8DFF), // Underweight - blue (left side for Hebrew)
                                      ],
                                stops: [0.0, 0.33, 0.66, 1.0],
                              ),
                            ),
                          ),
                          Positioned(
                            left: bmiToBarPosition(bmi, isEnglish) * (MediaQuery.of(context).size.width - 80),
                            child: Container(
                              width: 4,
                              height: 28,
                              decoration: BoxDecoration(
                                color: Colors.black,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: isEnglish
                          ? [
                              _bmiLegendDot(context, Color(0xFF3A8DFF), 'Underweight'),
                              _bmiLegendDot(context, Color(0xFF2ECC71), 'Healthy'),
                              _bmiLegendDot(context, Color(0xFFFFA726), 'Overweight'),
                              _bmiLegendDot(context, Color(0xFFE53935), 'Obese'),
                            ]
                          : [
                              _bmiLegendDot(context, Color(0xFF3A8DFF), 'תת משקל'),
                              _bmiLegendDot(context, Color(0xFF2ECC71), 'בריא'),
                              _bmiLegendDot(context, Color(0xFFFFA726), 'עודף משקל'),
                              _bmiLegendDot(context, Color(0xFFE53935), 'השמנת יתר'),
                            ],
                    ),
                    const SizedBox(height: 24),
                    Text(
                      isEnglish ? 'Disclaimer' : 'הבהרה',
                      style: FlutterFlowTheme.of(context).headlineMedium.copyWith(color: Colors.black, fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isEnglish
                          ? 'As with most measures of health, BMI is not a perfect test. For example, results can be thrown off by pregnancy or high muscle mass, and it may not be a good measure of health for children or the elderly.'
                          : 'כמו ברוב מדדי הבריאות, BMI אינו מבחן מושלם. לדוגמה, תוצאות עלולות להיות מוטות בהריון או במסה שרירית גבוהה, ואולי אינו מדד טוב לבריאות ילדים או קשישים.',
                      style: FlutterFlowTheme.of(context)
                          .headlineSmall
                          .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      isEnglish ? 'So then, why does BMI matter?' : 'אז למה BMI חשוב ?',
                      style: FlutterFlowTheme.of(context).headlineMedium.copyWith(color: Colors.black, fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isEnglish
                          ? 'In general, the higher your BMI, the higher the risk of developing a range of conditions linked with excess weight, including:'
                          : 'באופן כללי, ככל שה-BMI שלך גבוה יותר, כך עולה הסיכון לפתח מגוון מצבים הקשורים לעודף משקל, כולל:',
                      style: FlutterFlowTheme.of(context)
                          .headlineSmall
                          .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                    ),
                    const SizedBox(height: 12),
                    Padding(
                      padding: const EdgeInsets.only(left: 6.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isEnglish ? '• diabetes' : '• סוכרת',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish ? '• arthritis' : '• דלקת מפרקים',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish ? '• liver disease' : '• מחלת כבד',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish
                                ? '• several types of cancer (such as those of the breast, colon, and prostate)'
                                : '• סוגים שונים של סרטן (כגון סרטן השד, המעי הגס והערמונית)',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish ? '• high blood pressure (hypertension)' : '• לחץ דם גבוה (יתר לחץ דם)',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish ? '• high cholesterol' : '• כולסטרול גבוה',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                          Text(
                            isEnglish ? '• sleep apnea.' : '• דום נשימה בשינה.',
                            style: FlutterFlowTheme.of(context)
                                .headlineSmall
                                .copyWith(color: Colors.black, fontSize: 14, fontWeight: FontWeight.normal),
                          ),
                        ],
                      ),
                    ),
                    TextButton(
                      onPressed: () async {
                        const url = 'https://www.cdc.gov/bmi/about/index.html';

                        try {
                          launchURL(url);
                        } catch (e) {
                          print('Error launching URL: $e');
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(isEnglish ? 'Error launching URL: $e' : 'שגיאה בפתיחת הקישור: $e')),
                          );
                        }
                      },
                      child: Text(
                        isEnglish ? 'Source' : 'מקורות',
                        style: TextStyle(
                          color: Colors.grey,
                          decoration: TextDecoration.underline,
                          fontSize: 15,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }
}

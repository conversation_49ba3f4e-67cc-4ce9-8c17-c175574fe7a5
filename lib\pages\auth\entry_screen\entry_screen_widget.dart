import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:url_launcher/url_launcher.dart';

import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'entry_screen_model.dart';
export 'entry_screen_model.dart';
import 'dart:ui' as ui;

class EntryScreenWidget extends StatefulWidget {
  const EntryScreenWidget({super.key});

  @override
  State<EntryScreenWidget> createState() => _EntryScreenWidgetState();
}

class _EntryScreenWidgetState extends State<EntryScreenWidget>
    with TickerProviderStateMixin {
  late EntryScreenModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EntryScreenModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('entry_screen_wait__delay');
      await Future.delayed(const Duration(milliseconds: 10));
      if (FFAppState().languageCode == '') {
        String deviceLocale = ui.window.locale.languageCode;
        logFirebaseEvent('entry_screen_set $deviceLocale deviceLocale');
        // setAppLanguage(context, (deviceLocale == 'he') ? 'he' : 'en');
        setAppLanguage(context, 'he');
      } else {
        if ((FFAppState().languageCode != null &&
                FFAppState().languageCode != '') &&
            (FFAppState().languageCode == 'he')) {
          logFirebaseEvent('entry_screen_set_app_language');
          setAppLanguage(context, 'he');
        } else {
          logFirebaseEvent('entry_screen_set_app_language');
          // setAppLanguage(context, 'en');
        }
      }
    });

    animationsMap.addAll({
      'buttonOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          ShimmerEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            color: Color(0x80FFFFFF),
            angle: 0.524,
          ),
        ],
      ),
    });
  }

  Future<void> _checkFirstRun({required Function()? onCompletion}) async {
    final bool hasSeenDisclaimer = FFAppState().hasSeenDisclaimer;
    final bool hasSeenMedicalNotice = FFAppState().hasSeenMedicalNotice;

    if (!hasSeenDisclaimer) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _showMedicalDisclaimer();
      });
    } else if (!hasSeenMedicalNotice) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _showMedicalUsageNotice(onCompletion: onCompletion);
      });
    } else {
      if (onCompletion != null) {
        onCompletion();
      }
    }
  }

  void _showMedicalDisclaimer() {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          titlePadding: EdgeInsets.all(10),
          insetPadding: EdgeInsets.all(10),
          contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 12),
          actionsPadding: EdgeInsets.only(bottom: 12, right: 6, left: 6),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            textDirection:
                isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
            children: [
              Row(
                textDirection:
                    isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                children: [
                  Icon(Icons.health_and_safety, color: Colors.red, size: 24),
                  SizedBox(width: 8),
                  Text(
                    isEnglish
                        ? 'Medical Information Waiver'
                        : 'הבהרה על מידע רפואי',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                ],
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.grey),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          content: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection:
                    isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                children: [
                  Text(
                    isEnglish ? 'Important Medical Waiver' : 'הבהרה רפואי חשוב',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                      fontSize: 16,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'This application provides nutritional information for educational purposes only and is not intended to diagnose, treat, cure, or prevent any diseases or health conditions.'
                        : 'אפליקציה זו מספקת מידע תזונתי לצורכי חינוך בלבד ואינה מיועדת לאבחן, לטפל, לרפא או למנוע מחלות או מצבים בריאותיים.',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'The data is based on the USDA Food Database (2023), the National Nutrition Database, and additional scientific sources cited under \'Sources and Information\'.'
                        : 'הנתונים מבוססים על מאגר המזון של USDA (2023), מאגר התזונה הלאומי ומקורות מדעיים נוספים המצוטטים תחת "מקורות ומידע".',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'The food identification technology in the application is based on artificial intelligence and therefore may contain errors. The application does not take into account different food preparations, additives, or cooking methods.'
                        : 'טכנולוגיית זיהוי המזון באפליקציה מבוססת על בינה מלאכותית ולכן עלולות להיות טעויות. האפליקציה אינה מביאה בחשבון הכנות אוכל שונות, תוספים או שיטות בישול.',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'Before making changes to your diet, consult with a qualified physician or nutritionist, especially if you have food allergies, medical conditions, or medications that may be affected by dietary changes.'
                        : 'לפני ביצוע שינויים בתזונה שלך, התייעץ עם רופא מוסמך או תזונאי, במיוחד אם יש לך אלרגיות למזון, מצבים רפואיים או תרופות שעלולות להיות מושפעות משינויי תזונה.',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'This application is not a substitute for professional medical or nutritional advice.'
                        : 'אפליקציה זו אינה מהווה תחליף לייעוץ רפואי או תזונתי מקצועי.',
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, // Background color
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
              child: Text(
                isEnglish ? 'I understand' : 'אני מבין',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'SFHebrew',
                ),
              ),
              onPressed: () async {
                FFAppState().hasSeenDisclaimer = true;
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showMedicalUsageNotice({required Function()? onCompletion}) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          titlePadding: EdgeInsets.all(16),
          insetPadding: EdgeInsets.all(10),
          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          actionsPadding: EdgeInsets.only(bottom: 12, right: 12, left: 12),
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            textDirection:
                isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
            children: [
              Row(
                textDirection:
                    isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 24),
                  SizedBox(width: 6),
                  Text(
                    isEnglish
                        ? 'Important Usage Notice'
                        : 'הודעה חשובה על השימוש',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                ],
              ),
              IconButton(
                icon: Icon(Icons.close, color: Colors.grey),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          content: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                textDirection:
                    isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                children: [
                  Text(
                    isEnglish
                        ? 'How to Use This Information'
                        : 'כיצד להשתמש במידע זה',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                      fontSize: 16,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'The nutritional information provided by this application is intended for general guidance only. Please consider the following guidelines:'
                        : 'המידע התזונתי שמספקת אפליקציה זו מיועד להנחיה כללית בלבד. יש להתחשב בהנחיות הבאות:',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 8),
                  Text(
                    isEnglish
                        ? 'Use this information as one of the tools for making informed food choices'
                        : '• השתמש במידע זה כאחד הכלים לבחירת מזון מושכלת',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'Note that the portion sizes displayed may differ from what you consume'
                        : '• שים לב שגודל המנות המוצג עשוי להיות שונה ממה שאתה צורך',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'Nutritional content varies according to brand, preparation method, and ripeness'
                        : '• תכולת תזונה משתנה בהתאם למותג, שיטת הכנה ובשלות',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'Check the confidence ratings for each scan'
                        : '• בדוק את דירוגי האמון לכל סריקה',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'Verify information with the product packaging when possible'
                        : '• אמת מידע עם אריזת המוצר כאשר הדבר אפשרי',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 12),
                  Text(
                    isEnglish
                        ? 'For people with specific nutritional needs:'
                        : 'לאנשים עם צרכים תזונתיים ספציפיים:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontFamily: 'SFHebrew',
                    ),
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  SizedBox(height: 8),
                  Text(
                    isEnglish
                        ? 'This application does not identify all possible allergens or ingredients'
                        : '• אפליקציה זו אינה מזהה את כל האלרגנים האפשריים או המרכיבים',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'People with allergies, diabetes, or other medical conditions should not rely solely on this application'
                        : '• אנשים עם אלרגיות, סוכרת או מצבים רפואיים אחרים לא צריכים להסתמך רק על אפליקציה זו',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                  Text(
                    isEnglish
                        ? 'Work with medical professionals to interpret nutritional information according to your health needs'
                        : '• יש לעבוד עם אנשי מקצוע רפואיים כדי לפרש מידע תזונתי בהתאם לצרכים הבריאותיים שלך',
                    // textDirection: ui.TextDirection.rtl,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
              child: Text(
                isEnglish ? 'Continue to the application' : 'המשך לאפליקציה',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'SFHebrew',
                ),
              ),
              onPressed: () async {
                FFAppState().hasSeenMedicalNotice = true;
                Navigator.of(context).pop();
                if (onCompletion != null) {
                  onCompletion();
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return MeasuredWidget(
        name: 'EntryScreen',
        builder: (context) => GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
        body: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Flexible(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: Stack(
                  alignment: AlignmentDirectional(0.0, 1.0),
                  children: [
                    Align(
                      alignment: AlignmentDirectional(0.0, -1.0),
                      child: Container(
                        // width: double.infinity,
                        height: MediaQuery.sizeOf(context).height * 0.65,
                        decoration: BoxDecoration(
                          color: FlutterFlowTheme.of(context).primaryBackground,
                        ),
                        padding: EdgeInsets.all(25),
                        child: Image.asset(
                          'assets/images/backimage_he1.png',
                          colorBlendMode: BlendMode.difference,
                          // width: double.infinity,
                          // height: double.infinity,
                          height: MediaQuery.sizeOf(context).height * 0.50,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      height: MediaQuery.sizeOf(context).height * 0.35,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xA1FAFAFA),
                            FlutterFlowTheme.of(context).primaryBackground
                          ],
                          stops: [0.0, 1.0],
                          begin: AlignmentDirectional(0.0, -1.0),
                          end: AlignmentDirectional(0, 1.0),
                        ),
                      ),
                      alignment: AlignmentDirectional(0.0, 0.0),
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'r06pxq8g' /* Calorie tracking has never been easier. */,
                                  ),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context)
                                      .headlineMedium
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        fontSize: 27.0,
                                        letterSpacing: 0.0,
                                        lineHeight: 1.2,
                                      ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 6.0, 0.0, 0.0),
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      '637w9ldb' /* Stay informed about your food ... */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context)
                                        .labelLarge
                                        .override(
                                          fontFamily: 'SFHebrew',
                                          fontSize: 15.0,
                                          letterSpacing: 0.0,
                                          lineHeight: 1.5,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                RichText(
                                  textScaler: MediaQuery.of(context).textScaler,
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            FFLocalizations.of(context).getText(
                                          'alq298de' /* Already have an account?  */,
                                        ),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'SFHebrew',
                                              letterSpacing: 0.0,
                                            ),
                                      ),
                                      TextSpan(
                                        text:
                                            FFLocalizations.of(context).getText(
                                          'uzb6wap9' /* Sign In */,
                                        ),
                                        style: FlutterFlowTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'SFHebrew',
                                              color:
                                                  FlutterFlowTheme.of(context)
                                                      .tertiary,
                                              letterSpacing: 0.0,
                                              fontWeight: FontWeight.bold,
                                              decoration:
                                                  TextDecoration.underline,
                                            ),
                                        mouseCursor: SystemMouseCursors.click,
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () async {
                                            logFirebaseEvent(
                                                'RichTextSpan_navigate_to');

                                            // _checkFirstRun(onCompletion: () {
                                            context.pushNamed(
                                              'quiz',
                                              queryParameters: {
                                                'isForSignIn': serializeParam(
                                                  true,
                                                  ParamType.bool,
                                                ),
                                              }.withoutNulls,
                                            );
                                            // });
                                          },
                                      )
                                    ],
                                    style: FlutterFlowTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'SFHebrew',
                                          letterSpacing: 0.0,
                                        ),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 10.0, 0.0, 0.0),
                                  child: RichText(
                                    textScaler:
                                        MediaQuery.of(context).textScaler,
                                    text: TextSpan(
                                      children: [
                                        TextSpan(
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'xugyc1dw' /* Terms */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.bold,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                          mouseCursor: SystemMouseCursors.click,
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () async {
                                              logFirebaseEvent(
                                                  'RichTextSpan_navigate_to');

                                              // await launchUrl(
                                              //   Uri.parse(appTerms),
                                              //   mode: LaunchMode.inAppWebView,
                                              // );

                                              context.pushNamed(
                                                'teams_privacy',
                                                queryParameters: {
                                                  'privacy': serializeParam(
                                                    false,
                                                    ParamType.bool,
                                                  ),
                                                  'url': serializeParam(
                                                    appTerms,
                                                    ParamType.String,
                                                  ),
                                                }.withoutNulls,
                                              );
                                            },
                                        ),
                                        TextSpan(
                                          text:
                                              ', ' /*FFLocalizations.of(context)
                                              .getText(
                                            'o9l4sxw7' */ /*  and  */ /*,
                                          )*/
                                          ,
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                letterSpacing: 0.0,
                                              ),
                                        ),
                                        TextSpan(
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'cljmrv5e' /* Privacy Policy */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .primary,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.bold,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                          mouseCursor: SystemMouseCursors.click,
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () async {
                                              logFirebaseEvent(
                                                  'RichTextSpan_navigate_to');

                                              // await launchUrl(
                                              //   Uri.parse(appPrivacy),
                                              //   mode: LaunchMode.inAppWebView,
                                              // );

                                              context.pushNamed(
                                                'teams_privacy',
                                                queryParameters: {
                                                  'privacy': serializeParam(
                                                    true,
                                                    ParamType.bool,
                                                  ),
                                                  'url': serializeParam(
                                                    appPrivacy,
                                                    ParamType.String,
                                                  ),
                                                }.withoutNulls,
                                              );
                                            },
                                        ),
                                        TextSpan(
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'o9l4sxw7',
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                letterSpacing: 0.0,
                                              ),
                                        ),
                                        TextSpan(
                                          text: FFLocalizations.of(context)
                                              .getText(
                                            'cljmrv5a' /* Nutrition Citations */,
                                          ),
                                          style: FlutterFlowTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                color:
                                                    FlutterFlowTheme.of(context)
                                                        .primary,
                                                letterSpacing: 0.0,
                                                fontWeight: FontWeight.bold,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                          mouseCursor: SystemMouseCursors.click,
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () async {
                                              context.pushNamed(
                                                  'nutrition_citations');
                                            },
                                        )
                                      ],
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ],
                            ),
                            FFButtonWidget(
                              onPressed: () async {
                                logFirebaseEvent('Button_navigate_to');
                                // _checkFirstRun(onCompletion: () {
                                context.goNamed('quiz');
                                // });
                              },
                              text: FFLocalizations.of(context).getText(
                                '8mqlnlp4' /* Next */,
                              ),
                              options: FFButtonOptions(
                                width: MediaQuery.sizeOf(context).width * 0.9,
                                height: 50.0,
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                iconAlignment: IconAlignment.start,
                                iconPadding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 0.0, 0.0, 0.0),
                                color: FlutterFlowTheme.of(context).primary,
                                textStyle: FlutterFlowTheme.of(context)
                                    .titleSmall
                                    .override(
                                      fontFamily: 'SFHebrew',
                                      color: Colors.white,
                                      letterSpacing: 0.0,
                                      fontSize: 18,
                                    ),
                                elevation: 0.0,
                                borderRadius: BorderRadius.circular(30.0),
                              ),
                            ).animateOnPageLoad(
                                animationsMap['buttonOnPageLoadAnimation']!),
                          ],
                        ),
                      ),
                    ),
                    // LanguageSwitchOverlay(),
                  ],
                ),
              ),
            ),
            Container(
              width: 0.0,
              height: FFAppState().bottomPadding,
              decoration: BoxDecoration(
                color: FlutterFlowTheme.of(context).primaryBackground,
              ),
            ),
          ],
        ),
      ),
    ));
  }
}

class LanguageSwitchOverlay extends StatelessWidget {
  const LanguageSwitchOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    return Positioned(
      top: 40,
      right: 20,
      child: Material(
        elevation: 3,
        borderRadius: BorderRadius.circular(30),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.deepOrangeAccent[200]!,
                Colors.deepOrangeAccent[100]!,
                Colors.deepOrangeAccent[700]!,
              ],
              stops: [0.0, 0.5, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.deepOrangeAccent.withOpacity(0.4),
                spreadRadius: 2,
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              setAppLanguage(context, isEnglish ? 'he' : 'en');
            },
            borderRadius: BorderRadius.circular(30),
            child: AnimatedContainer(
              duration: Duration(milliseconds: 500),
              curve: Curves.easeInOut,
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 3),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'עברית',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: !isEnglish ? Colors.white : Colors.white70,
                      letterSpacing: 0.5,
                      fontFamily: 'SFHebrew',
                    ),
                  )
                      .animate()
                      .scale(duration: 300.ms, curve: Curves.easeOut)
                      .fade(duration: 300.ms),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Container(
                      padding: EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.white30, width: 1),
                      ),
                      child: Icon(
                        Icons.swap_horiz_rounded,
                        size: 14,
                        color: Colors.white,
                      ),
                    )
                        .animate(onPlay: (controller) => controller.repeat())
                        .shimmer(
                            duration: 2000.ms,
                            color: Colors.white.withOpacity(0.5))
                        .rotate(duration: 700.ms, begin: 0, end: 0.1),
                  ),
                  Text(
                    'English',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: isEnglish ? Colors.white : Colors.white70,
                      letterSpacing: 0.5,
                      fontFamily: 'SFHebrew',
                    ),
                  )
                      .animate()
                      .scale(duration: 300.ms, curve: Curves.easeOut)
                      .fade(duration: 300.ms),
                ],
              ),
            ),
          ),
        ),
      ),
    )
        .animate()
        .fadeIn(duration: 800.ms, curve: Curves.easeOut)
        .slideY(
          begin: -0.7,
          end: 0.0,
          duration: 1000.ms,
          curve: Curves.elasticOut,
        )
        .shimmer(delay: 400.ms, duration: 1800.ms);
  }
}

import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter_svg/flutter_svg.dart';

class FoodDatabaseLogItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final String calories;
  final VoidCallback? onTap;

  const FoodDatabaseLogItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.calories,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).lightgrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: GestureDetector(
        onTap: onTap,
        child: ListTile(
          onTap: onTap,
          minTileHeight: 10,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
          title: Text(
            title,
            style: FlutterFlowTheme.of(context).headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                SvgPicture.asset(
                  'assets/images/Frame.svg',
                  color: Colors.black,
                  width: 20.0,
                  height: 20.0,
                  fit: BoxFit.contain,
                ),
                SizedBox(
                  width: 5,
                ),
                SizedBox(
                  width: 180,
                  child: Text(
                    subtitle,
                    style: FlutterFlowTheme.of(context).bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Text(
              //   calories,
              //   style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
              //         fontWeight: FontWeight.w600,
              //       ),
              // ),
              // const SizedBox(width: 12),
              GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  // decoration: BoxDecoration(
                  //   color: FlutterFlowTheme.of(context).grey,
                  //   borderRadius: BorderRadius.circular(100),
                  // ),
                  child: const Icon(Icons.add, size: 25, color: Colors.black),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

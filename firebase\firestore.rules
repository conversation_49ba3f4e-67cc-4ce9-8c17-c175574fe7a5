rules_version = '3';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      // Allow users to create, read, write, and delete their own user document
      allow create, read, write, delete: if request.auth != null && request.auth.uid == userId;

      // Allow users to create, read, and update their own suggestions subcollection
      match /suggestions/{suggestionId} {
        allow create, read, update: if request.auth != null && request.auth.uid == userId;
        allow delete: if false; // Prevent deletion of suggestions
      }
    }

    // Deny all other operations by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>קל-קאונטי</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>he</string>
	</array>
	<key>CFBundleName</key>
	<string>CalCounti AI</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.92</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.1075353593734-06lc5egpvrpn7173367i4m5f68eb4b8l</string>
				<string>com.googleusercontent.apps.55847766910-2ignrsi5saooirobiot1pv3h574oldse</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>calcountiai.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>calcountiai</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>FlutterDeepLinkingEnabled</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<string>NO</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Allow Camera to capture Food!</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Camera usage Microphone while capturing the food image</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow Photos to choose Food!</string>
	<key>PermissionGroupNotification</key>
	<string>CalCounti Ai needs to send you notifications about meal updates and other important messages.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.posthog.posthog.API_KEY</key>
	<string>phc_CzDzxMZ3I199ik5QYyIUKYIYycFD8qopW8Mk5KRuUSP</string>
	<key>com.posthog.posthog.CAPTURE_APPLICATION_LIFECYCLE_EVENTS</key>
	<true/>
	<key>com.posthog.posthog.DEBUG</key>
	<true/>
	<key>com.posthog.posthog.POSTHOG_HOST</key>
	<string>https://us.i.posthog.com</string>
</dict>
</plist>

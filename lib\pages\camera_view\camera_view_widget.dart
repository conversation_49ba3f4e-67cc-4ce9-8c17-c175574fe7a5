import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:lottie/lottie.dart';

import '../../custom_code/widgets/camera_custom_widget.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/custom_code/widgets/index.dart' as custom_widgets;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'camera_view_model.dart';
export 'camera_view_model.dart';

class CameraViewWidget extends StatefulWidget {
  const CameraViewWidget({super.key});

  @override
  State<CameraViewWidget> createState() => _CameraViewWidgetState();
}

class _CameraViewWidgetState extends State<CameraViewWidget> {
  late CameraViewModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  final ValueNotifier<ScannerMode> scannerMode =
      ValueNotifier(ScannerMode.food);

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => CameraViewModel());
  }

  @override
  void dispose() {
    _model.dispose();
    scannerMode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return MeasuredWidget(
      name: 'CameraViewWidget',
      builder: (context) => GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: WillPopScope(
          onWillPop: () async {
            logFirebaseEvent('CameraViewWidget_navigate_back');
            return true; // Allow back navigation
          },
          child: Scaffold(
            key: scaffoldKey,
            body: Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.transparent,
                  child: custom_widgets.CameraCustomWidget(
                    width: double.infinity,
                    height: double.infinity,
                    onPickedImage: (imagePath) async {
                      logFirebaseEvent('CameraCustomWidget_update_page_state');
                      _model.capturedImage = imagePath;
                      safeSetState(() {});
                    },
                    takePicture: () async {
                      logFirebaseEvent('CameraCustomWidget_update_page_state');
                      safeSetState(() {});
                    },
                    onScannerModeChanged: (mode) {
                      scannerMode.value = mode;
                    },
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    logFirebaseEvent('IconButton_navigate_back');
                    if (Navigator.of(context).canPop()) {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.fromLTRB(20, 30, 20, 0),
                    width: 45,
                    height: 45.0,
                    decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.5),
                        shape: BoxShape.circle),
                    child: Icon(
                      Icons.clear_rounded,
                      color: FlutterFlowTheme.of(context).primaryBackground,
                      size: 30.0,
                    ),
                  ),
                ),
                ValueListenableBuilder<ScannerMode>(
                  valueListenable: scannerMode,
                  builder: (context, mode, child) {
                    if (_model.capturedImage == null ||
                        _model.capturedImage!.isEmpty) {
                      return SizedBox(
                        height: FFAppState().deviceHeight,
                        width: FFAppState().deviceWidth,
                        child: Stack(
                          children: [
                            Container(
                              padding: EdgeInsets.only(
                                bottom: FFAppState().bottomPadding + 70,
                                // top: FFAppState().topPadding,
                              ),
                              child: Align(
                                child: SvgPicture.asset(
                                  'assets/images/radius_scanner.svg',
                                  width:
                                      MediaQuery.sizeOf(context).width * 0.85,
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.47,
                                  fit: BoxFit.contain,
                                  alignment: Alignment.center,
                                ),
                                alignment: Alignment.center,
                              ),
                            ),
                            Align(
                              alignment: AlignmentDirectional(0.0, 0.0),
                              child: Container(
                                padding: EdgeInsets.only(
                                  bottom: FFAppState().bottomPadding + 70,
                                  // top: FFAppState().topPadding,
                                ),
                                child: Lottie.asset(
                                  'assets/jsons/animated_scanner.json',
                                  width: MediaQuery.sizeOf(context).width * 0.6,
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.6,
                                  fit: BoxFit.contain,
                                  animate: true,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    } else {
                      return Container(
                        width: 0.0,
                        height: 0.0,
                        decoration: const BoxDecoration(),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/pages/ingredients/edit_ingredients.dart';
import 'package:cal_counti_a_i/componentes/ingredient_item/nutrient_type.dart';
import 'package:cal_counti_a_i/pages/ingredients/edit_meal_name.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../backend/get_storage/storage_local.dart';
import '../../backend/schema/structs/food_item_struct.dart';
import '../../backend/api_requests/api_calls.dart';

class LogEmptyMeal extends StatefulWidget {
  const LogEmptyMeal({super.key});

  @override
  State<LogEmptyMeal> createState() => _LogEmptyMealState();
}

class _LogEmptyMealState extends State<LogEmptyMeal> {
  int servingAmount = 1;
  late TextEditingController caloriesCtrl;
  late TextEditingController proteinCtrl;
  late TextEditingController carbsCtrl;
  late TextEditingController fatsCtrl;
  double baseCalories = 0.0;
  double baseProtein = 0.0;
  double baseCarbs = 0.0;
  double baseFats = 0.0;
  String? foodName;
  bool _isFoodNameInitialized = false;
  late StorageOperations storageOps;

  String _formatNumber(double value) {
    if (value == value.floorToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(1);
  }

  void _scaleNutritionValues() {
    setState(() {
      double scaledCalories = baseCalories * servingAmount;
      double scaledProtein = baseProtein * servingAmount;
      double scaledCarbs = baseCarbs * servingAmount;
      double scaledFats = baseFats * servingAmount;

      caloriesCtrl.text = _formatNumber(scaledCalories);
      proteinCtrl.text = _formatNumber(scaledProtein);
      carbsCtrl.text = _formatNumber(scaledCarbs);
      fatsCtrl.text = _formatNumber(scaledFats);
    });
  }

  @override
  void initState() {
    super.initState();
    storageOps = StorageOperations();

    caloriesCtrl = TextEditingController(text: '0');
    proteinCtrl = TextEditingController(text: '0');
    carbsCtrl = TextEditingController(text: '0');
    fatsCtrl = TextEditingController(text: '0');

    baseCalories = 0.0;
    baseProtein = 0.0;
    baseCarbs = 0.0;
    baseFats = 0.0;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isFoodNameInitialized) {
      bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
      foodName = isEnglish ? 'New Meal' : 'ארוחה חדשה';
      _isFoodNameInitialized = true;
    }
  }

  @override
  void dispose() {
    caloriesCtrl.dispose();
    proteinCtrl.dispose();
    carbsCtrl.dispose();
    fatsCtrl.dispose();
    super.dispose();
  }

  Future<void> _editNutrient(
      NutrientType nutrientType, TextEditingController controller) async {
    double initialValue =
        (double.tryParse(controller.text) ?? 0.0) / servingAmount;
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditIngredients(
          nutrientType: nutrientType,
          initialValue: initialValue,
          maxValue: 2000.0,
        ),
      ),
    );

    if (result != null && result is double) {
      setState(() {
        switch (nutrientType) {
          case NutrientType.calories:
            baseCalories = result;
            break;
          case NutrientType.protein:
            baseProtein = result;
            break;
          case NutrientType.carbs:
            baseCarbs = result;
            break;
          case NutrientType.fats:
            baseFats = result;
            break;
        }
        _scaleNutritionValues();
      });
    }
  }

  Future<void> _editFoodName() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditMealName(currentName: foodName),
      ),
    );

    if (result != null && result is String) {
      setState(() {
        foodName = result;
      });
    }
  }

  Future<void> _logFoodItem() async {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    // Calculate scaled values for logging
    double scaledCalories = baseCalories * servingAmount;
    double scaledProtein = baseProtein * servingAmount;
    double scaledCarbs = baseCarbs * servingAmount;
    double scaledFats = baseFats * servingAmount;

    // Create a new ServingType with all nutritional fields
    final servingType = ServingType(
      id: 'default',
      label: 'Serving',
      formalName: 'Serving',
      metricUnit: 'unit',
      metricAmount: 100.0,
      numberOfUnits: 1.0,
      calories: baseCalories,
      protein: baseProtein,
      carbs: baseCarbs,
      fat: baseFats,
      fiber: 0.0,
      sugar: 0.0,
      sodium: 0.0,
      calcium: 0.0,
      iron: 0.0,
      potassium: 0.0,
      vitaminA: 0.0,
      vitaminC: 0.0,
      cholesterol: 0.0,
      saturatedFat: 0.0,
      monoUnsaturatedFat: 0.0,
      polyUnsaturatedFat: 0.0,
    );

    // Create FoodDetails
    final foodDetails = FoodDetails(
      id: 'manual_${DateTime.now().millisecondsSinceEpoch}',
      name: isEnglish ? (foodName ?? 'New Meal') : (foodName ?? 'ארוחה חדשה'),
      brand: '',
      fats: scaledFats,
      carbs: scaledCarbs,
      protein: scaledProtein,
      calories: scaledCalories,
      servings: servingAmount.toDouble(),
      servingTypeId: 'default',
      servingTypes: [servingType],
    );

    // Create FoodItem
    final foodItem = FoodItem(
      id: DateTime.now().millisecondsSinceEpoch,
      enName: isEnglish ? (foodName ?? 'New Meal') : 'New Meal',
      heName: isEnglish ? 'ארוחה חדשה' : (foodName ?? 'ארוחה חדשה'),
      data: foodDetails,
      created_at: DateTime.now().toIso8601String(),
      relevance: 1.0,
    );

    // Save the FoodItem locally
    storageOps.saveFoodItem(foodItem);

    // Prepare data for the UpdateMeal API call
    final accessToken = FFAppState().authToken;
    if (accessToken == null || accessToken.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isEnglish
              ? 'Error: Access token not found.'
              : 'שגיאה: לא נמצא טוקן גישה.'),
        ),
      );
      return;
    }

    final mealData = {
      "type": "manual",
      "total_calories": scaledCalories,
      "total_fats": scaledFats,
      "total_proteins": scaledProtein,
      "total_carbs": scaledCarbs,
      "name": foodName ?? (isEnglish ? 'New Meal' : 'ארוחה חדשה'),
      "quantity": servingAmount,
      "serving_type": "Serving",
      "items": [
        {
          "0": {
            "id": "default",
            "label": "Serving",
            "formalName": "Serving",
            "metricUnit": "unit",
            "metricAmount": 100.0,
            "numberOfUnits": 1.0,
            "calories": baseCalories,
            "protein": baseProtein,
            "carbs": baseCarbs,
            "fat": baseFats,
            "fiber": 0.0,
            "sugar": 0.0,
            "sodium": 0.0,
            "calcium": 0.0,
            "iron": 0.0,
            "potassium": 0.0,
            "vitaminA": 0.0,
            "vitaminC": 0.0,
            "cholesterol": 0.0,
            "saturatedFat": 0.0,
            "monoUnsaturatedFat": 0.0,
            "polyUnsaturatedFat": 0.0,
          },
          "id": "manual_${DateTime.now().millisecondsSinceEpoch}",
          "is_enable": true,
        }
      ],
    };

    // Call the UpdateMeal API
    final response = await UpdateMealCall.call(
      accessToken: accessToken,
      jsonJson: mealData,
    );

    // Check the API response
    if (response.statusCode == 200) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              isEnglish ? 'Food logged successfully' : 'המזון נרשם בהצלחה'),
        ),
      );

      // Redirect to dashboard screen with refresh flag
      context.goNamed(
        'dashboard',
        queryParameters: {
          'refresh': 'true',
        },
      );
    } else {
      final errorMessage = UpdateMealCall.error(response.jsonBody) ??
          (isEnglish ? 'Failed to log meal.' : 'נכשל ברישום המזון.');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
        ),
      );
      return;
    }

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    return MeasuredWidget(
        name: 'LogEmptyMeal',
        builder: (context) => Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.of(context).pop(),
            ),
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            titleSpacing: 0,
            title: Text(
              isEnglish ? 'Log Empty Meal' : 'רשום ארוחה ריקה',
              style: FlutterFlowTheme.of(context)
                  .headlineMedium
                  .copyWith(color: Colors.black, fontSize: 18),
            ),
            centerTitle: false,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: _editFoodName,
                          child: Row(
                            children: [
                              Text(
                                foodName ?? 'טוען...',
                                style: FlutterFlowTheme.of(context)
                                    .headlineLarge
                                    .copyWith(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 20,
                                    ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(width: 8),
                              Icon(
                                Icons.edit,
                                size: 20,
                                color: FlutterFlowTheme.of(context).primary,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 18),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        isEnglish ? 'Serving Amount' : 'כמות מנה',
                        style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                              fontWeight: FontWeight.w500,
                              color: FlutterFlowTheme.of(context).primaryText,
                            ),
                      ),
                      const SizedBox(width: 10),
                      Row(
                        children: [
                          Container(
                            width: 120,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.black, width: 1),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.remove),
                                  onPressed: () {
                                    setState(() {
                                      if (servingAmount > 1) {
                                        servingAmount--;
                                        _scaleNutritionValues();
                                      }
                                    });
                                  },
                                ),
                                Text(
                                  servingAmount.toString(),
                                  style: FlutterFlowTheme.of(context)
                                      .headlineMedium
                                      .override(
                                        fontWeight: FontWeight.normal,
                                        fontSize: 18,
                                      ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.add),
                                  onPressed: () {
                                    setState(() {
                                      servingAmount++;
                                      _scaleNutritionValues();
                                    });
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 25),
                  Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          border:
                              Border.all(color: FlutterFlowTheme.of(context).grey),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 15),
                          child: Row(
                            children: [
                              const Icon(Icons.local_fire_department,
                                  color: Colors.black, size: 28),
                              const SizedBox(width: 12),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _editNutrient(
                                      NutrientType.calories, caloriesCtrl),
                                  child: AbsorbPointer(
                                    child: TextField(
                                      readOnly: true,
                                      cursorColor: Colors.black,
                                      controller: caloriesCtrl,
                                      keyboardType: TextInputType.number,
                                      style: FlutterFlowTheme.of(context)
                                          .headlineMedium
                                          .override(
                                            fontSize: 20,
                                            fontWeight: FontWeight.normal,
                                          ),
                                      decoration: InputDecoration(
                                        labelStyle: FlutterFlowTheme.of(context)
                                            .headlineMedium
                                            .copyWith(
                                              fontWeight: FontWeight.normal,
                                              fontSize: 24,
                                              color: FlutterFlowTheme.of(context)
                                                  .primaryText,
                                            ),
                                        border: InputBorder.none,
                                        labelText:
                                            isEnglish ? 'Calories' : 'קלוריות',
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                    color: FlutterFlowTheme.of(context).grey),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 5, horizontal: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/Frame.svg',
                                      width: 18.0,
                                      height: 18.0,
                                      fit: BoxFit.contain,
                                    ),
                                    const SizedBox(width: 10),
                                    Flexible(
                                      child: GestureDetector(
                                        onTap: () => _editNutrient(
                                            NutrientType.protein, proteinCtrl),
                                        child: AbsorbPointer(
                                          child: TextField(
                                            readOnly: true,
                                            cursorColor: Colors.black,
                                            controller: proteinCtrl,
                                            keyboardType: TextInputType.number,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyLarge
                                                .copyWith(
                                                    fontWeight: FontWeight.bold),
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(
                                                      context)
                                                  .headlineMedium
                                                  .copyWith(
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                    color:
                                                        FlutterFlowTheme.of(context)
                                                            .primaryText,
                                                  ),
                                              border: InputBorder.none,
                                              labelText:
                                                  isEnglish ? 'Protein' : 'חלבון',
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                    color: FlutterFlowTheme.of(context).grey),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 5, horizontal: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      'assets/images/wheat-barley_svgrepo.com.svg',
                                      width: 20.0,
                                      height: 20.0,
                                      color: Colors.orange,
                                      fit: BoxFit.cover,
                                    ),
                                    const SizedBox(width: 10),
                                    Flexible(
                                      child: GestureDetector(
                                        onTap: () => _editNutrient(
                                            NutrientType.carbs, carbsCtrl),
                                        child: AbsorbPointer(
                                          child: TextField(
                                            readOnly: true,
                                            cursorColor: Colors.black,
                                            controller: carbsCtrl,
                                            keyboardType: TextInputType.number,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyLarge
                                                .copyWith(
                                                    fontWeight: FontWeight.bold),
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(
                                                      context)
                                                  .headlineMedium
                                                  .copyWith(
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                    color:
                                                        FlutterFlowTheme.of(context)
                                                            .primaryText,
                                                  ),
                                              border: InputBorder.none,
                                              labelText:
                                                  isEnglish ? 'Carbs' : 'פחמימות',
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                    color: FlutterFlowTheme.of(context).grey),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 5, horizontal: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(Icons.opacity,
                                        color: Colors.blue, size: 18),
                                    const SizedBox(width: 10),
                                    Flexible(
                                      child: GestureDetector(
                                        onTap: () => _editNutrient(
                                            NutrientType.fats, fatsCtrl),
                                        child: AbsorbPointer(
                                          child: TextField(
                                            readOnly: true,
                                            cursorColor: Colors.black,
                                            controller: fatsCtrl,
                                            keyboardType: TextInputType.number,
                                            style: FlutterFlowTheme.of(context)
                                                .bodyLarge
                                                .copyWith(
                                                    fontWeight: FontWeight.bold),
                                            decoration: InputDecoration(
                                              labelStyle: FlutterFlowTheme.of(
                                                      context)
                                                  .headlineMedium
                                                  .copyWith(
                                                    fontWeight: FontWeight.normal,
                                                    fontSize: 18,
                                                    color:
                                                        FlutterFlowTheme.of(context)
                                                            .primaryText,
                                                  ),
                                              border: InputBorder.none,
                                              labelText:
                                                  isEnglish ? 'Fats' : 'שומנים',
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _logFoodItem,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(24),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                      child: Text(
                        isEnglish ? 'Log' : 'עדכן',
                        style: FlutterFlowTheme.of(context).titleMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        )
    );
  }
}

import 'package:collection/collection.dart';

enum Gender {
  Male,
  Female,
  Other,
}

enum ImageType {
  oneStar,
  twoStar,
  threeStar,
  loseWeight,
  toneUp,
  gainWeight,
  lackConsistancy,
  unhealthyHabit,
  lackSupport,
  busySchedule,
  lackMotivation,
  eatLiveHealthy,
  boostEnergy,
  stayMotivate,
  feelBody,
  kg1,
  kg2,
  kg8,
  kg10,
}

enum GoalDuration {
  oneWeek,
  twoWeek,
  oneMonth,
  threeMonth,
}

enum NutritionDuration { thisWeek, lastWeek, twoWeekAgo, threeWeekAgo }

enum NutritionType {
  protein,
  carbs,
  fats,
}

extension FFEnumExtensions<T extends Enum> on T {
  String serialize() => name;
}

extension FFEnumListExtensions<T extends Enum> on Iterable<T> {
  T? deserialize(String? value) =>
      firstWhereOrNull((e) => e.serialize() == value);
}

T? deserializeEnum<T>(String? value) {
  switch (T) {
    case (Gender):
      return Gender.values.deserialize(value) as T?;
    case (ImageType):
      return ImageType.values.deserialize(value) as T?;
    case (GoalDuration):
      return GoalDuration.values.deserialize(value) as T?;
    case (NutritionType):
      return NutritionType.values.deserialize(value) as T?;
    default:
      return null;
  }
}

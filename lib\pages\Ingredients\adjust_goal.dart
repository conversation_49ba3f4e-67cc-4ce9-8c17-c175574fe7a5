import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/componentes/circular_progress/circular_progress_widget.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'dart:developer' as developer; // For logging

// Assuming these are available in your project
import '/backend/api_requests/api_calls.dart'; // For UpdateUserInfoCall and DashboardDataCall
import '/backend/schema/structs/index.dart'; // For UserDataStruct and DashboardDataStruct
import '/flutter_flow/flutter_flow_util.dart';

class AdjustGoal extends StatefulWidget {
  const AdjustGoal({
    super.key,
    this.initialCalories = 0,
    this.initialProtein = 0,
    this.initialCarbs = 0,
    this.initialFats = 0,
    this.userData, // Pass the current user data
    this.dashboardData, // Pass the dashboard data
  });

  final int initialCalories;
  final int initialProtein;
  final int initialCarbs;
  final int initialFats;
  final UserDataStruct? userData;
  final DashboardDataStruct? dashboardData; // Add dashboard data parameter

  @override
  State<AdjustGoal> createState() => _AdjustGoalState();
}

class _AdjustGoalState extends State<AdjustGoal> {
  late final TextEditingController calorieController;
  late final TextEditingController proteinController;
  late final TextEditingController carbController;
  late final TextEditingController fatController;

  final FocusNode calorieFocus = FocusNode();
  final FocusNode proteinFocus = FocusNode();
  final FocusNode carbFocus = FocusNode();
  final FocusNode fatFocus = FocusNode();

  bool isLoading = false; // Add loading state
  bool isInitializing = true; // Add initialization state
  DashboardDataStruct? dashboardData; // Store dashboard data

  @override
  void initState() {
    super.initState();
    // Initialize controllers with default values first
    calorieController = TextEditingController(text: '0');
    proteinController = TextEditingController(text: '0');
    carbController = TextEditingController(text: '0');
    fatController = TextEditingController(text: '0');

    calorieFocus.addListener(() => setState(() {}));
    proteinFocus.addListener(() => setState(() {}));
    carbFocus.addListener(() => setState(() {}));
    fatFocus.addListener(() => setState(() {}));

    // Add listeners to update the pie chart when values change
    proteinController.addListener(_updateChart);
    carbController.addListener(_updateChart);
    fatController.addListener(_updateChart);

    // Load dashboard data
    _loadDashboardData();
  }

  // Function to load dashboard data from API
  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        isInitializing = true;
      });

      // Call DashboardDataCall API
      final apiResult = await DashboardDataCall.call(
        accessToken: FFAppState().authToken,
        date: DateTime.now().toIso8601String().split('T')[0], // Today's date
        timezone: DateTime.now().timeZoneName,
      );

      if (apiResult.succeeded) {
        final fetchedData = DashboardDataCall.data(apiResult.jsonBody);
        if (fetchedData != null) {
          setState(() {
            dashboardData = fetchedData;
            // Update controllers with fetched data
            _updateControllersWithDashboardData(fetchedData);
          });
        } else {
          // Fallback to constructor values if API data is null
          _updateControllersWithConstructorValues();
        }
      } else {
        // Fallback to constructor values if API call fails
        _updateControllersWithConstructorValues();
        developer.log(
            'DashboardDataCall failed: ${DashboardDataCall.error(apiResult.jsonBody)}');
      }
    } catch (e) {
      // Fallback to constructor values if exception occurs
      _updateControllersWithConstructorValues();
      developer.log('Error loading dashboard data: $e');
    } finally {
      setState(() {
        isInitializing = false;
      });
    }
  }

  // Helper function to update controllers with dashboard data
  void _updateControllersWithDashboardData(DashboardDataStruct data) {
    // Helper function to parse string values and extract numbers
    int parseSafely(String? value) {
      if (value == null || value.isEmpty) return 0;
      final numeric = value.replaceAll(RegExp(r'[^0-9]'), '');
      return int.tryParse(numeric) ?? 0;
    }

    calorieController.text = (data.calories.caloriesRequired ?? 0).toString();
    proteinController.text =
        parseSafely(data.proteins.proteinsRequired).toString();
    carbController.text = parseSafely(data.carbs.carbsRequired).toString();
    fatController.text = parseSafely(data.fats.fatsRequired).toString();
  }

  // Helper function to update controllers with constructor values
  void _updateControllersWithConstructorValues() {
    calorieController.text = widget.initialCalories.toString();
    proteinController.text = widget.initialProtein.toString();
    carbController.text = widget.initialCarbs.toString();
    fatController.text = widget.initialFats.toString();
  }

  void _updateChart() {
    setState(() {}); // Rebuild the widget to update the pie chart
  }

  @override
  void dispose() {
    calorieController.dispose();
    proteinController.dispose();
    carbController.dispose();
    fatController.dispose();
    calorieFocus.dispose();
    proteinFocus.dispose();
    carbFocus.dispose();
    fatFocus.dispose();
    proteinController.removeListener(_updateChart);
    carbController.removeListener(_updateChart);
    fatController.removeListener(_updateChart);
    super.dispose();
  }

  // Function to update user details via API
  Future<void> _updateUserDetails() async {
    setState(() {
      isLoading = true; // Show loading state
    });

    // Helper function to get fallback values from dashboard data
    int getFallbackValue(
        String controllerText, int constructorValue, int? dashboardValue) {
      final parsed = int.tryParse(controllerText);
      if (parsed != null && parsed > 0) return parsed;
      return dashboardValue ?? constructorValue;
    }

    // Extract the updated values from the controllers with fallback to dashboard data
    final updatedCalories = getFallbackValue(calorieController.text,
        widget.initialCalories, dashboardData?.calories.caloriesRequired);
    final updatedProtein = getFallbackValue(
        proteinController.text,
        widget.initialProtein,
        int.tryParse(dashboardData?.proteins.proteinsRequired
                ?.replaceAll(RegExp(r'[^0-9]'), '') ??
            '0'));
    final updatedCarbs = getFallbackValue(
        carbController.text,
        widget.initialCarbs,
        int.tryParse(dashboardData?.carbs.carbsRequired
                ?.replaceAll(RegExp(r'[^0-9]'), '') ??
            '0'));
    final updatedFats = getFallbackValue(
        fatController.text,
        widget.initialFats,
        int.tryParse(dashboardData?.fats.fatsRequired
                ?.replaceAll(RegExp(r'[^0-9]'), '') ??
            '0'));

    // Create a map with the updated values using the correct field names for the API
    final updatedData = widget.userData?.toMap() ?? {};
    updatedData['calories_required'] = updatedCalories;
    updatedData['proteins_required'] = updatedProtein;
    updatedData['carbs_required'] = updatedCarbs;
    updatedData['fats_required'] = updatedFats;

    // Log the payload for debugging
    developer.log('Payload sent to API: ${updatedData.toString()}');
    developer.log('Auth token: ${FFAppState().authToken}');

    // Make the API call
    final apiResult = await UpdateUserInfoCall.call(
      accessToken: FFAppState().authToken,
      jsonJson: updatedData,
    );

    // Log the API response for debugging
    developer.log('API Response: ${apiResult.jsonBody.toString()}');

    setState(() {
      isLoading = false; // Hide loading state
    });

    if (apiResult.succeeded) {
      UserDataStruct? userData =
          UpdateUserInfoCall.userData(apiResult.jsonBody);
      if (userData != null) {
        FFAppState().savedUserData = userData;
      }
      // Log the updated state for debugging
      developer.log(
          'Updated FFAppState: ${FFAppState().savedUserData.toMap().toString()}');

      // Show success snackbar
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            FFLocalizations.of(context).languageCode == 'en'
                ? 'Your details updated successfully!'
                : 'הפרטים שלך עודכנו בהצלחה!',
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).success,
                  letterSpacing: 0.0,
                ),
          ),
          duration: const Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).alternate,
        ),
      );
      // Update FFAppState().savedUserData with new values
      FFAppState().savedUserData.caloriesRequired = updatedCalories;
      FFAppState().savedUserData.proteinsRequired = updatedProtein;
      FFAppState().savedUserData.carbsRequired = updatedCarbs;
      FFAppState().savedUserData.fatsRequired = updatedFats;
      // Navigate back with the updated values from the controllers
      Navigator.of(context).pop({
        'calories': updatedCalories,
        'protein': updatedProtein,
        'carbs': updatedCarbs,
        'fats': updatedFats,
      });
    } else {
      // Show error snackbar
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            UpdateUserInfoCall.error(apiResult.jsonBody) ??
                'Error updating details',
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).error,
                  letterSpacing: 0.0,
                ),
          ),
          duration: const Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).alternate,
        ),
      );
      // Revert the controllers to the dashboard data values on failure
      setState(() {
        if (dashboardData != null) {
          _updateControllersWithDashboardData(dashboardData!);
        } else {
          _updateControllersWithConstructorValues();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'AdjustGoal',
        builder: (context) => Scaffold(
              appBar: AppBar(
                leading: IconButton(
                  icon: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(100),
                    ),
                    child: const Icon(Icons.arrow_back, color: Colors.black),
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                elevation: 0,
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
                centerTitle: true,
              ),
              backgroundColor: Colors.white,
              body: Stack(
                children: [
                  SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 10),
                            Text(
                              isEnglish ? 'Adjust goals' : 'התאם יעדים',
                              style: FlutterFlowTheme.of(context)
                                  .headlineLarge
                                  .override(
                                    fontFamily: 'SFHebrew',
                                    color: FlutterFlowTheme.of(context).primary,
                                    fontSize: 24.0,
                                    letterSpacing: 0.0,
                                  ),
                            ),
                            const SizedBox(height: 16),
                            _buildPieChart(),
                            const SizedBox(height: 30),
                            _GoalRow(
                              image: const Icon(Icons.local_fire_department,
                                  color: Colors.black, size: 22),
                              iconColor: Colors.black,
                              label: isEnglish ? 'Calorie goal' : 'יעד קלוריות',
                              controller: calorieController,
                              focusNode: calorieFocus,
                              isSelected: calorieFocus.hasFocus,
                              fillColor: const Color(0xFFF8F8FB),
                              textColor: Colors.black,
                            ),
                            const SizedBox(height: 14),
                            _buildMacroInputRow(
                              image: SvgPicture.asset(
                                'assets/images/Frame.svg',
                                width: 22.0,
                                height: 22.0,
                                fit: BoxFit.contain,
                              ),
                              iconColor: Colors.red,
                              label: isEnglish ? 'Protein goal' : 'יעד חלבון',
                              controller: proteinController,
                              focusNode: proteinFocus,
                              isSelected: proteinFocus.hasFocus,
                            ),
                            const SizedBox(height: 14),
                            _buildMacroInputRow(
                              image: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: SvgPicture.asset(
                                  'assets/images/wheat-barley_svgrepo.com.svg',
                                  width: 22.0,
                                  height: 22.0,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              iconColor: Colors.orange,
                              label: isEnglish ? 'Carb goal' : 'יעד פחמימות',
                              controller: carbController,
                              focusNode: carbFocus,
                              isSelected: carbFocus.hasFocus,
                            ),
                            const SizedBox(height: 14),
                            _buildMacroInputRow(
                              image: SvgPicture.asset(
                                'assets/images/drop-invert_svgrepo.com.svg',
                                width: 22.0,
                                height: 22.0,
                                fit: BoxFit.contain,
                              ),
                              iconColor: Colors.blue,
                              label: isEnglish ? 'Fat goal' : 'יעד שומן',
                              controller: fatController,
                              focusNode: fatFocus,
                              isSelected: fatFocus.hasFocus,
                            ),
                            const SizedBox(height: 50),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 15),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: OutlinedButton(
                                      onPressed: () {
                                        setState(() {
                                          // Reset to dashboard data values if available, otherwise use constructor values
                                          if (dashboardData != null) {
                                            _updateControllersWithDashboardData(
                                                dashboardData!);
                                          } else {
                                            _updateControllersWithConstructorValues();
                                          }
                                        });
                                      },
                                      style: OutlinedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        side: const BorderSide(
                                            color: Colors.black),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10),
                                      ),
                                      child: Text(
                                        isEnglish ? 'Reset' : 'אפס',
                                        style: const TextStyle(
                                            fontSize: 18, color: Colors.black),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed:
                                          isLoading ? null : _updateUserDetails,
                                      // Disable button while loading
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.black,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10),
                                      ),
                                      child: Text(
                                        isEnglish ? 'Done' : 'סיום',
                                        style: const TextStyle(
                                            fontSize: 18, color: Colors.white),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.fromLTRB(10, 0, 10, 25),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: OutlinedButton(
                                      onPressed: () async {
                                        await context.pushNamed(
                                          'quiz',
                                          queryParameters: {
                                            'isForAdjustGoal': serializeParam(
                                              true,
                                              ParamType.bool,
                                            ),
                                          }.withoutNulls,
                                        );
                                        context.pop();
                                      },
                                      style: OutlinedButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(30),
                                        ),
                                        side: const BorderSide(
                                            color: Colors.black),
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10),
                                      ),
                                      child: Text(
                                        isEnglish
                                            ? 'Auto generate goal'
                                            : 'יצירת יעד אוטומטית',
                                        style: const TextStyle(
                                            fontSize: 18, color: Colors.black),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (isLoading || isInitializing)
                    Container(
                      color: Colors.black.withOpacity(0.3),
                      child: Center(
                          child: Dialog(
                        elevation: 0,
                        insetPadding: EdgeInsets.zero,
                        backgroundColor: Colors.transparent,
                        alignment: AlignmentDirectional(0.0, 0.0)
                            .resolve(Directionality.of(context)),
                        child: WebViewAware(
                          child: Container(
                            height: 100.0,
                            width: 100.0,
                            child: CircularProgressWidget(),
                          ),
                        ),
                      )),
                    ),
                ],
              ),
            ));
  }

  Widget _buildPieChart() {
    double protein = double.tryParse(proteinController.text) ?? 0.0;
    double carbs = double.tryParse(carbController.text) ?? 0.0;
    double fats = double.tryParse(fatController.text) ?? 0.0;

    // Prevent rendering issues when total is 0
    double total = protein + carbs + fats;
    if (total == 0) {
      // Set equal dummy values to make chart visible
      protein = carbs = fats = 1;
    }

    return Container(
      height: 150,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: PieChart(
        PieChartData(
          sections: [
            PieChartSectionData(
              color: Colors.red,
              value: protein.abs(),
              radius: 10,
              showTitle: false,
              badgePositionPercentageOffset: 1.8,
              badgeWidget: Container(
                width: 45,
                height: 25,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.white, width: 1)),
                child: Text(
                  '${(total == 0 ? 0 : protein).toStringAsFixed(0)}g',
                  style: FlutterFlowTheme.of(context)
                      .titleSmall
                      .override(color: Colors.white, fontSize: 12),
                ),
                padding: EdgeInsets.zero,
              ),
            ),
            PieChartSectionData(
              color: Colors.orange,
              value: carbs.abs(),
              radius: 10,
              showTitle: false,
              badgePositionPercentageOffset: 1.8,
              badgeWidget: Container(
                width: 45,
                height: 25,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.white, width: 1)),
                child: Text(
                  '${(total == 0 ? 0 : carbs).toStringAsFixed(0)}g',
                  style: FlutterFlowTheme.of(context)
                      .titleSmall
                      .override(color: Colors.white, fontSize: 12),
                ),
                padding: EdgeInsets.zero,
              ),
            ),
            PieChartSectionData(
              color: Colors.blue,
              value: fats.abs(),
              radius: 10,
              showTitle: false,
              badgePositionPercentageOffset: 1.8,
              badgeWidget: Container(
                width: 45,
                height: 25,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.white, width: 1)),
                child: Text(
                  '${(total == 0 ? 0 : fats).toStringAsFixed(0)}g',
                  style: FlutterFlowTheme.of(context)
                      .titleSmall
                      .override(color: Colors.white, fontSize: 12),
                ),
                padding: EdgeInsets.zero,
              ),
            ),
          ],
          sectionsSpace: 3,
          centerSpaceRadius: 35,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  Widget _buildMacroInputRow({
    required Widget image,
    required Color iconColor,
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    required bool isSelected,
  }) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5.0),
      child: Row(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                child: CircularProgressIndicator(
                  value: 0.5,
                  strokeWidth: 3,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(iconColor),
                ),
                height: 50,
                width: 50,
              ),
              image,
            ],
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF8F8FB),
                border: Border.all(
                  color: isSelected ? Colors.black : Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(14),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 4),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          label,
                          style: const TextStyle(
                              fontSize: 15, color: Colors.black),
                        ),
                        TextField(
                          controller: controller,
                          focusNode: focusNode,
                          cursorColor: FlutterFlowTheme.of(context).primaryText,
                          keyboardType: TextInputType.number,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    isEnglish ? 'g' : 'ג׳',
                    style: TextStyle(fontSize: 18, color: Colors.black54),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _GoalRow extends StatelessWidget {
  final Widget image;
  final Color iconColor;
  final String label;
  final TextEditingController controller;
  final FocusNode focusNode;
  final bool isSelected;
  final Color fillColor;
  final Color textColor;

  const _GoalRow({
    required this.image,
    required this.iconColor,
    required this.label,
    required this.controller,
    required this.focusNode,
    required this.isSelected,
    required this.fillColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5.0),
      child: Row(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                child: CircularProgressIndicator(
                  value: 0.5,
                  strokeWidth: 3,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(iconColor),
                ),
                height: 50,
                width: 50,
              ),
              image,
            ],
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: fillColor,
                border: Border.all(
                  color: isSelected ? Colors.black : Colors.transparent,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(14),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(fontSize: 15, color: textColor),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: controller,
                          focusNode: focusNode,
                          cursorColor: FlutterFlowTheme.of(context).primaryText,
                          keyboardType: TextInputType.number,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            isDense: true,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ),
                      Text(
                        isEnglish ? 'kcal' : 'קק"ל',
                        style: TextStyle(fontSize: 18, color: Colors.black54),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

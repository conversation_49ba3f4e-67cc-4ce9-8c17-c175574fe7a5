import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../componentes/language_switch/language_switch_widget.dart';
import '/auth/firebase_auth/auth_util.dart';
import '/backend/api_requests/api_calls.dart';
import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'settings_model.dart';
export 'settings_model.dart';
import 'dart:ui' as ui;
import '/flutter_flow/custom_functions.dart' as functions;

class SettingsWidget extends StatefulWidget {
  const SettingsWidget({super.key});

  @override
  State<SettingsWidget> createState() => _SettingsWidgetState();
}

class _SettingsWidgetState extends State<SettingsWidget> {
  late SettingsModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SettingsModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'Settings',
        builder: (context) => Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          appBar: AppBar(
                  backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                  automaticallyImplyLeading: false,
                  actions: [],
                  flexibleSpace: FlexibleSpaceBar(
                    titlePadding: const EdgeInsets.all(0),
                    title: Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          0.0,
                          valueOrDefault<double>(
                            FFAppState().topPadding + 16,
                            0.0,
                          ),
                          0.0,
                          0.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text(
                                FFLocalizations.of(context).getText(
                                  'ifi2gif7' /* Settings */,
                                ),
                                style: FlutterFlowTheme.of(context)
                                    .headlineMedium
                                    .override(
                                      fontFamily: 'SFHebrew',
                                      color: FlutterFlowTheme.of(context).primary,
                                      letterSpacing: 0.0,
                                    ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    centerTitle: false,
                    expandedTitleScale: 1.0,
                  ),
                  toolbarHeight: 80.0,
                  elevation: 2.0,
                ),
          body: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListView(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          logFirebaseEvent('materialList_Item_2_navigate_to');

                          context.pushNamed('personal_detail_view');
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'rrqho7ce' /* Personal Details */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          logFirebaseEvent('materialList_Item_2_navigate_to');

                          var result = await context.pushNamed(
                            'adjust_goal',
                            queryParameters: {
                              'initialCalories': serializeParam(
                                FFAppState().savedUserData.caloriesRequired,
                                ParamType.int,
                              ),
                              'initialProtein': serializeParam(
                                FFAppState().savedUserData.proteinsRequired,
                                ParamType.int,
                              ),
                              'initialCarbs': serializeParam(
                                FFAppState().savedUserData.carbsRequired,
                                ParamType.int,
                              ),
                              'initialFats': serializeParam(
                                FFAppState().savedUserData.fatsRequired,
                                ParamType.int,
                              ),
                            }.withoutNulls,
                          );
                          // updateUserDetails();
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'hwmiimuz' /* Adjust Goals */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          logFirebaseEvent('materialList_Item_2_navigate_to');

                          // await launchUrl(
                          //   Uri.parse(appPrivacy),
                          //   mode: LaunchMode.inAppWebView,
                          // );

                          context.pushNamed(
                            'teams_privacy',
                            queryParameters: {
                              'privacy': serializeParam(
                                true,
                                ParamType.bool,
                              ),
                              'url': serializeParam(
                                appPrivacy,
                                ParamType.String,
                              ),
                            }.withoutNulls,
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'wgtiqy8u' /* Privacy Policy */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          logFirebaseEvent('materialList_Item_2_navigate_to');

                          // await launchUrl(
                          //   Uri.parse(appTerms),
                          //   mode: LaunchMode.inAppWebView,
                          // );

                          context.pushNamed(
                            'teams_privacy',
                            queryParameters: {
                              'privacy': serializeParam(
                                false,
                                ParamType.bool,
                              ),
                              'url': serializeParam(
                                appTerms,
                                ParamType.String,
                              ),
                            }.withoutNulls,
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'h8qenkp8' /* Terms & Conditions */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    /// Suggestions and Bugs
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          await context.pushNamed(
                            'suggestions_bugs',
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'suggestions_bugs' /* Suggestions and Bugs */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                    fontFamily: 'SFHebrew',
                                    letterSpacing: 0.0,
                                  ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          await context.pushNamed(
                            'nutrition_citations',
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'cljmrv5a',
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    ///  Subscription
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 1.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.pushNamed(
                            'subscription',
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(),
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    'wgtil88u' /* Manage Subscription */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .titleLarge
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                                Icon(
                                  Icons.chevron_right_rounded,
                                  color: FlutterFlowTheme.of(context).secondaryText,
                                  size: 24.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    ///  language
                    // Builder(builder: (context) {
                    //   return Container(
                    //     width: double.infinity,
                    //     decoration: BoxDecoration(),
                    //     child: wrapWithModel(
                    //       model: _model.languageSwitchModel,
                    //       updateCallback: () => safeSetState(() {}),
                    //       child: LanguageSwitchWidget(onLanguageChanged: (languageCode) async {
                    //         _model.apiResultUpdateProfile =
                    //             await UpdateUserInfoCall.call(
                    //           accessToken: FFAppState().authToken,
                    //           jsonJson: functions.getUserUpdateRequestJson(
                    //               FFAppState().savedUserData),
                    //         );
                    //       }),
                    //     ),
                    //   );
                    // }),
                  ],
                ),

                /// Contact Us Section
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    textDirection:
                        isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        child: Text(
                          FFLocalizations.of(context).getText(
                            'contact_us' /* Contact Us */,
                          ),
                          style:
                              FlutterFlowTheme.of(context).titleMedium.override(
                                    fontFamily: 'SFHebrew',
                                    letterSpacing: 0.0,
                                  ),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          textDirection:
                              isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
                          children: [
                            SelectableText(
                              '${FFLocalizations.of(context).getText('email')}: <EMAIL>',
                              onTap: () {
                                String email = '<EMAIL>';
                                launchURL('mailto:$email');
                              },
                              style: FlutterFlowTheme.of(context).bodyLarge.override(
                                    fontFamily: 'SFHebrew',
                                    letterSpacing: 0.0,
                                  ),
                            ),
                            SelectableText(
                              '${FFLocalizations.of(context).getText('site')}: www.calcounti.com',
                              onTap: () {
                                String site = 'calcounti.com';
                                launchURL(site);
                              },
                              style: FlutterFlowTheme.of(context).bodyLarge.override(
                                    fontFamily: 'SFHebrew',
                                    letterSpacing: 0.0,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding:
                      EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                      child: Text(
                        FFLocalizations.of(context).getText(
                          '2w3q4e32' /* App Versions */,
                        ),
                        style: FlutterFlowTheme.of(context).titleLarge.override(
                          fontFamily: 'SFHebrew',
                          fontSize: 16.0,
                          letterSpacing: 0.0,
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                      EdgeInsetsDirectional.fromSTEB(16.0, 4.0, 0.0, 0.0),
                      child: AnimatedDefaultTextStyle(
                        style: FlutterFlowTheme.of(context).labelMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                        ),
                        duration: Duration(milliseconds: 600),
                        curve: Curves.easeIn,
                        child: Text(
                          FFAppState().appVersion,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 0.0, 0.0),
                          child: FFButtonWidget(
                            onPressed: () async {
                              Function() _navigate = () {};
                              logFirebaseEvent('Button_alert_dialog');
                              var confirmDialogResponse = await showDialog<bool>(
                                    context: context,
                                    builder: (alertDialogContext) {
                                      return WebViewAware(
                                        child: AlertDialog(
                                          title: Text(
                                              isEnglish ? 'Logging Out?' : 'מתנתק?'),
                                          content: Text(isEnglish
                                              ? 'You can log back in anytime to resume where you left off.'
                                              : 'תוכל להתחבר מחדש בכל עת ולהמשיך מהנקודה בה הפסקת.'),
                                          actions: [
                                            TextButton(
                                              onPressed: () => Navigator.pop(
                                                  alertDialogContext, false),
                                              child: Text(
                                                  FFLocalizations.of(context).getText(
                                                '5efik18d' /* Cancel */,
                                              )),
                                            ),
                                            TextButton(
                                              onPressed: () => Navigator.pop(
                                                  alertDialogContext, true),
                                              child: Text(
                                                  FFLocalizations.of(context).getText(
                                                '4efik18d' /* Confirm */,
                                              )),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ) ??
                                  false;
                              if (confirmDialogResponse) {
                                logFirebaseEvent('Button_backend_call');
                                _model.logoutRes = await UserLogoutCall.call(
                                  accessToken: FFAppState().authToken,
                                );

                                logFirebaseEvent('Button_update_app_state');
                                FFAppState().deleteSavedUserData();
                                FFAppState().savedUserData = UserDataStruct();

                                FFAppState().isOnboardingCompleted = false;
                                FFAppState().deleteAuthToken();
                                FFAppState().authToken = '';

                                safeSetState(() {});
                                logFirebaseEvent('Button_auth');
                                GoRouter.of(context).prepareAuthEvent();
                                await authManager.signOut();
                                GoRouter.of(context).clearRedirectLocation();

                                _navigate = () =>
                                    context.goNamedAuth('splash', context.mounted);
                              }

                              _navigate();

                              safeSetState(() {});
                            },
                            text: FFLocalizations.of(context).getText(
                              'nug8zy7m' /* Log Out */,
                            ),
                            icon: Icon(
                              Icons.logout_rounded,
                              size: 18.0,
                            ),
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  24.0, 0.0, 24.0, 0.0),
                              iconPadding:
                                  EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                              textStyle:
                                  FlutterFlowTheme.of(context).labelMedium.override(
                                        fontFamily: 'SFHebrew',
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                      ),
                              elevation: 0.0,
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).alternate,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(50.0),
                            ),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(16.0, 12.0, 0.0, 0.0),
                          child: FFButtonWidget(
                            onPressed: () async {
                              logFirebaseEvent('Button_alert_dialog');
                              var confirmDialogResponse = await showDialog<bool>(
                                    context: context,
                                    builder: (alertDialogContext) {
                                      return WebViewAware(
                                        child: AlertDialog(
                                          title: Text(isEnglish
                                              ? 'Confirm Account Deletion'
                                              : 'אישור מחיקת חשבון'),
                                          content: Text(isEnglish
                                              ? 'Are You Sure You Want to Delete Your Account?  Your account and all associated information will be permanently removed.'
                                              : 'האם אתה בטוח שברצונך למחוק את החשבון שלך? החשבון וכל המידע הקשור אליו יימחקו לצמיתות.'),
                                          actions: [
                                            TextButton(
                                              onPressed: () => Navigator.pop(
                                                  alertDialogContext, false),
                                              child: Text(
                                                  FFLocalizations.of(context).getText(
                                                '5efik18d' /* Cancel */,
                                              )),
                                            ),
                                            TextButton(
                                              onPressed: () => Navigator.pop(
                                                  alertDialogContext, true),
                                              child: Text(
                                                  FFLocalizations.of(context).getText(
                                                '4efik18d' /* Confirm */,
                                              )),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  ) ??
                                  false;
                              if (confirmDialogResponse) {
                                logFirebaseEvent('Button_backend_call');
                                _model.userDeleteRes = await UserDeleteCall.call(
                                  accessToken: FFAppState().authToken,
                                );

                                if ((_model.userDeleteRes?.succeeded ?? true)) {
                                  logFirebaseEvent('Button_update_app_state');
                                  FFAppState().deleteSavedUserData();
                                  FFAppState().savedUserData = UserDataStruct();
                                  FFAppState().isOnboardingCompleted = false;
                                  FFAppState().deleteAuthToken();
                                  FFAppState().authToken = '';
                                  safeSetState(() {});
                                  logFirebaseEvent('Button_auth');
                                  await authManager.deleteUser(context);
                                  logFirebaseEvent('Button_navigate_to');
                                  context.goNamed('entry_screen');
                                } else {
                                  logFirebaseEvent('Button_show_snack_bar');
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        getJsonField(
                                          (_model.userDeleteRes?.jsonBody ?? ''),
                                          r'''$.error''',
                                        ).toString(),
                                        style: FlutterFlowTheme.of(context)
                                            .labelMedium
                                            .override(
                                              fontFamily: 'SFHebrew',
                                              color: FlutterFlowTheme.of(context)
                                                  .primaryText,
                                              letterSpacing: 0.0,
                                            ),
                                      ),
                                      duration: Duration(milliseconds: 4000),
                                      backgroundColor:
                                          FlutterFlowTheme.of(context).secondary,
                                    ),
                                  );
                                }
                              }

                              safeSetState(() {});
                            },
                            text: FFLocalizations.of(context).getText(
                              'uddyw1ll' /* Delete Account */,
                            ),
                            icon: Icon(
                              Icons.delete_outline_rounded,
                              size: 18.0,
                            ),
                            options: FFButtonOptions(
                              height: 40.0,
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  24.0, 0.0, 24.0, 0.0),
                              iconPadding:
                                  EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              color: FlutterFlowTheme.of(context).secondaryBackground,
                              textStyle:
                                  FlutterFlowTheme.of(context).labelMedium.override(
                                        fontFamily: 'SFHebrew',
                                        color: Color(0xFFFF0000),
                                        fontSize: 16.0,
                                        letterSpacing: 0.0,
                                      ),
                              elevation: 0.0,
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).alternate,
                                width: 1.0,
                              ),
                              borderRadius: BorderRadius.circular(50.0),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: FFAppState().bottomPadding)
              ].addToEnd(SizedBox(
                  height: valueOrDefault<double>(
                FFAppState().bottomPadding + 30,
                80.0,
              ))),
            ),
          ),
        )
    );
  }

  Future<void> updateUserDetails() async {
    var _shouldSetState = false;
    logFirebaseEvent('Button_haptic_feedback');
    HapticFeedback.lightImpact();
    logFirebaseEvent('Button_backend_call');
    _model.apiResultUpdateProfile = await UpdateUserInfoCall.call(
      accessToken: FFAppState().authToken,
      jsonJson: functions.getUserUpdateRequestJson(FFAppState().savedUserData),
    );

    _shouldSetState = true;
    if ((_model.apiResultUpdateProfile?.succeeded ?? true)) {
      logFirebaseEvent('Button_navigate_back');
      context.safePop();
      logFirebaseEvent('Button_show_snack_bar');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            FFLocalizations.of(context).getText(
              '6ajj0u1y' /* Your details updated successfully! */,
            ),
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).success,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).alternate,
        ),
      );
    } else {
      logFirebaseEvent('Button_show_snack_bar');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            getJsonField(
              (_model.apiResultUpdateProfile?.jsonBody ?? ''),
              r'''$.error''',
            ).toString(),
            style: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).error,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).alternate,
        ),
      );
      if (_shouldSetState) safeSetState(() {});
      return;
    }

    if (_shouldSetState) safeSetState(() {});
  }
}

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';
import 'language_switch_model.dart';
export 'language_switch_model.dart';

class LanguageSwitchWidget extends StatefulWidget {
  const LanguageSwitchWidget({super.key, required this.onLanguageChanged});

  final Function(String) onLanguageChanged;

  @override
  State<LanguageSwitchWidget> createState() => _LanguageSwitchWidgetState();
}

class _LanguageSwitchWidgetState extends State<LanguageSwitchWidget> {
  late LanguageSwitchModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LanguageSwitchModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              FFLocalizations.of(context).getText(
                'e0wa75tn' /* Language */,
              ),
              style: FlutterFlowTheme.of(context).titleLarge.override(
                    fontFamily: 'SFHebrew',
                    letterSpacing: 0.0,
                  ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    'fh8lyi6z' /* English */,
                  ),
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'SFHebrew',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        letterSpacing: 0.0,
                      ),
                ),
                Switch(
                  value: FFLocalizations.of(context).languageCode == 'he',
                  onChanged: (newValue) async {
                    String languageCode = newValue ? 'he' : 'en';

                    logFirebaseEvent('Switch_set_app_language');
                    setAppLanguage(context, languageCode);

                    logFirebaseEvent('Switch_backend_call');
                    await currentUserReference!.update(createUsersRecordData(
                      languageCode: languageCode,
                    ));

                    FFAppState().updateSavedUserDataStruct(
                        (e) => e..languageCode = languageCode);
                    widget.onLanguageChanged(languageCode);
                    safeSetState(() => _model.switchValue = newValue);
                  },
                  activeColor: FlutterFlowTheme.of(context).primary,
                  activeTrackColor: FlutterFlowTheme.of(context).accent4,
                  inactiveTrackColor:
                      FlutterFlowTheme.of(context).secondaryText,
                  inactiveThumbColor: FlutterFlowTheme.of(context).alternate,
                ),
                Text(
                  FFLocalizations.of(context).getText(
                    '6p8s1fbg' /* עברית */,
                  ),
                  style: FlutterFlowTheme.of(context).bodyMedium.override(
                        fontFamily: 'SFHebrew',
                        color: FlutterFlowTheme.of(context).secondaryText,
                        letterSpacing: 0.0,
                      ),
                ),
              ].divide(SizedBox(width: 4.0)),
            ),
          ],
        ),
      ),
    );
  }
}

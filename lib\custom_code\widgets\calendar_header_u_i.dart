import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

import 'dart:ui';

class CalendarHeaderUI extends StatefulWidget {
  const CalendarHeaderUI({
    super.key,
    this.width,
    this.height,
    this.onDateChange,
    required this.selectedDate,
  });

  final double? width;
  final double? height;
  final Future Function(DateTime newSelectedDate)? onDateChange;
  final DateTime selectedDate;

  @override
  State<CalendarHeaderUI> createState() => _CalendarHeaderUIState();
}

class _CalendarHeaderUIState extends State<CalendarHeaderUI> {
  DateTime currentDate = DateTime.now();
  DateTime? selectedDate;

  // Get week dates based on the current date
  List<DateTime> getWeekDates(DateTime date) {
    int currentDayOfWeek = date.weekday;
    DateTime firstDayOfWeek =
        date.subtract(Duration(days: currentDayOfWeek - 1));
    return List.generate(
        7, (index) => firstDayOfWeek.add(Duration(days: index)));
  }

  // Open date picker for past dates
  void pickPastDate() async {
    DateTime firstDate = DateTime(2000);
    DateTime lastDate = DateTime.now();
    DateTime initialDate = selectedDate ?? currentDate;
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    if (initialDate.isAfter(lastDate)) {
      initialDate = lastDate;
    }

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      locale: isEnglish ? const Locale('en', 'US') : const Locale('he', 'IL'),
    );

    if (pickedDate != null) {
      setState(() {
        selectedDate = pickedDate;
        currentDate = pickedDate;
      });
      widget.onDateChange?.call(selectedDate!);
    }
  }

  // Reset to today's date
  void rollbackToToday() {
    setState(() {
      currentDate = DateTime.now();
      selectedDate = currentDate;
    });
    widget.onDateChange?.call(selectedDate!);
  }

  // Check if the current day is visible in the week's dates
  bool isCurrentDayVisible(List<DateTime> weekDates) {
    DateTime today = DateTime.now();
    return weekDates.any((date) =>
        date.day == today.day &&
        date.month == today.month &&
        date.year == today.year);
  }

  @override
  Widget build(BuildContext context) {
    List<DateTime> weekDates = getWeekDates(currentDate);
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    return Container(
      height: 100,
      child: Row(
        children: [
          // Action Buttons
          if (!isCurrentDayVisible(weekDates))
            IconButton(
              onPressed: rollbackToToday,
              icon: const Icon(Icons.restore, size: 24, color: Colors.blue),
              tooltip: isEnglish ? 'Back to Today' : 'חזרה להיום',
              padding: EdgeInsets.zero,
              visualDensity: VisualDensity.compact,
              iconSize: 24,
            ),
          IconButton(
            onPressed: pickPastDate,
            icon: const Icon(
              Icons.calendar_today,
              size: 24,
            ),
            tooltip: isEnglish ? 'Select Past Date' : 'בחר תאריך בעבר',
            padding: EdgeInsets.zero,
            visualDensity: VisualDensity.compact,
            iconSize: 24,
          ),
          // Week Dates List
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: weekDates.length,
              itemBuilder: (context, index) {
                DateTime date = weekDates[index];
                bool isToday = date.day == DateTime.now().day &&
                    date.month == DateTime.now().month &&
                    date.year == DateTime.now().year;
                bool isSelected = selectedDate != null &&
                    date.day == selectedDate!.day &&
                    date.month == selectedDate!.month &&
                    date.year == selectedDate!.year;

                return Padding(
                  padding: const EdgeInsets.all(5),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedDate = date;
                      });
                      widget.onDateChange?.call(selectedDate!);
                    },
                    child: CustomPaint(
                      painter: DashedBorderPainter(
                        color: isToday
                            ? Colors.green
                            : isSelected
                                ? Colors.grey
                                : Colors.transparent,
                      ),
                      child: Container(
                        width: 50,
                        height: 80,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          // color: isToday
                          //     ? Colors.green.withOpacity(0.3)
                          //     : isSelected
                          //         ? Colors.grey.withOpacity(0.3)
                          //         : Colors.transparent,
                          borderRadius: BorderRadius.circular(10),
                          gradient: LinearGradient(colors: [
                            isToday
                                ? Colors.green.withOpacity(0.1)
                                : isSelected
                                ? Colors.grey.withOpacity(0.1)
                                : Colors.transparent,
                            isToday
                                ? Colors.green.withOpacity(0.3)
                                : isSelected
                                ? Colors.grey.withOpacity(0.3)
                                : Colors.transparent,
                          ]),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              DateFormat('EEE', isEnglish ? 'en_US' : 'he_IL').format(date),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: isToday
                                    ? Colors.green
                                    : isSelected
                                        ? Colors.grey
                                        : FlutterFlowTheme.of(context)
                                            .primaryText,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              date.day.toString(),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isToday
                                    ? Colors.green
                                    : isSelected
                                        ? Colors.grey
                                        : FlutterFlowTheme.of(context)
                                            .primaryText,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class DashedBorderPainter extends CustomPainter {
  final Color color;

  DashedBorderPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = 5.0;
    const dashSpace = 3.0;
    const borderRadius = 10.0;

    final rect = RRect.fromRectAndRadius(
      Offset.zero & size,
      const Radius.circular(borderRadius),
    );

    final path = Path()..addRRect(rect);

    // Draw dashed border
    for (PathMetric pathMetric in path.computeMetrics()) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        const segmentLength = dashWidth + dashSpace;
        final nextDistance = distance + segmentLength;
        if (nextDistance > pathMetric.length) break;

        canvas.drawPath(
          pathMetric.extractPath(distance, distance + dashWidth),
          paint,
        );
        distance += segmentLength;
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dart:ui';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'recent_meal_item_model.dart';
export 'recent_meal_item_model.dart';

class RecentMealItemWidget extends StatefulWidget {
  const RecentMealItemWidget({
    super.key,
    required this.mealData,
    this.isLoading = false,
    this.showImage = true, // New parameter
  });

  final MealDetailStruct? mealData;
  final bool isLoading;
  final bool showImage; // Added to control image visibility

  @override
  State<RecentMealItemWidget> createState() => _RecentMealItemWidgetState();
}

class _RecentMealItemWidgetState extends State<RecentMealItemWidget> {
  late RecentMealItemModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RecentMealItemModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        boxShadow: [
          BoxShadow(
            blurRadius: 4.0,
            color: Color(0x33000000),
            offset: Offset(
              0.0,
              2.0,
            ),
            spreadRadius: 0.0,
          )
        ],
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: buildRow(isEnglish, context),
    );
  }

  Widget buildRow(bool isEnglish, BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Builder(
          builder: (context) {
            // Only show the image if showImage is true and the meal has an image
            if (widget.showImage &&
                valueOrDefault<bool>(
                  widget.mealData?.image != null &&
                      widget.mealData?.image != '',
                  false,
                )) {
              return Hero(
                tag: ValueKey(widget.mealData!.id),
                key: ValueKey(widget.mealData!.id),
                transitionOnUserGestures: true,
                child: Container(
                  constraints: BoxConstraints(maxHeight: 120, maxWidth: 120),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child: ClipRRect(
                      key: ValueKey(widget.mealData!.image),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(isEnglish ? 10.0 : 0.0),
                        bottomRight: Radius.circular(isEnglish ? 0.0 : 10.0),
                        topLeft: Radius.circular(isEnglish ? 10.0 : 0.0),
                        topRight: Radius.circular(isEnglish ? 0.0 : 10.0),
                      ),
                      child: CachedNetworkImage(
                        key: ValueKey(widget.mealData!.image),
                        fadeInDuration: Duration(milliseconds: 200),
                        fadeOutDuration: Duration(milliseconds: 200),
                        imageUrl: valueOrDefault<String>(
                          '${widget.mealData?.image}',
                          'https://placehold.co/600x400/EEE/31343C?font=open-sans&text=Open%20Sans',
                        ),
                        width: 120,
                        height: 120,
                        cacheKey: widget.mealData?.image ?? '',
                        fit: BoxFit.cover,
                        errorWidget: (_, __, ___) => errorWidget(),
                        memCacheWidth: 1952,
                        memCacheHeight: 1952,
                      ),
                    ),
                  ),
                ),
              );
            } else {
              return Container(
                width: 100.0,
                height: 110.0,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10.0),
                    bottomRight: Radius.circular(0.0),
                    topLeft: Radius.circular(10.0),
                    topRight: Radius.circular(0.0),
                  ),
                ),
                child: Align(
                  alignment: AlignmentDirectional(0.0, 0.0),
                  child: Icon(
                    Icons.fastfood_rounded,
                    color: FlutterFlowTheme.of(context).secondaryText,
                    size: 30.0,
                  ),
                ),
              );
            }
          },
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 10.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        valueOrDefault<String>(
                          widget.mealData?.name,
                          '-',
                        ),
                        maxLines: 2,
                        style:
                            FlutterFlowTheme.of(context).titleMedium.override(
                                  fontFamily: 'SFHebrew',
                                  fontSize: 16.0,
                                  letterSpacing: 0.0,
                                ),
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      functions.get12HrTimeFromStringDate(
                          widget.mealData!.scannedAt),
                      style: FlutterFlowTheme.of(context).bodySmall.override(
                            fontFamily: 'SFHebrew',
                            color: FlutterFlowTheme.of(context).secondaryText,
                            letterSpacing: 0.0,
                          ),
                    ),
                  ],
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Icon(
                      Icons.local_fire_department_rounded,
                      size: 14.0,
                    ),
                    Text(
                      (String var1) {
                        return isEnglish
                            ? '${var1.toStringAsFixed()} calories'
                            : "${var1.toStringAsFixed()} קלוריות";
                      }(widget.mealData!.totalCalories),
                      style:
                          FlutterFlowTheme.of(context).headlineSmall.override(
                                fontFamily: 'SFHebrew',
                                fontSize: 13.0,
                                letterSpacing: 0.0,
                              ),
                    ),
                  ].divide(SizedBox(width: 4.0)),
                ),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/Frame.svg',
                          width: 15.0,
                          height: 15.0,
                          fit: BoxFit.cover,
                        ),
                        Text(
                          (widget.mealData!.totalProteins.toStringAsFixed()),
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                        Text(
                          isEnglish ? 'proteins' : "חלבונים ",
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                      ].divide(SizedBox(height: 2.0)),
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/wheat-barley_svgrepo.com.svg',
                          width: 15.0,
                          height: 15.0,
                          fit: BoxFit.cover,
                        ),
                        Text(
                          widget.mealData!.totalCarbs.toStringAsFixed(),
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                        Text(
                          isEnglish ? 'carbs' : "פחמימות ",
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                      ].divide(SizedBox(height: 2.0)),
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/images/drop-invert_svgrepo.com.svg',
                          width: 15.0,
                          height: 15.0,
                          fit: BoxFit.cover,
                        ),
                        Text(
                          (widget.mealData!.totalFats.toStringAsFixed()),
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                        Text(
                          isEnglish ? 'fats' : "שומנים ",
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).bodySmall.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 10.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                      ].divide(SizedBox(height: 2.0)),
                    ),
                  ],
                ),
              ].divide(SizedBox(height: 4.0)),
            ),
          ),
        ),
      ].divide(SizedBox(width: 10.0)).addToEnd(SizedBox(width: 10.0)),
    );
  }

  Image errorWidget() {
    return Image.asset(
      'assets/images/error_image.png',
      width: 120,
      height: 120,
      fit: BoxFit.cover,
    );
  }
}

import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

import 'package:package_info_plus/package_info_plus.dart';

Future getDeviceSize(BuildContext context) async {
  // Add your function code here!
  // Importing the necessary packages

  String appVersionString = FFAppState().appVersion;
  if (appVersionString.trim().isEmpty) {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    String versionName =
        "v ${packageInfo.version} (${packageInfo.buildNumber})";
    FFAppState().appVersion = versionName;
  }

// Defining the function to get the device size

  double _deviceWidth = FFAppState().deviceWidth;
  double _deviceHeight = FFAppState().deviceHeight;
  double _topPadding = FFAppState().topPadding;
  double _bottomPadding = FFAppState().bottomPadding;
  if (_deviceWidth == 0.0 ||
      _deviceHeight == 0.0 ||
      _topPadding == 0.0 ||
      _bottomPadding == 0.0) {
    // Creating a MediaQueryData object to get the device size
    MediaQueryData mediaQueryData = await Future.delayed(
        Duration(milliseconds: 100),
        () => MediaQuery.of(context));

    // Getting the device width and height
    double deviceWidth = mediaQueryData.size.width;
    double deviceHeight = mediaQueryData.size.height;

    double bottomPadding = mediaQueryData.padding.bottom;
    double topPadding = mediaQueryData.padding.top;

    FFAppState().deviceWidth = deviceWidth;
    FFAppState().deviceHeight = deviceHeight;

    FFAppState().bottomPadding = bottomPadding;
    FFAppState().topPadding = topPadding;
  }

  // Returning true to indicate that the function has completed successfully
  return true;
}

import '/backend/api_requests/api_calls.dart';
import '/componentes/p_detail_item/p_detail_item_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/instant_timer.dart';
import 'personal_detail_view_widget.dart' show PersonalDetailViewWidget;
import 'package:flutter/material.dart';

class PersonalDetailViewModel
    extends FlutterFlowModel<PersonalDetailViewWidget> {
  ///  Local state fields for this page.

  int mValue = 0;

  ///  State fields for stateful widgets in this page.

  InstantTimer? instantTimer;
  // Model for p_detail_item component.
  late PDetailItemModel pDetailItemModel1;
  // Model for p_detail_item component.
  late PDetailItemModel pDetailItemModel2;
  // Model for p_detail_item component.
  late PDetailItemModel pDetailItemModel3;
  // Model for p_detail_item component.
  late PDetailItemModel pDetailItemModel4;
  // Stores action output result for [Backend Call - API (Update User Info)] action in Button widget.
  ApiCallResponse? apiResultUpdateProfile;

  @override
  void initState(BuildContext context) {
    pDetailItemModel1 = createModel(context, () => PDetailItemModel());
    pDetailItemModel2 = createModel(context, () => PDetailItemModel());
    pDetailItemModel3 = createModel(context, () => PDetailItemModel());
    pDetailItemModel4 = createModel(context, () => PDetailItemModel());
  }

  @override
  void dispose() {
    instantTimer?.cancel();
    pDetailItemModel1.dispose();
    pDetailItemModel2.dispose();
    pDetailItemModel3.dispose();
    pDetailItemModel4.dispose();
  }
}

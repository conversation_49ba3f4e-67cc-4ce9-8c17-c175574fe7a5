// Automatic FlutterFlow imports
import '/flutter_flow/flutter_flow_util.dart';
import 'package:flutter/material.dart';

// Begin custom widget code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

class CustomDatePicker extends StatefulWidget {
  const CustomDatePicker({
    super.key,
    this.width,
    this.height,
    this.initialDate,
    this.onDateChanged,
    this.firstDate,
    this.lastDate,
  });

  final double? width;
  final double? height;
  final DateTime? initialDate;
  final Future Function(DateTime? selectedDate)? onDateChanged;
  final DateTime? firstDate;
  final DateTime? lastDate;

  @override
  State<CustomDatePicker> createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  late FixedExtentScrollController _yearController;

  List<String> _months = [];
  final List<String> hebrewMonths = [
    'ינואר', // January
    'פברואר', // February
    'מרץ', // March
    'אפריל', // April
    'מאי', // May
    'יוני', // June
    'יולי', // July
    'אוגוסט', // August
    'ספטמבר', // September
    'אוקטובר', // October
    'נובמבר', // November
    'דצמבר' // December
  ];
  final List<String> englishMonths = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  DateTime _minimumDate = DateTime(1900, 1, 1);
  DateTime _maximumDate = DateTime.now();
  int? _selectedMonth;
  int? _selectedDay;
  int? _selectedYear;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  Future<void> _initializeControllers() async {
    if (widget.initialDate != null &&
        widget.initialDate!.isBefore(_maximumDate) &&
        widget.initialDate!.isAfter(_minimumDate)) {
      _selectedMonth = widget.initialDate!.month;
      _selectedDay = widget.initialDate!.day;
      _selectedYear = widget.initialDate!.year;
    } else {
      // Set default date to 1990 if no initial date provided
      _selectedMonth = 1;
      _selectedDay = 1;
      _selectedYear = 1990;
    }

    _monthController = FixedExtentScrollController(
        initialItem: _selectedMonth != null ? _selectedMonth! - 1 : 0);
    _dayController = FixedExtentScrollController(
        initialItem: _selectedDay != null ? _selectedDay! - 1 : 0);

    // Calculate the initial item for the year controller in reverse order
    final yearRange = _maximumDate.year - _minimumDate.year + 1;
    final defaultYear = 1990;
    // Adjust the index calculation to properly position 1990
    final defaultYearIndex = _maximumDate.year - defaultYear;
    _yearController = FixedExtentScrollController(
        initialItem: _selectedYear != null
            ? _maximumDate.year - _selectedYear!
            : defaultYearIndex);

    final day = _selectedDay;
    final month = _selectedMonth;
    final year = _selectedYear;

    if (day != null && month != null && year != null) {
      widget.onDateChanged?.call(DateTime(year, month, day));
    }
  }

  @override
  void dispose() {
    _monthController.dispose();
    _dayController.dispose();
    _yearController.dispose();
    super.dispose();
  }

  bool _isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }

  int _getDaysInMonth(int year, int month) {
    if (month == 2) {
      // February
      return _isLeapYear(year) ? 29 : 28;
    } else {
      return DateTime(year, month + 1, 0).day;
    }
  }

  bool _isValidDate(int year, int month, int day) {
    final date = DateTime(year, month, day);
    return date.isAfter(_minimumDate) && date.isBefore(_maximumDate);
  }

  void _onDateChanged() {
    final month = _monthController.selectedItem + 1;
    final day = _dayController.selectedItem + 1;
    final year = _maximumDate.year - _yearController.selectedItem;

    if (_isValidDate(year, month, day)) {
      setState(() {
        _selectedMonth = month;
        _selectedDay = day;
        _selectedYear = year;
      });
      widget.onDateChanged?.call(DateTime(year, month, day));
    } /*else {
      widget.onDateChanged?.call(null);
    }*/
  }

  void _updateDayWheel() {
    final year = _maximumDate.year - _yearController.selectedItem;
    final month = _monthController.selectedItem + 1;
    final daysInMonth = _getDaysInMonth(year, month);

    // Update the day controller to reflect the correct number of days
    if (_dayController.selectedItem >= daysInMonth) {
      _dayController =
          FixedExtentScrollController(initialItem: daysInMonth - 1);
      setState(() {
        _selectedDay = daysInMonth;
      });
    }
  }

  Widget _buildWheel({
    required FixedExtentScrollController controller,
    required List<Widget> children,
    required double width,
  }) {
    return SizedBox(
      height: 150,
      width: width,
      child: ListWheelScrollView.useDelegate(
        controller: controller,
        itemExtent: 40,
        perspective: 0.002,
        diameterRatio: 1.5,
        physics: const FixedExtentScrollPhysics(),
        onSelectedItemChanged: (index) {
          if (controller == _monthController || controller == _yearController) {
            _updateDayWheel();
          }
          _onDateChanged();
        },
        childDelegate: ListWheelChildListDelegate(children: children),
        overAndUnderCenterOpacity: 0.5,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    _months = isEnglish ? englishMonths : hebrewMonths;
    return Container(
      height: 200,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildWheel(
            controller: _monthController,
            width: 100,
            children: _months.map((month) {
              final isSelected = _months.indexOf(month) + 1 == _selectedMonth;
              return Container(
                decoration: BoxDecoration(
                  color: isSelected ? Colors.black : null,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          )
                        ]
                      : null,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    month,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w400,
                      color: !isSelected ? Colors.black : Colors.white,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          _buildWheel(
            controller: _dayController,
            width: 60,
            children: List.generate(
                _getDaysInMonth(
                    _selectedYear ?? _minimumDate.year, _selectedMonth ?? 1),
                (index) {
              final isSelected = _selectedDay == index + 1;
              return Container(
                decoration: BoxDecoration(
                  color: isSelected ? Colors.black : null,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          )
                        ]
                      : null,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w400,
                      color: !isSelected ? Colors.black : Colors.white,
                    ),
                  ),
                ),
              );
            }),
          ),
          _buildWheel(
            controller: _yearController,
            width: 80,
            children: List.generate(
              _maximumDate.year - _minimumDate.year + 1,
              (index) {
                final year = _maximumDate.year - index;
                final isSelected = _selectedYear == year;
                return Container(
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.black : null,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            )
                          ]
                        : null,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      year.toString(),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.w400,
                        color: !isSelected ? Colors.black : Colors.white,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

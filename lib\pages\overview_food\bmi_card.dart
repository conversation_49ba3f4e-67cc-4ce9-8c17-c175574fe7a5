import 'package:cal_counti_a_i/pages/overview_food/bmi_details.dart';
import 'package:flutter/material.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';

class BMICard extends StatelessWidget {
  final double bmi;
  final String status;
  final Color statusColor;
  final double minBMI;
  final double maxBMI;

  const BMICard({
    super.key,
    required this.bmi,
    required this.status,
    required this.statusColor,
    this.minBMI = 15,
    this.maxBMI = 40,
  });

  @override
  Widget build(BuildContext context) {
    final bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    double normalizedBMI = ((bmi - minBMI) / (maxBMI - minBMI)).clamp(0.0, 1.0);
    if (bmi < minBMI || bmi > maxBMI) {
      normalizedBMI = 0.0;
    } else {
      normalizedBMI = ((bmi - minBMI) / (maxBMI - minBMI)).clamp(0.0, 1.0);
    }
    return GestureDetector(
      onTap: () {
        redirectToBMIInfoScreen(context);
      },
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: FlutterFlowTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 12,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  isEnglish ? 'Your BMI' : 'ה-BMI שלך',
                  style: FlutterFlowTheme.of(context)
                      .headlineSmall
                      .override(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    redirectToBMIInfoScreen(context);
                  },
                  child: Icon(Icons.help_outline,
                      color: Colors.grey[700], size: 18),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  bmi.toStringAsFixed(2),
                  style: FlutterFlowTheme.of(context)
                      .displayMedium
                      .override(fontWeight: FontWeight.bold, fontSize: 30),
                ),
                const SizedBox(width: 10),
                Text(
                  isEnglish ? 'Your weight is' : 'המשקל שלך הוא',
                  style: FlutterFlowTheme.of(context)
                      .bodyMedium
                      .override(color: Colors.grey[500]),
                ),
                const SizedBox(width: 8),
                Container(
                  width: 110,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      isEnglish ? status : _hebrewStatus(status),
                      style: FlutterFlowTheme.of(context).bodyMedium.override(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Gradient bar with indicator
            Stack(
              alignment: Alignment.centerLeft,
              children: [
                Container(
                  height: 14,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: LinearGradient(
                      colors: isEnglish
                          ? [
                              Color(0xFF3A8DFF), // Underweight - blue
                              Color(0xFF2ECC71), // Healthy - green
                              Color(0xFFFFA726), // Overweight - orange
                              Color(0xFFE53935), // Obese - red
                            ]
                          : [
                              Color(
                                  0xFFE53935), // Obese - red (right side for Hebrew)
                              Color(0xFFFFA726), // Overweight - orange
                              Color(0xFF2ECC71), // Healthy - green
                              Color(
                                  0xFF3A8DFF), // Underweight - blue (left side for Hebrew)
                            ],
                      stops: [0.0, 0.33, 0.66, 1.0],
                    ),
                  ),
                ),
                Positioned(
                  left: bmiToBarPosition(bmi, isEnglish) *
                      (MediaQuery.of(context).size.width - 80),
                  child: Container(
                    width: 4,
                    height: 28,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 18),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: isEnglish
                  ? [
                      _BMIIndicatorDot(
                          color: Color(0xFF3A8DFF), label: 'Underweight'),
                      _BMIIndicatorDot(
                          color: Color(0xFF2ECC71), label: 'Healthy'),
                      _BMIIndicatorDot(
                          color: Color(0xFFFFA726), label: 'Overweight'),
                      _BMIIndicatorDot(
                          color: Color(0xFFE53935), label: 'Obese'),
                    ]
                  : [
                      _BMIIndicatorDot(
                          color: Color(0xFF3A8DFF), label: 'תת משקל'),
                      _BMIIndicatorDot(color: Color(0xFF2ECC71), label: 'בריא'),
                      _BMIIndicatorDot(
                          color: Color(0xFFFFA726), label: 'עודף משקל'),
                      _BMIIndicatorDot(
                          color: Color(0xFFE53935), label: 'השמנת יתר'),
                    ],
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> redirectToBMIInfoScreen(BuildContext context) {
    return Navigator.push(
      context,
      MaterialPageRoute(
          builder: (context) => BmiDetails(
                bmi: bmi,
                status: status,
                statusColor: statusColor,
              )),
    );
  }

  double bmiToBarPosition(double bmi, bool isEnglish) {
    if (isEnglish) {
      // English layout (left to right)
      if (bmi <= minBMI) return 0.0;
      if (bmi <= 20) return (bmi - minBMI) / (20 - minBMI) * 0.33;
      if (bmi <= 25) return 0.33 + (bmi - 20) / (25 - 20) * 0.33;
      if (bmi <= 30) return 0.66 + (bmi - 25) / (30 - 25) * 0.34;
      return 1.0;
    } else {
      // Hebrew layout (right to left) - reverse the positioning
      if (bmi <= minBMI) return 1.0;
      if (bmi <= 20) return 1.0 - (bmi - minBMI) / (20 - minBMI) * 0.33;
      if (bmi <= 25) return 0.67 - (bmi - 20) / (25 - 20) * 0.33;
      if (bmi <= 30) return 0.34 - (bmi - 25) / (30 - 25) * 0.34;
      return 0.0;
    }
  }
}

class _BMIIndicatorDot extends StatelessWidget {
  final Color color;
  final String label;

  const _BMIIndicatorDot({required this.color, required this.label});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 5),
        Text(
          label,
          style: FlutterFlowTheme.of(context).bodySmall,
        ),
      ],
    );
  }
}

String _hebrewStatus(String status) {
  switch (status.toLowerCase()) {
    case 'healthy':
      return 'בריא';
    case 'underweight':
      return 'תת משקל';
    case 'overweight':
      return 'עודף משקל';
    case 'obese':
      return 'השמנת יתר';
    default:
      return status;
  }
}

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/pages/overview_food/edit_weight.dart';
import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/schema/structs/index.dart';
import '/componentes/circular_progress/circular_progress_widget.dart';
import '/componentes/segmented/segmented_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/revenue_cat_util.dart' as revenue_cat;
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/permissions_util.dart';
import 'food_log_weight_card.dart';
import 'bmi_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'overview_food_model.dart';
export 'overview_food_model.dart';

class OverviewFoodWidget extends StatefulWidget {
  const OverviewFoodWidget({super.key});

  @override
  State<OverviewFoodWidget> createState() => _OverviewFoodWidgetState();
}

class _OverviewFoodWidgetState extends State<OverviewFoodWidget>
    with TickerProviderStateMixin {
  late OverviewFoodModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  bool _isLoading = false;

  // Helper method to format enum names (e.g., thisWeek -> This Week)
  String formatEnumName1(String enumName) {
    // Split by camelCase and add spaces
    String spaced = enumName.replaceAllMapped(
        RegExp(r'(?<=[a-z])([A-Z])'), (match) => ' ${match.group(1)}');
    // Capitalize first letter of each word
    return spaced.split(' ').map((word) {
      if (word.isNotEmpty) {
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }
      return word;
    }).join(' ');
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => OverviewFoodModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      logFirebaseEvent('overview_food_backend_call');
      _model.apiResultData = await MealOverviewCall.call(
        accessToken: FFAppState().authToken,
      );

      if ((_model.apiResultData?.succeeded ?? true)) {
        logFirebaseEvent('overview_food_wait__delay');
        await Future.delayed(const Duration(milliseconds: 50));
        logFirebaseEvent('overview_food_update_page_state');
        _model.foodOverviewData = MealOverviewCall.data(
          (_model.apiResultData?.jsonBody ?? ''),
        );
        safeSetState(() {});
      } else {
        logFirebaseEvent('overview_food_wait__delay');
        await Future.delayed(const Duration(milliseconds: 50));
        logFirebaseEvent('overview_food_update_page_state');
        _model.foodOverviewData = FoodOverviewDetailStruct();
        safeSetState(() {});
      }
    });

    animationsMap.addAll({
      'circularProgressOnPageLoadAnimation1': AnimationInfo(
        loop: true,
        reverse: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
      'circularProgressOnPageLoadAnimation2': AnimationInfo(
        loop: true,
        reverse: true,
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          RotateEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 600.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
        ],
      ),
    });
  }

  @override
  void dispose() {
    _model.dispose();
    super.dispose();
  }

  // Function to calculate days logged, latest weight, and BMI
  Map<String, dynamic> _calculateLoggedDaysAndBMI(dynamic jsonBody) {
    final dailyLogs = jsonBody['data']?['dailyLogs'] as List<dynamic>? ?? [];
    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(Duration(days: 7));

    int daysLogged = 0;
    double? latestWeight;
    DateTime? latestWeightDate;

    for (var log in dailyLogs) {
      final logDate = DateTime.parse(log['log_date']).toLocal();
      final totalWeight = (log['total_weight'] as num?)?.toDouble() ?? 0.0;

      if (logDate.isAfter(sevenDaysAgo) &&
          logDate.isBefore(now.add(Duration(days: 1)))) {
        if (totalWeight > 0) {
          daysLogged++;
          if (latestWeightDate == null || logDate.isAfter(latestWeightDate)) {
            latestWeight = totalWeight;
            latestWeightDate = logDate;
          }
        }
      }
    }

    double bmi = -1.0;
    String bmiStatus = 'Unknown (Insufficient data)';
    Color bmiStatusColor = Colors.grey;

    final heightInCm = FFAppState().savedUserData.heightValue;
    final weightInKgs = latestWeight ?? FFAppState().savedUserData.weightValue;

    if (heightInCm > 0 && weightInKgs > 0) {
      final heightInMeters = heightInCm / 100.0;
      bmi = weightInKgs / (heightInMeters * heightInMeters);

      if (bmi < 18.5) {
        bmiStatus = 'Underweight';
        bmiStatusColor = const Color(0xFF3A8DFF);
      } else if (bmi < 25) {
        bmiStatus = 'Healthy';
        bmiStatusColor = const Color(0xFF2ECC71);
      } else if (bmi < 30) {
        bmiStatus = 'Overweight';
        bmiStatusColor = const Color(0xFFFFA726);
      } else {
        bmiStatus = 'Obese';
        bmiStatusColor = const Color(0xFFE53935);
      }
    }

    return {
      'daysLogged': daysLogged,
      'weight': latestWeight != null
          ? '${latestWeight.toStringAsFixed(0)} ${isEnglish ? 'kg' : 'קילוגרם'}'
          : 'N/A',
      'bmi': bmi,
      'bmiStatus': bmiStatus,
      'bmiStatusColor': bmiStatusColor,
    };
  }

  bool isEnglish = false;
  @override
  Widget build(BuildContext context) {
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    context.watch<FFAppState>();
    // Calculate logged days, latest weight, and BMI
    final logData =
        _calculateLoggedDaysAndBMI(_model.apiResultData?.jsonBody ?? {});
    final daysLogged = logData['daysLogged'] as int;
    final weight = logData['weight'] as String;
    final bmi = logData['bmi'] as double;
    final bmiStatus = logData['bmiStatus'] as String;
    final bmiStatusColor = logData['bmiStatusColor'] as Color;

    return Stack(
      children: [
        MeasuredWidget(
          name: 'OverviewFood',
          builder: (context) => GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Scaffold(
              key: scaffoldKey,
              backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
              floatingActionButton: true
                  ? null
                  : FloatingActionButton(
                      onPressed: () async {
                        var _shouldSetState = false;
                        logFirebaseEvent(
                            'FloatingActionButton_request_permissions');
                        await requestPermission(cameraPermission);
                        if (!(await getPermissionStatus(cameraPermission))) {
                          needCamera(context, _shouldSetState);
                          return;
                        }
                        logFirebaseEvent('FloatingActionButton_navigate_to');
                        bool _isSubscribed = revenue_cat.isSubscribed();
                        if (!_isSubscribed) {
                          await context.pushNamed(
                            'subscription',
                            extra: <String, dynamic>{
                              kTransitionInfoKey: const TransitionInfo(
                                hasTransition: true,
                                transitionType: PageTransitionType.bottomToTop,
                              ),
                            },
                          );
                          bool __isSubscribed = revenue_cat.isSubscribed();
                          if (!__isSubscribed) {
                            return;
                          }
                        } else {
                          context.pushNamed(
                            'camera_view',
                            extra: <String, dynamic>{
                              kTransitionInfoKey: TransitionInfo(
                                hasTransition: true,
                                transitionType: PageTransitionType.bottomToTop,
                              ),
                            },
                          );
                        }
                      },
                      backgroundColor:
                          FlutterFlowTheme.of(context).primaryBackground,
                      elevation: 8.0,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: SvgPicture.asset(
                          'assets/images/add_a_photo.svg',
                          width: 25.0,
                          height: 25.0,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
              appBar: responsiveVisibility(
                context: context,
                tablet: false,
                tabletLandscape: false,
                desktop: false,
              )
                  ? AppBar(
                      backgroundColor:
                          FlutterFlowTheme.of(context).primaryBackground,
                      automaticallyImplyLeading: false,
                      actions: [],
                      flexibleSpace: FlexibleSpaceBar(
                        titlePadding: const EdgeInsets.all(0),
                        title: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0,
                              valueOrDefault<double>(
                                FFAppState().topPadding + 16,
                                0.0,
                              ),
                              0.0,
                              0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 8.0, horizontal: 15),
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      'xh5t4r1k' /* Data Analysis */,
                                    ),
                                    style: FlutterFlowTheme.of(context)
                                        .headlineMedium
                                        .override(
                                          fontSize: 24,
                                          fontFamily: 'SFHebrew',
                                          color: FlutterFlowTheme.of(context)
                                              .primary,
                                          letterSpacing: 0.0,
                                        ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        centerTitle: false,
                        expandedTitleScale: 1.0,
                      ),
                      toolbarHeight: 80.0,
                      elevation: 0.0,
                    )
                  : null,
              body: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                      child: FoodLogWeightCard(
                        onLogWeight: () async {
                          double initialWeight = 70.0;
                          try {
                            // Try to extract the numeric value from the weight string
                            initialWeight = double.parse(weight.split(' ')[0]);
                          } catch (_) {}
                          final result =
                              await Navigator.push(context, MaterialPageRoute(
                            builder: (context) {
                              return EditWeight(initialWeight: initialWeight);
                            },
                          ));
                          if (result != null) {
                            setState(() {
                              _isLoading = true;
                            });
                            _model.apiResultData = await MealOverviewCall.call(
                              accessToken: FFAppState().authToken,
                            );
                            if ((_model.apiResultData?.succeeded ?? true)) {
                              _model.foodOverviewData = MealOverviewCall.data(
                                (_model.apiResultData?.jsonBody ?? ''),
                              );
                            }
                            setState(() {
                              _isLoading = false;
                            });
                          }
                        },
                        weight: weight,
                        daysLogged: daysLogged,
                        totalDays: 7,
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(16.0, 5.0, 16.0, 5.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: 370,
                        decoration: BoxDecoration(
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(12.0),
                          border: Border.all(
                            color: Color(0xFFDDDDDD),
                          ),
                        ),
                        child: Builder(
                          builder: (context) {
                            if (_model.foodOverviewData != null) {
                              return Column(
                                children: [
                                  Row(
                                    children: [
                                      Align(
                                          alignment: Alignment.centerLeft,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 15, vertical: 15),
                                            child: Text(
                                              // isEnglish ? "Goal Progress" : "התקדמות",
                                              isEnglish
                                                  ? "Weight Progress"
                                                  : "התקדמות משקל",
                                              style:
                                                  FlutterFlowTheme.of(context)
                                                      .displaySmall
                                                      .copyWith(fontSize: 22),
                                            ),
                                          )),
                                      Spacer(),
                                      // Padding(
                                      //   padding: EdgeInsetsDirectional.fromSTEB(
                                      //       0.0, 0.0, 16.0, 8.0),
                                      //   child: Row(
                                      //     mainAxisSize: MainAxisSize.max,
                                      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      //     children: [
                                      //       Text(
                                      //         FFLocalizations.of(context).getText(
                                      //           '6zb1nboh' /* Calories */,
                                      //         ),
                                      //         style: FlutterFlowTheme.of(context)
                                      //             .bodyMedium
                                      //             .override(
                                      //           fontFamily: 'SFHebrew',
                                      //           letterSpacing: 0.0,
                                      //           fontWeight: FontWeight.w500,
                                      //         ),
                                      //       ),
                                      //     ],
                                      //   ),
                                      // ),
                                    ],
                                  ),
                                  Container(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        10.0, 0.0, 10.0, 0.0),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        // color: FlutterFlowTheme.of(context).grey,
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      child: wrapWithModel(
                                        model: _model.segmentedModel1,
                                        updateCallback: () =>
                                            safeSetState(() {}),
                                        child: SegmentedWidget(
                                          initialValue:
                                              _model.selectedGoalDuration!.name,
                                          segmentItems: GoalDuration.values
                                              .map((e) => e.name)
                                              .toList(),
                                          onSelect: (title) async {
                                            logFirebaseEvent(
                                                'segmented_haptic_feedback');
                                            HapticFeedback.lightImpact();
                                            _model.selectedGoalDuration =
                                                GoalDuration.values.firstWhere(
                                              (e) => e.name == title,
                                            );
                                            logFirebaseEvent(
                                                'segmented_update_page_state');
                                            safeSetState(() {});
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                  custom_widgets.LineChartUI(
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height: 220.0,
                                    isGoals: true,
                                    foodOverviewDetail:
                                        _model.foodOverviewData!,
                                    goalDuration: _model.selectedGoalDuration,
                                  ),
                                ],
                              );
                            } else {
                              return wrapWithModel(
                                model: _model.circularProgressModel1,
                                updateCallback: () => safeSetState(() {}),
                                child: CircularProgressWidget(),
                              );
                            }
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 8.0, 0.0),
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(8.0, 8.0, 0.0, 8.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              FFLocalizations.of(context).getText(
                                'jx26qss5' /* Nutrition */,
                              ),
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'SFHebrew',
                                    letterSpacing: 0.0,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        decoration: BoxDecoration(
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                        ),
                        child: Visibility(
                          visible: false,
                          child: Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 11.0, 0.0, 12.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        '2z890ysg' /* 0 */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .headlineSmall
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'xhy6x1if' /* Total calories */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodySmall
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                  ],
                                ),
                                Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'prxpd7jx' /* 0.0 */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .headlineSmall
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        '8rvgdrpv' /* Daily avg. */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodySmall
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          16.0, 10.0, 16.0, 10.0),
                      child: Container(
                        width: MediaQuery.sizeOf(context).width * 1.0,
                        height: 290,
                        decoration: BoxDecoration(
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                          borderRadius: BorderRadius.circular(12.0),
                          border: Border.all(
                            color: Color(0xFFDDDDDD),
                          ),
                        ),
                        child: Builder(
                          builder: (context) {
                            if (_model.foodOverviewData != null) {
                              return Column(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      // color: FlutterFlowTheme.of(context).grey,
                                      borderRadius: BorderRadius.circular(10.0),
                                    ),
                                    child: wrapWithModel(
                                      model: _model.segmentedModel2,
                                      updateCallback: () => safeSetState(() {}),
                                      child: SegmentedWidget(
                                        initialValue: _model
                                            .selectedNutritionDuration!.name,
                                        segmentItems: NutritionDuration.values
                                            .map((e) => e.name)
                                            .toList(),
                                        onSelect: (title) async {
                                          logFirebaseEvent(
                                              'segmented_haptic_feedback');
                                          HapticFeedback.lightImpact();
                                          _model.selectedNutritionDuration =
                                              NutritionDuration.values
                                                  .firstWhere(
                                            (e) => e.name == title,
                                          );
                                          logFirebaseEvent(
                                              'segmented_update_page_state');
                                          safeSetState(() {});
                                        },
                                      ),
                                    ),
                                  ),
                                  custom_widgets.LineChartUI(
                                    width:
                                        MediaQuery.sizeOf(context).width * 1.0,
                                    height: 190.0,
                                    isGoals: false,
                                    foodOverviewDetail:
                                        _model.foodOverviewData!,
                                    nutritionDuration:
                                        _model.selectedNutritionDuration,
                                  ),
                                ],
                              );
                            } else {
                              return wrapWithModel(
                                model: _model.circularProgressModel2,
                                updateCallback: () => safeSetState(() {}),
                                child: CircularProgressWidget(),
                              );
                            }
                          },
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 15,
                    ),
                    // BMI Card
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
                      child: BMICard(
                        bmi: bmi,
                        status: bmiStatus,
                        statusColor: bmiStatusColor,
                      ),
                    ),
                  ]
                      .addToStart(SizedBox(height: 16.0))
                      .addToEnd(SizedBox(height: 50.0)),
                ),
              ),
            ),
          ),
        ),
        if (_isLoading)
          Container(
            color: Colors.black.withOpacity(0.3),
            child: const Center(child: CircularProgressWidget()),
          ),
      ],
    );
  }

  void needCamera(BuildContext context, bool _shouldSetState) {
    logFirebaseEvent('FloatingActionButton_show_snack_bar');
    ScaffoldMessenger.of(context).clearSnackBars();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Need camera access for scanning Food.',
          style: TextStyle(
            color: FlutterFlowTheme.of(context).error,
          ),
        ),
        duration: Duration(milliseconds: 4000),
        backgroundColor: FlutterFlowTheme.of(context).alternate,
      ),
    );
    if (_shouldSetState) safeSetState(() {});
  }
}

import '/backend/schema/structs/index.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'recent_meal_item_progress_model.dart';
export 'recent_meal_item_progress_model.dart';

class RecentMealItemProgressWidget extends StatefulWidget {
  /// Design a clean and modern card layout with rounded corners and a subtle
  /// shadow for depth. The card should include a rounded corner image on the
  /// left, covering approximately 40% of the card's width. On the right side,
  /// include a bold, medium-sized title displaying the dish name (e.g.,
  /// '<PERSON><PERSON><PERSON>') and a smaller, light text in the top-right corner indicating the
  /// time (e.g., '7:27 PM'). Below the title, display a bold, large text label
  /// for calorie information (e.g., '409 calories') with a flame icon next to
  /// it. Beneath the calorie info, add macronutrient details with small icons
  /// and text labels: protein (e.g., '18g' with a chicken leg icon),
  /// carbohydrates (e.g., '42g' with a wheat icon), and fats (e.g., '20g' with
  /// an avocado icon). These icons should align horizontally with even spacing.
  /// Use a light background color, such as white or light gray, with around
  /// 16px padding for inner spacing and balanced margins between elements.
  /// Ensure vertical alignment between the title, time, and calorie details,
  /// and make the card responsive to fit varying screen sizes while preserving
  /// proportions.
  const RecentMealItemProgressWidget({
    super.key,
    required this.mealData,
  });

  final MealDetailStruct? mealData;

  @override
  State<RecentMealItemProgressWidget> createState() =>
      _RecentMealItemProgressWidgetState();
}

class _RecentMealItemProgressWidgetState
    extends State<RecentMealItemProgressWidget> {
  late RecentMealItemProgressModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => RecentMealItemProgressModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 90.0,
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        boxShadow: [
          BoxShadow(
            blurRadius: 4.0,
            color: Color(0x33000000),
            offset: Offset(
              0.0,
              2.0,
            ),
            spreadRadius: 0.0,
          )
        ],
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Builder(
            builder: (context) {
              if (valueOrDefault<bool>(
                widget.mealData?.image != null &&
                    widget.mealData?.image != '',
                false,
              )) {
                return ClipRRect(
                  key: ValueKey(widget.mealData!.image),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(0.0),
                    bottomRight: Radius.circular(10.0),
                    topLeft: Radius.circular(0.0),
                    topRight: Radius.circular(10.0),
                  ),
                  child: Image.asset(
                    key: ValueKey(widget.mealData!.image),
                    'assets/images/2vqf7_',
                    width: 90.0,
                    height: 90.0,
                    fit: BoxFit.cover,
                    cacheWidth: 400,
                    cacheHeight: 400,
                  ),
                );
              } else {
                return Container(
                  width: 90.0,
                  height: 90.0,
                  decoration: BoxDecoration(
                    color: FlutterFlowTheme.of(context).secondaryBackground,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.0),
                      bottomRight: Radius.circular(0.0),
                      topLeft: Radius.circular(10.0),
                      topRight: Radius.circular(0.0),
                    ),
                  ),
                  child: Align(
                    alignment: AlignmentDirectional(0.0, 0.0),
                    child: Icon(
                      Icons.error_outline_outlined,
                      color: FlutterFlowTheme.of(context).error,
                      size: 30.0,
                    ),
                  ),
                );
              }
            },
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(10.0, 4.0, 10.0, 4.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          valueOrDefault<String>(
                            widget.mealData?.name,
                            '-',
                          ),
                          maxLines: 1,
                          style:
                              FlutterFlowTheme.of(context).titleMedium.override(
                                    fontFamily: 'SFHebrew',
                                    fontSize: 16.0,
                                    letterSpacing: 0.0,
                                  ),
                        ),
                      ),
                      Text(
                        functions.get12HrTimeFromStringDate(
                            widget.mealData!.scannedAt),
                        style: FlutterFlowTheme.of(context).bodySmall.override(
                              fontFamily: 'SFHebrew',
                              color: FlutterFlowTheme.of(context).secondaryText,
                              letterSpacing: 0.0,
                            ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Icon(
                        Icons.local_fire_department_rounded,
                        size: 14.0,
                      ),
                      Text(
                        (String var1) {
                          return "$var1 קלוריות";
                        }(widget.mealData!.totalCalories),
                        style:
                            FlutterFlowTheme.of(context).headlineSmall.override(
                                  fontFamily: 'SFHebrew',
                                  fontSize: 13.0,
                                  letterSpacing: 0.0,
                                ),
                      ),
                    ].divide(SizedBox(width: 4.0)),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/images/Frame.svg',
                            width: 15.0,
                            height: 15.0,
                            fit: BoxFit.cover,
                          ),
                          Text(
                            (String var1) {
                              return "$var1 חלבונים";
                            }(widget.mealData!.totalProteins),
                            maxLines: 1,
                            style:
                                FlutterFlowTheme.of(context).bodySmall.override(
                                      fontFamily: 'SFHebrew',
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                    ),
                          ),
                        ].divide(SizedBox(height: 2.0)),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/images/wheat-barley_svgrepo.com.svg',
                            width: 15.0,
                            height: 15.0,
                            fit: BoxFit.cover,
                          ),
                          Text(
                            (String var1) {
                              return "$var1 פחמימות";
                            }(widget.mealData!.totalCarbs),
                            maxLines: 1,
                            style:
                                FlutterFlowTheme.of(context).bodySmall.override(
                                      fontFamily: 'SFHebrew',
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                    ),
                          ),
                        ].divide(SizedBox(height: 2.0)),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                            'assets/images/drop-invert_svgrepo.com.svg',
                            width: 15.0,
                            height: 15.0,
                            fit: BoxFit.cover,
                          ),
                          Text(
                            (String var1) {
                              return "$var1 שומנים";
                            }(widget.mealData!.totalFats),
                            maxLines: 1,
                            style:
                                FlutterFlowTheme.of(context).bodySmall.override(
                                      fontFamily: 'SFHebrew',
                                      fontSize: 10.0,
                                      letterSpacing: 0.0,
                                    ),
                          ),
                        ].divide(SizedBox(height: 2.0)),
                      ),
                    ],
                  ),
                ].divide(SizedBox(height: 4.0)),
              ),
            ),
          ),
        ].divide(SizedBox(width: 10.0)).addToEnd(SizedBox(width: 10.0)),
      ),
    );
  }
}

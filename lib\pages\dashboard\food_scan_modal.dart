import 'package:cal_counti_a_i/app_state.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/internationalization.dart';
import 'package:flutter/material.dart';

class FoodScanModal extends StatelessWidget {
  final void Function()? onFoodDatabase;
  final void Function()? onScanFood;
  final void Function()? onScanBarcode;

  const FoodScanModal({
    Key? key,
    this.onFoodDatabase,
    this.onScanFood,
    this.onScanBarcode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Padding(
          //   padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       Expanded(
          //         child: _OptionCard(
          //           icon: Icons.qr_code_scanner,
          //           label: isEnglish ? 'Scan Barcode' : 'סריקת ברקוד',
          //           onTap: onScanBarcode,
          //           horizontal: true,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _OptionCard(
                icon: Icons.search,
                label: isEnglish
                    ? 'Food Database'
                    : 'מאגר נתוני מזון', // Food database
                onTap: onFoodDatabase,
              ),
              _OptionCard(
                icon: Icons.qr_code_scanner,
                label: isEnglish ? 'Scan Food' : 'סריקת מזון', // Scan Food
                onTap: onScanFood,
              ),
            ],
          ),
          SizedBox(height: FFAppState().bottomPadding)
        ],
      ),
    );
  }
}

class _OptionCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final void Function()? onTap;
  final bool horizontal;

  const _OptionCard({required this.icon, required this.label, this.onTap, this.horizontal = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: horizontal? null:130,
        height: horizontal ? null: 105,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 8,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: horizontal? Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(32),
                ),
                padding: const EdgeInsets.all(12),
                child: Icon(icon, size: 25, color: Colors.black87),
              ),
              const SizedBox(height: 12),
              Text(
                label,
                style: FlutterFlowTheme.of(context)
                    .titleSmall
                    .override(fontSize: 14),
              ),
            ],
          ) : Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(32),
                ),
                padding: const EdgeInsets.all(12),
                child: Icon(icon, size: 25, color: Colors.black87),
              ),
              const SizedBox(height: 12),
              Text(
                label,
                style: FlutterFlowTheme.of(context)
                    .titleSmall
                    .override(fontSize: 14),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

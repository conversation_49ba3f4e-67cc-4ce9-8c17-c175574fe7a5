import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';

import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'personal_detail_model.dart';
export 'personal_detail_model.dart';

class PersonalDetailWidget extends StatefulWidget {
  const PersonalDetailWidget({super.key});

  @override
  State<PersonalDetailWidget> createState() => _PersonalDetailWidgetState();
}

class _PersonalDetailWidgetState extends State<PersonalDetailWidget> {
  late PersonalDetailModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PersonalDetailModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return MeasuredWidget(
        name: 'PersonalDetail',
        builder: (context) => GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            key: scaffoldKey,
            backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
            appBar: responsiveVisibility(
              context: context,
              tablet: false,
              tabletLandscape: false,
              desktop: false,
            )
                ? AppBar(
                    backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                    automaticallyImplyLeading: false,
                    actions: [],
                    flexibleSpace: FlexibleSpaceBar(
                      titlePadding: const EdgeInsets.all(0),
                      title: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            0.0,
                            valueOrDefault<double>(
                              FFAppState().topPadding + 16,
                              0.0,
                            ),
                            0.0,
                            0.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            FlutterFlowIconButton(
                              borderRadius: 8.0,
                              buttonSize: 40.0,
                              icon: Icon(
                                Icons.chevron_left_rounded,
                                color: FlutterFlowTheme.of(context).primary,
                                size: 24.0,
                              ),
                              onPressed: () async {
                                logFirebaseEvent('IconButton_navigate_back');
                                context.safePop();
                              },
                            ),
                            Expanded(
                              child: Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'qryfmp8h' /* Personal Details */,
                                  ),
                                  style: FlutterFlowTheme.of(context)
                                      .headlineMedium
                                      .override(
                                        fontFamily: 'SFHebrew',
                                        color: FlutterFlowTheme.of(context).primary,
                                        letterSpacing: 0.0,
                                      ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      centerTitle: false,
                      expandedTitleScale: 1.0,
                    ),
                    toolbarHeight: 80.0,
                    elevation: 2.0,
                  )
                : null,
            body: SafeArea(
              top: true,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Form(
                      key: _model.formKey,
                      autovalidateMode: AutovalidateMode.disabled,
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'o2kuu6e1' /* Personalized Nutrition Insight... */,
                                      ),
                                      textAlign: TextAlign.center,
                                      style: FlutterFlowTheme.of(context)
                                          .headlineSmall
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            fontSize: 22.0,
                                            letterSpacing: 0.0,
                                            lineHeight: 1.2,
                                          ),
                                    ),
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 6.0, 0.0, 0.0),
                                      child: Text(
                                        FFLocalizations.of(context).getText(
                                          'pjqfg7h9' /* Provide a few details to creat... */,
                                        ),
                                        textAlign: TextAlign.center,
                                        style: FlutterFlowTheme.of(context)
                                            .labelMedium
                                            .override(
                                              fontFamily: 'SFHebrew',
                                              fontSize: 13.0,
                                              letterSpacing: 0.0,
                                              lineHeight: 1.5,
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        '7fg1gn3i' /* DOB */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                    Container(
                                      width: double.infinity,
                                      height: 200.0,
                                      child: custom_widgets.CustomDatePicker(
                                        width: double.infinity,
                                        height: 200.0,
                                        initialDate: functions.stringToDateTime(
                                            FFAppState().savedUserData.dob),
                                        onDateChanged: (selectedDate) async {
                                          logFirebaseEvent(
                                              'CustomDatePicker_update_page_state');
                                          _model.dobDateTime = selectedDate;
                                          safeSetState(() {});
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 10.0, 0.0, 0.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    Text(
                                      FFLocalizations.of(context).getText(
                                        'cgf4qqwk' /* Gender */,
                                      ),
                                      style: FlutterFlowTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'SFHebrew',
                                            letterSpacing: 0.0,
                                          ),
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        FlutterFlowRadioButton(
                                          options: [
                                            FFLocalizations.of(context).getText(
                                              'jpcaqb7c' /* Male */,
                                            ),
                                            FFLocalizations.of(context).getText(
                                              '2poewxi8' /* Female */,
                                            ),
                                            FFLocalizations.of(context).getText(
                                              'h2zijadv' /* Other */,
                                            )
                                          ].toList(),
                                          onChanged: (val) async {
                                            safeSetState(() {});
                                            logFirebaseEvent(
                                                'gender_set_form_field');
                                            safeSetState(() {
                                              _model.genderValueController?.value =
                                                  _model.genderValue!;
                                            });
                                          },
                                          controller:
                                              _model.genderValueController ??=
                                                  FormFieldController<String>(
                                                      FFAppState()
                                                          .savedUserData
                                                          .gender),
                                          optionHeight: 32.0,
                                          textStyle: FlutterFlowTheme.of(context)
                                              .labelMedium
                                              .override(
                                                fontFamily: 'SFHebrew',
                                                letterSpacing: 0.0,
                                              ),
                                          selectedTextStyle:
                                              FlutterFlowTheme.of(context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'SFHebrew',
                                                    letterSpacing: 0.0,
                                                  ),
                                          buttonPosition: RadioButtonPosition.left,
                                          direction: Axis.horizontal,
                                          radioButtonColor:
                                              FlutterFlowTheme.of(context).primary,
                                          inactiveRadioButtonColor:
                                              FlutterFlowTheme.of(context)
                                                  .secondaryText,
                                          toggleable: false,
                                          horizontalAlignment: WrapAlignment.start,
                                          verticalAlignment:
                                              WrapCrossAlignment.start,
                                        ),
                                      ],
                                    ),
                                    Divider(
                                      thickness: 1.0,
                                      indent: 0.0,
                                      endIndent: 0.0,
                                      color: FlutterFlowTheme.of(context).primary,
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.stretch,
                                  children: [
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 10.0, 0.0, 0.0),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            FFLocalizations.of(context).getText(
                                              'viedfyeo' /* Your Height & Weight */,
                                            ),
                                            style: FlutterFlowTheme.of(context)
                                                .headlineSmall
                                                .override(
                                                  fontFamily: 'SFHebrew',
                                                  letterSpacing: 0.0,
                                                  lineHeight: 1.2,
                                                ),
                                          ),
                                          Padding(
                                            padding: EdgeInsetsDirectional.fromSTEB(
                                                0.0, 6.0, 0.0, 0.0),
                                            child: Text(
                                              FFLocalizations.of(context).getText(
                                                'byo28xe7' /* Choose your preferred unit sys... */,
                                              ),
                                              textAlign: TextAlign.center,
                                              style: FlutterFlowTheme.of(context)
                                                  .labelMedium
                                                  .override(
                                                    fontFamily: 'SFHebrew',
                                                    letterSpacing: 0.0,
                                                    lineHeight: 1.5,
                                                  ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          height:
                                              MediaQuery.sizeOf(context).height *
                                                  0.42,
                                          child: custom_widgets.UnitConverterWheel(
                                            width: double.infinity,
                                            height:
                                                MediaQuery.sizeOf(context).height *
                                                    0.42,
                                            initialValue: <String, dynamic>{
                                              'height_value': FFAppState()
                                                  .savedUserData
                                                  .heightValue,
                                              'height_unit': FFAppState()
                                                  .savedUserData
                                                  .heightUnit,
                                              'weight_value': FFAppState()
                                                  .savedUserData
                                                  .weightValue,
                                              'weight_unit': FFAppState()
                                                  .savedUserData
                                                  .weightUnit,
                                            },
                                            onChange: (data) async {
                                              logFirebaseEvent(
                                                  'UnitConverterWheel_update_app_state');
                                              FFAppState()
                                                  .updateSavedUserDataStruct(
                                                (e) => e
                                                  ..heightUnit = getJsonField(
                                                    data,
                                                    r'''$.height_unit''',
                                                  ).toString()
                                                  ..heightValue = getJsonField(
                                                    data,
                                                    r'''$.height_value''',
                                                  )
                                                  ..weightUnit = getJsonField(
                                                    data,
                                                    r'''$.weight_unit''',
                                                  ).toString()
                                                  ..weightValue = getJsonField(
                                                    data,
                                                    r'''$.weight_value''',
                                                  ),
                                              );
                                              safeSetState(() {});
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  FFButtonWidget(
                    onPressed: () async {
                      logFirebaseEvent('Button_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e
                          ..dob = _model.dobDateTime?.toString()
                          ..gender = _model.genderValue
                          ..weightUnit = 'kg'
                          ..heightUnit = 'cm',
                      );
                      safeSetState(() {});
                      logFirebaseEvent('Button_navigate_back');
                      context.safePop();
                    },
                    text: FFLocalizations.of(context).getText(
                      'd9zpqvzf' /* Save */,
                    ),
                    options: FFButtonOptions(
                      width: MediaQuery.sizeOf(context).width * 0.4,
                      height: 50.0,
                      padding: EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
                      iconPadding:
                          EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                      color: FlutterFlowTheme.of(context).primary,
                      textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                            fontFamily: 'SFHebrew',
                            color: Colors.white,
                            letterSpacing: 0.0,
                          ),
                      elevation: 0.0,
                      borderRadius: BorderRadius.circular(30.0),
                    ),
                  ),
                ].addToEnd(SizedBox(height: 30.0)),
              ),
            ),
          ),
        )
    );
  }
}

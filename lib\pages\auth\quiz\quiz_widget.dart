import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:vertical_weight_slider/vertical_weight_slider.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/componentes/center_image/center_image_widget.dart';
import '/componentes/circular_progress/circular_progress_widget.dart';
import '/componentes/progressive_appbar/progressive_appbar_widget.dart';
import '/componentes/segmented/segmented_widget.dart';
import '/componentes/selectable_item/selectable_item_widget.dart';
import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_radio_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/flutter_flow/flutter_flow_widgets.dart';
import '/flutter_flow/form_field_controller.dart';
import 'dart:async';
import 'dart:ui' as ui;
import '/custom_code/actions/index.dart' as actions;
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/custom_functions.dart' as functions;
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:webviewx_plus/webviewx_plus.dart';
import 'quiz_model.dart';
export 'quiz_model.dart';
import 'dart:io' show Platform;

class QuizWidget extends StatefulWidget {
  const QuizWidget({
    super.key,
    bool? isForSignIn,
    bool? isForAdjustGoal,
  })  : this.isForSignIn = isForSignIn ?? false,
        this.isForAdjustGoal = isForAdjustGoal ?? false;

  final bool isForSignIn;
  final bool isForAdjustGoal;

  @override
  State<QuizWidget> createState() => _QuizWidgetState();
}

class _QuizWidgetState extends State<QuizWidget> with TickerProviderStateMixin {
  late QuizModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = <String, AnimationInfo>{};

  @override
  void initState() {
    if (!widget.isForAdjustGoal) {
      FFAppState().deleteSavedUserData();
      FFAppState().savedUserData = UserDataStruct();
    }
    super.initState();
    _model = createModel(context, () => QuizModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      if (widget.isForSignIn) {
        logFirebaseEvent('quiz_custom_action');
        _model.deviceIdOutput = await actions.getDeviceId();
        logFirebaseEvent('quiz_update_page_state');
        _model.deviceId = _model.deviceIdOutput;
        safeSetState(() {});
        return;
      } else {
        logFirebaseEvent('quiz_custom_action');
        _model.deviceIdOutput1 = await actions.getDeviceId();
        logFirebaseEvent('quiz_update_page_state');
        _model.deviceId = _model.deviceIdOutput1;
        safeSetState(() {});
        if (!widget.isForAdjustGoal) {
          return;
        }
      }

      if (FFAppState().savedUserData.gender != null && FFAppState().savedUserData.gender != '') {
        logFirebaseEvent('quiz_set_form_field');
        safeSetState(() {
          _model.genderValueController?.value = FFAppState().savedUserData.gender;
        });
      }
    });

    // _model.weightinputTextController ??= TextEditingController();
    // _model.weightinputFocusNode ??= FocusNode();

    _model.emailAddressTextController ??= TextEditingController();
    _model.emailAddressFocusNode ??= FocusNode();

    _model.passwordTextController ??= TextEditingController();
    _model.passwordFocusNode ??= FocusNode();

    _model.confirmPasswordTextController ??= TextEditingController();
    _model.confirmPasswordFocusNode ??= FocusNode();

    animationsMap.addAll({
      'containerOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          VisibilityEffect(duration: 1.ms),
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 0.0.ms,
            duration: 300.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
          ScaleEffect(
            curve: Curves.bounceOut,
            delay: 0.0.ms,
            duration: 300.0.ms,
            begin: Offset(0.6, 1.0),
            end: Offset(1.0, 1.0),
          ),
        ],
      ),
      'columnOnPageLoadAnimation': AnimationInfo(
        trigger: AnimationTrigger.onPageLoad,
        effectsBuilder: () => [
          FadeEffect(
            curve: Curves.easeInOut,
            delay: 200.0.ms,
            duration: 400.0.ms,
            begin: 0.0,
            end: 1.0,
          ),
          MoveEffect(
            curve: Curves.easeInOut,
            delay: 200.0.ms,
            duration: 400.0.ms,
            begin: Offset(0.0, 60.0),
            end: Offset(0.0, 0.0),
          ),
          TiltEffect(
            curve: Curves.easeInOut,
            delay: 200.0.ms,
            duration: 400.0.ms,
            begin: Offset(-0.349, 0),
            end: Offset(0, 0),
          ),
        ],
      ),
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => safeSetState(() {}));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
        name: 'Quiz',
        builder: (context) => GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
                FocusManager.instance.primaryFocus?.unfocus();
              },
              child: WillPopScope(
                onWillPop: () async => false,
                child: Scaffold(
                  key: scaffoldKey,
                  backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
                  body: Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(
                        0.0,
                        0.0,
                        0.0,
                        valueOrDefault<double>(
                          FFAppState().bottomPadding,
                          0.0,
                        )),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        progressBarWidget(context),
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            height: MediaQuery.sizeOf(context).height * 0.8,
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                              child: PageView(
                                physics: const NeverScrollableScrollPhysics(),
                                controller: _model.pageViewController ??= PageController(
                                    initialPage: max(
                                        0,
                                        min(
                                            valueOrDefault<int>(
                                              widget.isForSignIn ? 12 : 0,
                                              0,
                                            ),
                                            12))),
                                onPageChanged: (_) => safeSetState(() {}),
                                scrollDirection: Axis.horizontal,
                                children: [
                                  /// 0
                                  HeightWeightPage(_model, safeSetState),

                                  /// 1
                                  DobPage(_model, safeSetState),

                                  /// 2
                                  FitnessGoalPage(_model, safeSetState),

                                  /// 3
                                  ImageChartPage(_model, safeSetState),

                                  /// 4
                                  PerWeekExercisePage(_model, safeSetState),

                                  /// 5
                                  TargetWeight(_model, safeSetState),

                                  /// 6
                                  // BigGraphPage(_model, safeSetState),

                                  /// 7
                                  ChallengesToAchievePage(_model, safeSetState),

                                  /// 8
                                  AccomplishmentPage(_model, safeSetState),

                                  /// 9
                                  RatePage(_model, safeSetState),

                                  /// 10
                                  CongratulationsPage(_model, safeSetState),

                                  /// 11
                                  LoginSignUpPage(_model, safeSetState, animationsMap, widget.isForSignIn),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Builder(
                          builder: (context) {
                            if (_model.pageViewCurrentIndex <= 8) {
                              return Builder(
                                builder: (context) => Container(
                                  padding: EdgeInsets.only(bottom: 16),
                                  child: FFButtonWidget(
                                    onPressed: onBottomButtonPressed(context, isEnglish),
                                    text: bottomButtonText(isEnglish),
                                    options: FFButtonOptions(
                                      width: MediaQuery.sizeOf(context).width * 0.55,
                                      height: valueOrDefault<double>(
                                        _model.pageViewCurrentIndex <= 8 ? 50.0 : 0.0,
                                        50.0,
                                      ),
                                      padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                                      iconAlignment: IconAlignment.start,
                                      iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                                      color: FlutterFlowTheme.of(context).primary,
                                      textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                                            fontFamily: 'SFHebrew',
                                            color: Colors.white,
                                            letterSpacing: 0.0,
                                          ),
                                      elevation: 0.0,
                                      borderRadius: BorderRadius.circular(30.0),
                                      disabledColor: Color(0xC657636C),
                                    ),
                                  ),
                                ),
                              );
                            } else {
                              return Container(
                                width: 0.0,
                                height: 0.0,
                                decoration: BoxDecoration(),
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  Widget progressBarWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(
          0.0,
          valueOrDefault<double>(
            FFAppState().topPadding,
            0.0,
          ),
          0.0,
          0.0),
      child: wrapWithModel(
        model: _model.progressiveAppbarModel,
        updateCallback: () => safeSetState(() {}),
        child: ProgressiveAppbarWidget(
          pageIndex: _model.pageViewCurrentIndex,
          onBackTap: () async {
            if (_model.pageViewCurrentIndex == 0) {
              logFirebaseEvent('progressive_appbar_navigate_back');
              context.safePop();
            } else {
              logFirebaseEvent('progressive_appbar_page_view');
              await _model.pageViewController?.previousPage(
                duration: Duration(milliseconds: 300),
                curve: Curves.ease,
              );
            }
          },
        ),
      ),
    );
  }

  String bottomButtonText(bool isEnglish) {
    return valueOrDefault<String>(
      (_model.pageViewCurrentIndex == 7) && widget.isForAdjustGoal
          ? isEnglish
              ? 'Update'
              : 'לְעַדְכֵּן'
          : isEnglish
              ? 'Next'
              : 'הבא',
      isEnglish ? 'Next' : 'הבא',
    );
  }

  dynamic Function()? onBottomButtonPressed(BuildContext context, bool isEnglish) {
    return !() {
      if (_model.pageViewCurrentIndex == 1) {
        return ((FFAppState().savedUserData.dob != null && FFAppState().savedUserData.dob != '') &&
            (FFAppState().savedUserData.gender != null && FFAppState().savedUserData.gender != ''));
      } else if (_model.pageViewCurrentIndex == 0) {
        return ((FFAppState().savedUserData.heightUnit != null && FFAppState().savedUserData.heightUnit != '') &&
            ((FFAppState().savedUserData.heightValue != null) && (FFAppState().savedUserData.heightValue != 0)) &&
            (FFAppState().savedUserData.weightUnit != null && FFAppState().savedUserData.weightUnit != '') &&
            ((FFAppState().savedUserData.weightValue != null) && (FFAppState().savedUserData.weightValue != 0.0)));
      } else if (_model.pageViewCurrentIndex == 4) {
        return ((FFAppState().savedUserData.workoutFrequencyId != null) &&
            (FFAppState().savedUserData.workoutFrequencyId != 0));
      } else if (_model.pageViewCurrentIndex == 2) {
        return ((FFAppState().savedUserData.fitnessGoalId != null) && (FFAppState().savedUserData.fitnessGoalId != 0));
      } else if (_model.pageViewCurrentIndex == 5) {
        return (valueOrDefault<bool>(
              (FFAppState().savedUserData.targetWeightValue != null) &&
                  valueOrDefault<bool>(
                    FFAppState().savedUserData.targetWeightValue != 0.0,
                    false,
                  ),
              false,
            ) &&
            valueOrDefault<bool>(
              FFAppState().savedUserData.targetWeightUnit != null && FFAppState().savedUserData.targetWeightUnit != '',
              false,
            ));
      } else if (_model.pageViewCurrentIndex == 6) {
        return (FFAppState().savedUserData.challenges != 0);
      } else if (_model.pageViewCurrentIndex == 7) {
        return (FFAppState().savedUserData.accomplishmentId != 0);
      } else {
        return true;
      }
    }()
        ? null
        : () async {
            if ((_model.pageViewCurrentIndex == 7) && widget.isForAdjustGoal) {
              logFirebaseEvent('Button_custom_action');
              unawaited(
                () async {
                  await actions.hideSoftKeyboard();
                }(),
              );
              logFirebaseEvent('Button_wait__delay');
              await Future.delayed(const Duration(milliseconds: 10));
              logFirebaseEvent('Button_alert_dialog');
              showDialog(
                context: context,
                barrierDismissible: false, // Prevent dismissing by tapping outside
                builder: (dialogContext) {
                  return Dialog(
                    elevation: 0,
                    insetPadding: EdgeInsets.zero,
                    backgroundColor: Colors.transparent,
                    alignment: AlignmentDirectional(0.0, 0.0).resolve(Directionality.of(context)),
                    child: WebViewAware(
                      child: Container(
                        height: 100.0,
                        width: 100.0,
                        child: CircularProgressWidget(),
                      ),
                    ),
                  );
                },
              );

              logFirebaseEvent('Button_backend_call');
              _model.apiResultUpdateGoal = await UpdateGoalCall.call(
                jsonJson: functions.updateGoalAPIJson(FFAppState().savedUserData),
                accessToken: FFAppState().authToken,
              );

              logFirebaseEvent('Button_wait__delay');
              await Future.delayed(const Duration(milliseconds: 50));
              logFirebaseEvent('Button_dismiss_dialog');
              Navigator.pop(context);
              if ((_model.apiResultUpdateGoal?.succeeded ?? true)) {
                logFirebaseEvent('Button_navigate_back');
                context.safePop();
                logFirebaseEvent('Button_show_snack_bar');
                ScaffoldMessenger.of(context).clearSnackBars();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isEnglish ? 'The destination was updated successfully!' : 'היעד עודכן בהצלחה!',
                      style: FlutterFlowTheme.of(context).labelMedium.override(
                            fontFamily: 'SFHebrew',
                            color: FlutterFlowTheme.of(context).alternate,
                            letterSpacing: 0.0,
                          ),
                    ),
                    duration: Duration(milliseconds: 3000),
                    backgroundColor: FlutterFlowTheme.of(context).success,
                  ),
                );
              }
            } else {
              logFirebaseEvent('Button_custom_action');
              await actions.hideSoftKeyboard();
              logFirebaseEvent('Button_page_view');
              await _model.pageViewController?.nextPage(
                duration: Duration(milliseconds: 300),
                curve: Curves.ease,
              );
            }

            safeSetState(() {});
          };
  }
}

class TargetWeight extends StatelessWidget {
  const TargetWeight(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    '6j9vow13' /* Select Your Ideal Weight and S... */,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                        lineHeight: 1.2,
                      ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'pik2qq2c' /* Stay informed about your food ... */,
                    ),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).labelMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                          lineHeight: 1.5,
                        ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 30.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Container(
                //   width: double.infinity,
                //   child: TextFormField(
                //     key: ValueKey('dob'),
                //     controller: model.weightinputTextController,
                //     focusNode: model.weightinputFocusNode,
                //     onChanged: (_) => EasyDebounce.debounce(
                //       '_model.weightinputTextController',
                //       Duration(milliseconds: 100),
                //       () async {
                //         logFirebaseEvent('weightinput_update_app_state');
                //         FFAppState().updateSavedUserDataStruct(
                //           (e) => e
                //             ..targetWeightValue = double.tryParse(
                //                 model.weightinputTextController.text)
                //             ..targetWeightUnit = 'kg',
                //         );
                //         safeSetState(() {});
                //       },
                //     ),
                //     autofocus: false,
                //     textInputAction: TextInputAction.done,
                //     obscureText: false,
                //     decoration: InputDecoration(
                //       isDense: false,
                //       labelStyle:
                //           FlutterFlowTheme.of(context).labelMedium.override(
                //                 fontFamily: 'SFHebrew',
                //                 letterSpacing: 0.0,
                //               ),
                //       hintText: FFLocalizations.of(context).getText(
                //         'l2429cur' /* Add Weight */,
                //       ),
                //       hintStyle:
                //           FlutterFlowTheme.of(context).labelMedium.override(
                //                 fontFamily: 'SFHebrew',
                //                 letterSpacing: 0.0,
                //               ),
                //       enabledBorder: UnderlineInputBorder(
                //         borderSide: BorderSide(
                //           color: FlutterFlowTheme.of(context).primary,
                //           width: 1.0,
                //         ),
                //         borderRadius: const BorderRadius.only(
                //           topLeft: Radius.circular(4.0),
                //           topRight: Radius.circular(4.0),
                //         ),
                //       ),
                //       focusedBorder: UnderlineInputBorder(
                //         borderSide: BorderSide(
                //           color: FlutterFlowTheme.of(context).primary,
                //           width: 1.0,
                //         ),
                //         borderRadius: const BorderRadius.only(
                //           topLeft: Radius.circular(4.0),
                //           topRight: Radius.circular(4.0),
                //         ),
                //       ),
                //       errorBorder: UnderlineInputBorder(
                //         borderSide: BorderSide(
                //           color: FlutterFlowTheme.of(context).error,
                //           width: 1.0,
                //         ),
                //         borderRadius: const BorderRadius.only(
                //           topLeft: Radius.circular(4.0),
                //           topRight: Radius.circular(4.0),
                //         ),
                //       ),
                //       focusedErrorBorder: UnderlineInputBorder(
                //         borderSide: BorderSide(
                //           color: FlutterFlowTheme.of(context).error,
                //           width: 1.0,
                //         ),
                //         borderRadius: const BorderRadius.only(
                //           topLeft: Radius.circular(4.0),
                //           topRight: Radius.circular(4.0),
                //         ),
                //       ),
                //       filled: true,
                //       fillColor:
                //           FlutterFlowTheme.of(context).primaryBackground,
                //     ),
                //     style: FlutterFlowTheme.of(context).bodyMedium.override(
                //           fontFamily: 'SFHebrew',
                //           letterSpacing: 0.0,
                //         ),
                //     minLines: 1,
                //     maxLength: 5,
                //     buildCounter: (context,
                //             {required currentLength,
                //             required isFocused,
                //             maxLength}) =>
                //         null,
                //     keyboardType:
                //         const TextInputType.numberWithOptions(decimal: true),
                //     cursorColor: FlutterFlowTheme.of(context).primaryText,
                //     validator: model.weightinputTextControllerValidator
                //         .asValidator(context),
                //     inputFormatters: [
                //       FilteringTextInputFormatter.allow(RegExp('[0-9]'))
                //     ],
                //   ),
                // ),
                // Align(
                //   alignment:
                //       AlignmentDirectional(1.0, 0.0),
                //   child: Container(
                //     width: 120.0,
                //     decoration: BoxDecoration(),
                //     child: wrapWithModel(
                //       model: _model.segmentedModel1,
                //       updateCallback: () =>
                //           safeSetState(() {}),
                //       child: SegmentedWidget(
                //         segmentItems: ["kg", "lbs"],
                //         initialValue:
                //             valueOrDefault<String>(
                //           FFAppState()
                //               .savedUserData
                //               .targetWeightUnit,
                //           'kg',
                //         ),
                //         onSelect: (title) async {
                //           logFirebaseEvent(
                //               'segmented_update_app_state');
                //           FFAppState()
                //               .updateSavedUserDataStruct(
                //             (e) => e
                //               ..targetWeightUnit =
                //                   title,
                //           );
                //           safeSetState(() {});
                //         },
                //       ),
                //     ),
                //   ),
                // ),

                WeightSliderWidget(model, safeSetState)
              ].divide(SizedBox(height: 2.0)),
            ),
          ),
        ],
      ),
    );
  }
}

class WeightSliderWidget extends StatefulWidget {
  final QuizModel model;
  final safeSetState;

  const WeightSliderWidget(this.model, this.safeSetState, {super.key});

  @override
  State<WeightSliderWidget> createState() => _WeightSliderWidgetState();
}

class _WeightSliderWidgetState extends State<WeightSliderWidget> {
  double _weight = FFAppState().savedUserData.targetWeightValue;

  @override
  Widget build(BuildContext context) {
    _weight = getTargetWeightValue();
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Column(
      children: [
        Row(
          textDirection: isEnglish ? ui.TextDirection.ltr : ui.TextDirection.rtl,
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 55,
              alignment: Alignment.center,
              child: Text(
                "${_weight.toStringAsFixed(0)} ${isEnglish ? 'kg' : 'קילוגרם'}",
                style: TextStyle(
                  fontSize: 45,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'SFHebrew',
                ),
              ),
            ),
            SizedBox(width: 10),
            Container(
              padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 5),
              child: Text(
                FFLocalizations.of(context).getText(
                  'wforbk53' /* Weight */,
                ),
                style: FlutterFlowTheme.of(context).bodyMedium.override(
                      fontFamily: 'SFHebrew',
                      letterSpacing: 0.0,
                    ),
              ),
            ),
          ],
        ),
        SizedBox(height: 20),
        VerticalWeightSlider(
          controller: WeightSliderController(
            initialWeight: getTargetWeightValue(),
            minWeight: 30,
            interval: 0.2,
            maxWeight: 130,
          ),
          decoration: const PointerDecoration(
            width: 130,
            height: 3,
            largeColor: Color(0xFF898989),
            mediumColor: Color(0xFFC5C5C5),
            smallColor: Color(0xFFF0F0F0),
            gap: 30,
          ),
          haptic: Haptic.selectionClick,
          isVertical: false,
          height: 100,
          diameterRatio: 6,
          unit: MeasurementUnit.kg,
          onChanged: (double value) {
            widget.safeSetState(() {
              _weight = value;
            });
            FFAppState().updateSavedUserDataStruct(
              (e) => e
                ..targetWeightValue = _weight
                ..targetWeightUnit = 'kg',
            );
            widget.safeSetState(() {});
          },
          indicator: Container(
            height: 3,
            width: 200,
            alignment: Alignment.centerLeft,
            color: Colors.red[300],
          ),
        ),
      ],
    );
  }

  double getTargetWeightValue() {
    double targetWeightValue = FFAppState().savedUserData.targetWeightValue;
    int weightValue = FFAppState().savedUserData.weightValue;
    if (weightValue == 0) {
      return 30.0;
    } else {
      if (targetWeightValue != 0) {
        return targetWeightValue;
      } else {
        return weightValue.toDouble();
      }
    }
  }
}

class CongratulationsPage extends StatelessWidget {
  const CongratulationsPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      constraints: BoxConstraints(
        minWidth: double.infinity,
        minHeight: double.infinity,
        maxWidth: double.infinity,
        maxHeight: double.infinity,
      ),
      decoration: BoxDecoration(
        color: FlutterFlowTheme.of(context).secondaryBackground,
        image: DecorationImage(
          fit: BoxFit.cover,
          image: Image.asset(
            'assets/images/all_done.png',
          ).image,
        ),
      ),
      child: Container(
        // width: double.infinity,
        // height: double.infinity,
        child: Stack(
          children: [
            Align(
              alignment: AlignmentDirectional(0.0, 0.0),
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Material(
                  color: Colors.transparent,
                  elevation: 5.0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(10.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8.0),
                                child: SvgPicture.asset(
                                  'assets/images/check_badge.svg',
                                  width: 24.0,
                                  height: 24.0,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(5.0, 0.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'lj7bbojn' /* All done! */,
                                  ),
                                  style: FlutterFlowTheme.of(context).titleSmall.override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                      ),
                                ),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  FFLocalizations.of(context).getText(
                                    '29daqe0x' /* Thank you for trusting us */
                                    ,
                                  ),
                                  textAlign: TextAlign.center,
                                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                                        fontFamily: 'SFHebrew',
                                        letterSpacing: 0.0,
                                        lineHeight: 1.2,
                                      ),
                                ),
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      '495opshv' /* We promise to always keep your... */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: FlutterFlowTheme.of(context).labelMedium.override(
                                          fontFamily: 'SFHebrew',
                                          letterSpacing: 0.0,
                                          lineHeight: 1.5,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Align(
              alignment: AlignmentDirectional(0.0, 1.0),
              child: Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0, 0.0, 0.0, 16),
                child: FFButtonWidget(
                  onPressed: () async {
                    logFirebaseEvent('Button_page_view');
                    await model.pageViewController?.nextPage(
                      duration: Duration(milliseconds: 300),
                      curve: Curves.ease,
                    );
                  },
                  text: FFLocalizations.of(context).getText(
                    'd9zpqvzf' /* Continue */,
                  ),
                  options: FFButtonOptions(
                    width: MediaQuery.sizeOf(context).width * 0.5,
                    height: 50.0,
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                    iconAlignment: IconAlignment.end,
                    iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                    color: FlutterFlowTheme.of(context).primary,
                    textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                          fontFamily: 'SFHebrew',
                          color: Colors.white,
                          letterSpacing: 0.0,
                        ),
                    elevation: 5.0,
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RatePage extends StatelessWidget {
  const RatePage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Padding(
      padding: EdgeInsets.all(0.0),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(0),
            child: Container(
              width: double.infinity,
              height: MediaQuery.of(context).size.height * 0.7,
              child: Image.asset(
                isEnglish ? 'assets/images/rate_image_en.png' : 'assets/images/rate_image_he.png',
                fit: BoxFit.fill,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class AccomplishmentPage extends StatelessWidget {
  const AccomplishmentPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    'jeiro3lw' /* What would you like to accompl... */
                    ,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                        lineHeight: 1.2,
                      ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.accomplishmentItem1Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'To eat and live healthier',
                        heText: 'לאכול ולחיות בריא יותר',
                      ),
                      'To eat and live healthier',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.accomplishmentId == 1,
                    onTap: () async {
                      logFirebaseEvent('accomplishment_item1_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..accomplishmentId = 1,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.eatLiveHealthy,
                      isSelected: FFAppState().savedUserData.accomplishmentId == 1,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.accomplishmentItem2Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'To increase my energy and motivation',
                        heText: 'להגביר את האנרגיה והמוטיבציה שלי',
                      ),
                      'To increase my energy and motivation',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.accomplishmentId == 2,
                    onTap: () async {
                      logFirebaseEvent('accomplishment_item2_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..accomplishmentId = 2,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.boostEnergy,
                      isSelected: FFAppState().savedUserData.accomplishmentId == 2,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.accomplishmentItem3Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'To improve my nutritional discipline',
                        heText: 'לשפר את המשמעת התזונתית שלי',
                      ),
                      'To improve my nutritional discipline',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.accomplishmentId == 3,
                    onTap: () async {
                      logFirebaseEvent('accomplishment_item3_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..accomplishmentId = 3,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.stayMotivate,
                      isSelected: FFAppState().savedUserData.accomplishmentId == 3,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.accomplishmentItem4Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'To feel good about my body',
                        heText: 'להרגיש טוב לגבי הגוף שלי',
                      ),
                      'To feel good about my body',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.accomplishmentId == 4,
                    onTap: () async {
                      logFirebaseEvent('accomplishment_item4_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..accomplishmentId = 4,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.feelBody,
                      isSelected: FFAppState().savedUserData.accomplishmentId == 4,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ].divide(SizedBox(height: 20.0)),
      ),
    );
  }
}

class DobPage extends StatelessWidget {
  const DobPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: model.formKey2,
      autovalidateMode: AutovalidateMode.disabled,
      child: Padding(
        padding: EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      '6a63ld8i' /* What is your date of birth? */,
                    ),
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                          lineHeight: 1.2,
                        ),
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        'aa0uwcgy' /* We will use this to build a suitable plan for you! */,
                      ),
                      textAlign: TextAlign.center,
                      style: FlutterFlowTheme.of(context).labelMedium.override(
                            fontFamily: 'SFHebrew',
                            letterSpacing: 0.0,
                            lineHeight: 1.5,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      'duvmrxuy' /* DOB */,
                    ),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                        ),
                  ),
                  Container(
                    width: double.infinity,
                    height: 200.0,
                    child: custom_widgets.CustomDatePicker(
                      width: double.infinity,
                      height: 200.0,
                      initialDate: functions.stringToDateTime(FFAppState().savedUserData.dob),
                      onDateChanged: (selectedDate) async {
                        logFirebaseEvent('CustomDatePicker_update_app_state');
                        FFAppState().updateSavedUserDataStruct(
                          (e) => e..dob = functions.dateTimeToString(selectedDate),
                        );
                        safeSetState(() {});
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      'cebmt0tq' /* Gender */,
                    ),
                    style: FlutterFlowTheme.of(context).bodyMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                        ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      FlutterFlowRadioButton(
                        options: [
                          FFLocalizations.of(context).getText(
                            'r13uhl3s' /* Male */,
                          ),
                          FFLocalizations.of(context).getText(
                            '8qajh77m' /* Female */,
                          ),
                          FFLocalizations.of(context).getText(
                            'ul2n0znu' /* Other */,
                          )
                        ].toList(),
                        onChanged: (val) async {
                          safeSetState(() {});
                          logFirebaseEvent('gender_update_app_state');
                          FFAppState().updateSavedUserDataStruct(
                            (e) => e..gender = model.genderValue,
                          );
                          safeSetState(() {});
                        },
                        controller: model.genderValueController ??= FormFieldController<String>(null),
                        optionHeight: 32.0,
                        textStyle: FlutterFlowTheme.of(context).labelMedium.override(
                              fontFamily: 'SFHebrew',
                              letterSpacing: 0.0,
                            ),
                        selectedTextStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'SFHebrew',
                              letterSpacing: 0.0,
                            ),
                        buttonPosition: RadioButtonPosition.left,
                        direction: Axis.horizontal,
                        radioButtonColor: FlutterFlowTheme.of(context).primary,
                        inactiveRadioButtonColor: FlutterFlowTheme.of(context).secondaryText,
                        toggleable: false,
                        horizontalAlignment: WrapAlignment.start,
                        verticalAlignment: WrapCrossAlignment.start,
                      ),
                    ],
                  ),
                  Divider(
                    thickness: 1.0,
                    indent: 0.0,
                    endIndent: 0.0,
                    color: FlutterFlowTheme.of(context).primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HeightWeightPage extends StatelessWidget {
  const HeightWeightPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: model.formKey1,
      autovalidateMode: AutovalidateMode.disabled,
      child: Padding(
        padding: EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    FFLocalizations.of(context).getText(
                      '25129kcj' /* Height & Weight */,
                    ),
                    style: FlutterFlowTheme.of(context).headlineSmall.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                          lineHeight: 1.2,
                        ),
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                    child: Text(
                      FFLocalizations.of(context).getText(
                        '99fb1t5x' /* This will be used to calibrate... */,
                      ),
                      textAlign: TextAlign.center,
                      style: FlutterFlowTheme.of(context).labelMedium.override(
                            fontFamily: 'SFHebrew',
                            letterSpacing: 0.0,
                            lineHeight: 1.5,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  width: double.infinity,
                  height: MediaQuery.sizeOf(context).height * 0.45,
                  child: custom_widgets.UnitConverterWheel(
                    width: double.infinity,
                    height: MediaQuery.sizeOf(context).height * 0.45,
                    initialValue: initialValue(),
                    onChange: (data) async {
                      logFirebaseEvent('UnitConverterWheel_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e
                          ..heightUnit = getJsonField(
                            data,
                            r'''$.height_unit''',
                          ).toString()
                          ..heightValue = getJsonField(
                            data,
                            r'''$.height_value''',
                          )
                          ..weightUnit = getJsonField(
                            data,
                            r'''$.weight_unit''',
                          ).toString()
                          ..weightValue = getJsonField(
                            data,
                            r'''$.weight_value''',
                          ),
                      );
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        safeSetState(() {});
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic>? initialValue() {
    var heightValue = FFAppState().savedUserData.heightValue;
    var heightUnit = FFAppState().savedUserData.heightUnit;
    var weightValue = FFAppState().savedUserData.weightValue;
    var weightUnit = FFAppState().savedUserData.weightUnit;
    if (heightValue == 0) {
      return null;
    }
    return <String, dynamic>{
      'height_value': heightValue,
      'height_unit': heightUnit,
      'weight_value': weightValue,
      'weight_unit': weightUnit,
    };
  }
}

class ImageChartPage extends StatelessWidget {
  const ImageChartPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Padding(
        //   padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
        //   child: Column(
        //     mainAxisSize: MainAxisSize.min,
        //     mainAxisAlignment: MainAxisAlignment.center,
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       Padding(
        //         padding: EdgeInsets.all(20.0),
        //         child: Text(
        //           FFLocalizations.of(context).getText(
        //             'blcn7tgf' /* CalCounty customers preserve feces for the long term! */,
        //           ),
        //           style: FlutterFlowTheme.of(context).headlineSmall.override(
        //                 fontFamily: 'SFHebrew',
        //                 letterSpacing: 0.0,
        //                 lineHeight: 1.2,
        //               ),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        Align(
          alignment: AlignmentDirectional(0.0, 1.0),
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(25.0),
              bottomRight: Radius.circular(25.0),
              topLeft: Radius.circular(0.0),
              topRight: Radius.circular(0.0),
            ),
            child: Image.asset(
              isEnglish ? 'assets/images/preserve_feces_en.png' : 'assets/images/preserve_feces_he.png',
              // width: MediaQuery.sizeOf(context).width * 0.6,
              height: MediaQuery.sizeOf(context).height * 0.67,
              fit: BoxFit.contain,
              alignment: Alignment(0.0, 1.0),
            ),
          ),
        ),
      ].divide(SizedBox(height: 20.0)),
    );
  }
}

class PerWeekExercisePage extends StatelessWidget {
  const PerWeekExercisePage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    'c5fzwo1u' /* How many times a week do you exercise? */,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                        lineHeight: 1.2,
                      ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      '624odt7n' /* We will use this to optimize your plan. */,
                    ),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).labelMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                          lineHeight: 1.5,
                        ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.workoutItem1Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    key: ValueKey('workout1'),
                    title: '0 - 2',
                    subTitle: FFLocalizations.of(context).getVariableText(
                      enText: '(Workouts now and then)',
                      heText: 'משתדל/ת להתאמן',
                    ),
                    isSelected: FFAppState().savedUserData.workoutFrequencyId == 1,
                    onTap: () async {
                      logFirebaseEvent('workout_item1_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..workoutFrequencyId = 1,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.oneStar,
                      isSelected: FFAppState().savedUserData.workoutFrequencyId == 1,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.workoutItem2Model1,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    key: ValueKey('workout2'),
                    title: '3 - 5',
                    subTitle: FFLocalizations.of(context).getVariableText(
                      enText: '(A few workouts per week)',
                      heText: 'שגרת אימונים קבועה',
                    ),
                    isSelected: FFAppState().savedUserData.workoutFrequencyId == 2,
                    onTap: () async {
                      logFirebaseEvent('workout_item2_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..workoutFrequencyId = 2,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.twoStar,
                      isSelected: FFAppState().savedUserData.workoutFrequencyId == 2,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.workoutItem3Model1,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    key: ValueKey('workout3'),
                    title: '+6',
                    subTitle: FFLocalizations.of(context).getVariableText(
                      enText: '(Dedicated athlete)',
                      heText: 'אימונים ברמה אינטנסיבית',
                    ),
                    isSelected: FFAppState().savedUserData.workoutFrequencyId == 3,
                    onTap: () async {
                      logFirebaseEvent('workout_item3_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..workoutFrequencyId = 3,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.threeStar,
                      isSelected: FFAppState().savedUserData.workoutFrequencyId == 3,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ].divide(SizedBox(height: 20.0)),
      ),
    );
  }
}

class FitnessGoalPage extends StatelessWidget {
  const FitnessGoalPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  FFLocalizations.of(context).getText(
                    'ozdo4o2r' /* What is your main fitness goal... */,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                        lineHeight: 1.2,
                      ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 6.0, 0.0, 0.0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      '2g4dfqia' /* this helps us a plan for your ... */,
                    ),
                    textAlign: TextAlign.center,
                    style: FlutterFlowTheme.of(context).labelMedium.override(
                          fontFamily: 'SFHebrew',
                          letterSpacing: 0.0,
                          lineHeight: 1.5,
                        ),
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.fitnessItem1Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Lose Weight',
                        heText: 'לרדת במשקל',
                      ),
                      'Lose Weight',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.fitnessGoalId == 1,
                    onTap: () async {
                      logFirebaseEvent('fitness_item1_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..fitnessGoalId = 1,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.loseWeight,
                      isSelected: FFAppState().savedUserData.fitnessGoalId == 1,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.workoutItem2Model2,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Tone Up',
                        heText: 'להתחטב',
                      ),
                      'Tone Up',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.fitnessGoalId == 2,
                    onTap: () async {
                      logFirebaseEvent('workout_item2_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..fitnessGoalId = 2,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.toneUp,
                      isSelected: FFAppState().savedUserData.fitnessGoalId == 2,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.workoutItem3Model2,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Gain Weight',
                        heText: 'לעלות במשקל',
                      ),
                      'Gain Weight',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.fitnessGoalId == 3,
                    onTap: () async {
                      logFirebaseEvent('workout_item3_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..fitnessGoalId = 3,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.gainWeight,
                      isSelected: FFAppState().savedUserData.fitnessGoalId == 3,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ].divide(SizedBox(height: 20.0)),
      ),
    );
  }
}

class BigGraphPage extends StatelessWidget {
  const BigGraphPage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Padding(
          padding: EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Text(
              //   FFLocalizations.of(context).getText(
              //     'r5zkg59j' /* CalCounty customers preserve feces for the long term! */,
              //   ),
              //   textAlign: TextAlign.center,
              //   style: FlutterFlowTheme.of(context).headlineSmall.override(
              //         fontFamily: 'SFHebrew',
              //         letterSpacing: 0.0,
              //         lineHeight: 1.2,
              //       ),
              // ),
              // ClipRRect(
              //   borderRadius: BorderRadius.circular(8.0),
              //   child: Image.asset(
              //     'assets/images/small_cir_he.png',
              //     width: 150.0,
              //     height: 160.0,
              //     fit: BoxFit.cover,
              //   ),
              // ),
              // ClipRRect(
              //   borderRadius: BorderRadius.circular(8.0),
              //   child: Image.asset(
              //     'assets/images/big_cir_he.png',
              //     width: 270.0,
              //     height: 250.0,
              //     fit: BoxFit.cover,
              //   ),
              // ),

              Padding(
                padding: const EdgeInsets.all(0),
                child: Image.asset(
                  isEnglish ? 'assets/images/big_graph_en.png' : 'assets/images/big_graph_he.png',
                  // width: MediaQuery.sizeOf(context).width *
                  //     0.7,
                  height: MediaQuery.sizeOf(context).height * 0.4,
                  fit: BoxFit.contain,
                ),
              )
            ].divide(SizedBox(height: 5.0)).addToStart(SizedBox(height: 10.0)),
          ),
        ),
      ],
    );
  }
}

class ChallengesToAchievePage extends StatelessWidget {
  const ChallengesToAchievePage(this.model, this.safeSetState, {super.key});

  final QuizModel model;
  final safeSetState;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  // "מה הכי מעכב אותך מלהשיג את המטרות שלך?",
                  FFLocalizations.of(context).getText(
                    'auyp6w9j' /* What's Holding You Back from A... */,
                  ),
                  textAlign: TextAlign.center,
                  style: FlutterFlowTheme.of(context).headlineSmall.override(
                        fontFamily: 'SFHebrew',
                        letterSpacing: 0.0,
                        lineHeight: 1.2,
                      ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.challengesItem1Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Lack of consistency',
                        heText: 'חוסר עקביות',
                      ),
                      'חוסר עקביות',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.challenges == 1,
                    onTap: () async {
                      logFirebaseEvent('challenges_item1_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..challenges = 1,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.lackConsistancy,
                      isSelected: FFAppState().savedUserData.challenges == 1,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.challengesItem2Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Unhealthy eating habits',
                        heText: 'הרגלי אכילה לא בריאים',
                      ),
                      'Unhealthy eating habits',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.challenges == 2,
                    onTap: () async {
                      logFirebaseEvent('challenges_item2_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..challenges = 2,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.unhealthyHabit,
                      isSelected: FFAppState().savedUserData.challenges == 2,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.challengesItem3Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Lack of knowledge',
                        heText: 'חוסר ידע',
                      ),
                      'Lack of knowledge',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.challenges == 3,
                    onTap: () async {
                      logFirebaseEvent('challenges_item3_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..challenges = 3,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.lackSupport,
                      isSelected: FFAppState().savedUserData.challenges == 3,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.challengesItem4Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Busy schedule',
                        heText: 'לו\"ז עמוס',
                      ),
                      'Busy schedule',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.challenges == 4,
                    onTap: () async {
                      logFirebaseEvent('challenges_item4_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..challenges = 4,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.busySchedule,
                      isSelected: FFAppState().savedUserData.challenges == 4,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                child: wrapWithModel(
                  model: model.challengesItem5Model,
                  updateCallback: () => safeSetState(() {}),
                  child: SelectableItemWidget(
                    title: valueOrDefault<String>(
                      FFLocalizations.of(context).getVariableText(
                        enText: 'Increase my energy and motivation',
                        heText: 'חוסר באנרגיה או במוטיבציה',
                      ),
                      'Increase my energy and motivation',
                    ),
                    subTitle: '',
                    isSelected: FFAppState().savedUserData.challenges == 5,
                    onTap: () async {
                      logFirebaseEvent('challenges_item5_update_app_state');
                      FFAppState().updateSavedUserDataStruct(
                        (e) => e..challenges = 5,
                      );
                      safeSetState(() {});
                    },
                    imagePath: () => CenterImageWidget(
                      imageType: ImageType.lackMotivation,
                      isSelected: FFAppState().savedUserData.challenges == 5,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ].divide(SizedBox(height: 20.0)),
      ),
    );
  }
}

class LoginSignUpPage extends StatelessWidget {
  const LoginSignUpPage(this.model, this.safeSetState, this.animationsMap, this.isForSignIn, {super.key});

  final QuizModel model;
  final safeSetState;
  final Map<String, AnimationInfo> animationsMap;
  final bool isForSignIn;

  // Add validation function for quiz data
  bool isQuizDataComplete() {
    final data = FFAppState().savedUserData;
    return data.heightValue != 0 &&
        data.heightUnit != null &&
        data.heightUnit != '' &&
        data.weightValue != 0 &&
        data.weightUnit != null &&
        data.weightUnit != '' &&
        data.dob != null &&
        data.dob != '' &&
        data.gender != null &&
        data.gender != '' &&
        data.fitnessGoalId != 0 &&
        data.workoutFrequencyId != 0 &&
        data.targetWeightValue != 0 &&
        data.targetWeightUnit != null &&
        data.targetWeightUnit != '' &&
        data.challenges != 0 &&
        data.accomplishmentId != 0;
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            width: double.infinity,
            height: 200.0,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  FlutterFlowTheme.of(context).primary,
                  FlutterFlowTheme.of(context).secondaryText,
                  FlutterFlowTheme.of(context).alternate
                ],
                stops: [0.0, 0.5, 1.0],
                begin: AlignmentDirectional(-1.0, -1.0),
                end: AlignmentDirectional(1.0, 1.0),
              ),
            ),
            child: Container(
              width: 100.0,
              height: 80.0,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0x00FFFFFF), FlutterFlowTheme.of(context).secondaryBackground],
                  stops: [0.0, 1.0],
                  begin: AlignmentDirectional(0.0, -1.0),
                  end: AlignmentDirectional(0, 1.0),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Align(
                    alignment: AlignmentDirectional(-1.0, 0.0),
                    child: FlutterFlowIconButton(
                      buttonSize: 45.0,
                      icon: Icon(
                        Icons.chevron_left_outlined,
                        color: FlutterFlowTheme.of(context).info,
                        size: 30.0,
                      ),
                      onPressed: () async {
                        logFirebaseEvent('IconButton_navigate_back');
                        context.safePop();
                      },
                    ),
                  ),
                  Container(
                    width: 100.0,
                    height: 100.0,
                    decoration: BoxDecoration(
                      color: FlutterFlowTheme.of(context).primary,
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: Image.asset(
                          'assets/images/AppIcon.png',
                        ).image,
                      ),
                      borderRadius: BorderRadius.circular(16.0),
                    ),
                  ).animateOnPageLoad(animationsMap['containerOnPageLoadAnimation']!),
                ],
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional(0.0, 0.0),
            child: Container(
              width: MediaQuery.sizeOf(context).width * 0.75,
              decoration: BoxDecoration(),
              alignment: AlignmentDirectional(0.0, 0.0),
              child: wrapWithModel(
                model: model.segmentedModel2,
                updateCallback: () => safeSetState(() {}),
                child: SegmentedWidget(
                  initialValue:
                      isForSignIn ? (isEnglish ? 'Log In' : 'התחברות') : (isEnglish ? 'Create Account' : 'צור חשבון'),
                  segmentItems: isEnglish ? ["Create Account", "Log In"] : ["צור חשבון", "התחברות"],
                  onSelect: (title) async {
                    logFirebaseEvent('segmented_update_page_state');
                    model.forSignUp = (title != null && title != '') &&
                        (isEnglish ? title == 'Create Account' : title == 'צור חשבון');
                    safeSetState(() {});
                  },
                ),
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional(0.0, 0.0),
            child: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 16.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      child: TextFormField(
                        controller: model.emailAddressTextController,
                        focusNode: model.emailAddressFocusNode,
                        autofocus: false,
                        autofillHints: [AutofillHints.email],
                        obscureText: false,
                        decoration: InputDecoration(
                          isDense: false,
                          labelText: FFLocalizations.of(context).getText(
                            'q9n16tk0' /* Email */,
                          ),
                          labelStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                fontFamily: 'SFHebrew',
                                letterSpacing: 0.0,
                              ),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).secondaryText,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).primary,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).error,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).error,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          filled: true,
                          fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                          contentPadding: EdgeInsets.all(24.0),
                        ),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'SFHebrew',
                              letterSpacing: 0.0,
                            ),
                        keyboardType: TextInputType.emailAddress,
                        cursorColor: FlutterFlowTheme.of(context).primary,
                        validator: model.emailAddressTextControllerValidator.asValidator(context),
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      child: TextFormField(
                        controller: model.passwordTextController,
                        focusNode: model.passwordFocusNode,
                        autofocus: false,
                        autofillHints: [AutofillHints.password],
                        obscureText: !model.passwordVisibility,
                        decoration: InputDecoration(
                          labelText: FFLocalizations.of(context).getText(
                            'qdsd58cf' /* Password */,
                          ),
                          labelStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                fontFamily: 'SFHebrew',
                                letterSpacing: 0.0,
                              ),
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).secondaryText,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).primary,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).error,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).error,
                              width: 1.5,
                            ),
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          filled: true,
                          fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                          contentPadding: EdgeInsets.all(24.0),
                          suffixIcon: InkWell(
                            onTap: () => safeSetState(
                              () => model.passwordVisibility = !model.passwordVisibility,
                            ),
                            focusNode: FocusNode(skipTraversal: true),
                            child: Icon(
                              model.passwordVisibility ? Icons.visibility_outlined : Icons.visibility_off_outlined,
                              color: FlutterFlowTheme.of(context).secondaryText,
                              size: 24.0,
                            ),
                          ),
                        ),
                        style: FlutterFlowTheme.of(context).bodyMedium.override(
                              fontFamily: 'SFHebrew',
                              letterSpacing: 0.0,
                            ),
                        validator: model.passwordTextControllerValidator.asValidator(context),
                      ),
                    ),
                  ),
                  if (model.forSignUp)
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
                      child: Container(
                        width: double.infinity,
                        child: TextFormField(
                          controller: model.confirmPasswordTextController,
                          focusNode: model.confirmPasswordFocusNode,
                          autofocus: false,
                          autofillHints: [AutofillHints.password],
                          obscureText: !model.confirmPasswordVisibility,
                          decoration: InputDecoration(
                            labelText: FFLocalizations.of(context).getText(
                              'so5nufkf' /* Confirm Password */,
                            ),
                            labelStyle: FlutterFlowTheme.of(context).labelMedium.override(
                                  fontFamily: 'SFHebrew',
                                  letterSpacing: 0.0,
                                ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).secondaryText,
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).primary,
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).error,
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            focusedErrorBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: FlutterFlowTheme.of(context).error,
                                width: 1.5,
                              ),
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            filled: true,
                            fillColor: FlutterFlowTheme.of(context).secondaryBackground,
                            contentPadding: EdgeInsets.all(24.0),
                            suffixIcon: InkWell(
                              onTap: () => safeSetState(
                                () => model.confirmPasswordVisibility = !model.confirmPasswordVisibility,
                              ),
                              focusNode: FocusNode(skipTraversal: true),
                              child: Icon(
                                model.confirmPasswordVisibility
                                    ? Icons.visibility_outlined
                                    : Icons.visibility_off_outlined,
                                color: FlutterFlowTheme.of(context).secondaryText,
                                size: 24.0,
                              ),
                            ),
                          ),
                          style: FlutterFlowTheme.of(context).bodyMedium.override(
                                fontFamily: 'SFHebrew',
                                letterSpacing: 0.0,
                              ),
                          validator: model.confirmPasswordTextControllerValidator.asValidator(context),
                        ),
                      ),
                    ),
                  instructionContentWidget(context),
                  loginSignUpButtonWidget(isEnglish),
                  forgotPasswordWidget(context),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Align(
                        alignment: AlignmentDirectional(0.0, 0.0),
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
                          child: Column(
                            spacing: 8.0,
                            // runSpacing: 0.0,
                            // alignment: WrapAlignment.center,
                            // crossAxisAlignment: WrapCrossAlignment.center,
                            // direction: Axis.horizontal,
                            // runAlignment: WrapAlignment.center,
                            // verticalDirection: VerticalDirection.down,
                            // clipBehavior: Clip.none,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              googleSignInWidget(context, isEnglish),
                              if (Platform.isIOS) appleSignInWidget(context, isEnglish),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ).animateOnPageLoad(animationsMap['columnOnPageLoadAnimation']!),
            ),
          ),
        ],
      ),
    );
  }

  // Widget googleSignInWidget(BuildContext context) {
  //   return Padding(
  //     padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
  //     child: FFButtonWidget(
  //       onPressed: () async {
  //         await googleSignInCall(context);
  //       },
  //       text: FFLocalizations.of(context).getText(
  //         'stf4v9gj' /* Google */,
  //       ),
  //       icon: Container(
  //         height: 28,
  //         width: 28,
  //         decoration: BoxDecoration(
  //           color: FlutterFlowTheme.of(context).secondaryBackground,
  //           image: DecorationImage(
  //             fit: BoxFit.cover,
  //             image: Image.asset(
  //               'assets/images/google_logo.png',
  //             ).image,
  //           ),
  //         ),
  //       ),
  //       // child: Image.asset('assets/images/google_logo.png')),
  //       // FaIcon(
  //       //   FontAwesomeIcons.google,
  //       //   size: 20.0,
  //       // ),
  //       options: FFButtonOptions(
  //         width: 130.0,
  //         height: 44.0,
  //         padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
  //         iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
  //         color: FlutterFlowTheme.of(context).secondaryBackground,
  //         textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
  //               fontFamily: 'SFHebrew',
  //               letterSpacing: 0.0,
  //               fontWeight: FontWeight.bold,
  //             ),
  //         elevation: 0.0,
  //         borderSide: BorderSide(
  //           color: FlutterFlowTheme.of(context).alternate,
  //           width: 2.0,
  //         ),
  //         borderRadius: BorderRadius.circular(12.0),
  //         hoverColor: FlutterFlowTheme.of(context).primaryBackground,
  //       ),
  //     ),
  //   );
  // }

  Widget googleSignInWidget(BuildContext context, bool isEnglish) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
      child: FFButtonWidget(
        onPressed: () async {
          await googleSignInCall(context, isEnglish);
        },
        text: FFLocalizations.of(context).getText(
          'stf4v9gj' /* Google */,
        ),
        icon: Container(
          height: 20,
          width: 20,
          decoration: BoxDecoration(
            color: Colors.transparent,
            // color: FlutterFlowTheme.of(context).secondaryBackground,
            image: DecorationImage(
              fit: BoxFit.cover,
              image: Image.asset(
                'assets/images/google_logo.png',
              ).image,
            ),
          ),
        ),
        options: FFButtonOptions(
          width: 170.0,
          height: 44.0,
          padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
          iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
          color: Colors.black,
          textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                fontFamily: 'SFHebrew',
                letterSpacing: 0.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
          elevation: 0.0,
          borderSide: BorderSide(
            color: Colors.black,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(6.0),
          hoverColor: Colors.black87,
        ),
      ),
    );
  }

  Widget appleSignInWidget(BuildContext context, bool isEnglish) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
      child: FFButtonWidget(
        onPressed: () async {
          await appleSignInCall(context, isEnglish);
        },
        text: FFLocalizations.of(context).getText(
          '8rolxrnc' /* Continue with Apple */,
        ),
        icon: FaIcon(
          FontAwesomeIcons.apple,
          size: 20.0,
          color: Colors.white,
        ),
        options: FFButtonOptions(
          width: 170.0,
          height: 44.0,
          padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
          iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
          color: Colors.black,
          textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                fontFamily: 'SFHebrew',
                letterSpacing: 0.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
          elevation: 0.0,
          borderSide: BorderSide(
            color: Colors.black,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(6.0),
          hoverColor: Colors.black87,
        ),
      ),
    );
  }

  Widget instructionContentWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 10.0),
      child: RichText(
        textScaler: MediaQuery.of(context).textScaler,
        text: TextSpan(
          children: [
            TextSpan(
              text: FFLocalizations.of(context).getText(
                '6t0xtyea' /* By clicking "Continue", you ag... */,
              ),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    letterSpacing: 0.0,
                  ),
            ),
            TextSpan(
              text: FFLocalizations.of(context).getText(
                '8ouvs4dg' /* Terms  */,
              ),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    letterSpacing: 0.0,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.underline,
                  ),
              mouseCursor: SystemMouseCursors.click,
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  logFirebaseEvent('RichTextSpan_navigate_to');

                  await openTeams(context);
                },
            ),
            TextSpan(
              text: FFLocalizations.of(context).getText(
                'kf5xst85' /* and have read our  */,
              ),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    letterSpacing: 0.0,
                  ),
            ),
            TextSpan(
              text: FFLocalizations.of(context).getText(
                'ksc28buv' /* Privacy Policy */,
              ),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    color: FlutterFlowTheme.of(context).primary,
                    letterSpacing: 0.0,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.underline,
                  ),
              mouseCursor: SystemMouseCursors.click,
              recognizer: TapGestureRecognizer()
                ..onTap = () async {
                  logFirebaseEvent('RichTextSpan_navigate_to');

                  await openPrivacy(context);
                },
            ),
            TextSpan(
              text: FFLocalizations.of(context).getText(
                'k1jw3wg1' /* . */,
              ),
              style: FlutterFlowTheme.of(context).bodyMedium.override(
                    fontFamily: 'SFHebrew',
                    letterSpacing: 0.0,
                  ),
            )
          ],
          style: FlutterFlowTheme.of(context).bodyMedium.override(
                fontFamily: 'SFHebrew',
                letterSpacing: 0.0,
              ),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Future<void> openTeams(BuildContext context) async {
    // await launchUrl(
    //   Uri.parse(appTerms),
    //   mode: LaunchMode.inAppWebView,
    // );
    await context.pushNamed(
      'teams_privacy',
      queryParameters: {
        'privacy': serializeParam(
          false,
          ParamType.bool,
        ),
        'url': serializeParam(
          appTerms,
          ParamType.String,
        ),
      }.withoutNulls,
    );
  }

  Future<void> openPrivacy(BuildContext context) async {
    // await launchUrl(
    //   Uri.parse(appPrivacy),
    //   mode: LaunchMode.inAppWebView,
    // );
    await context.pushNamed(
      'teams_privacy',
      queryParameters: {
        'privacy': serializeParam(
          true,
          ParamType.bool,
        ),
        'url': serializeParam(
          appPrivacy,
          ParamType.String,
        ),
      }.withoutNulls,
    );
  }

  Future<void> googleSignInCall(BuildContext context, bool isEnglish) async {
    var _shouldSetState = false;
    logFirebaseEvent('Button_auth');
    GoRouter.of(context).prepareAuthEvent();
    final user = await authManager.signInWithGoogle(context);
    if (user == null) {
      return;
    }
    logFirebaseEvent('Button_backend_call');
    model.userDataGoogle = await UsersRecord.getDocumentOnce(currentUserReference!);
    _shouldSetState = true;
    if (model.userDataGoogle!.hasCompletedOnboarding && model.forSignUp) {
      logFirebaseEvent('Button_show_snack_bar');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish
                ? model.userDataGoogle!.hasCompletedOnboarding
                    ? 'Your account already exists. Please choose login from the options above.'
                    : 'Complete all mandatory fields by going back to the onboarding process.'
                : model.userDataGoogle!.hasCompletedOnboarding
                    ? 'החשבון שלך כבר קיים, אנא בחר התחברות מהאפשרויות למעלה.'
                    : 'השלם את כל השדות החיוניים על ידי חזרה לתהליך ההרשמה.',
            style: FlutterFlowTheme.of(context).labelMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).alternate,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 4000),
          backgroundColor: FlutterFlowTheme.of(context).primary,
        ),
      );
      logFirebaseEvent('Button_auth');
      GoRouter.of(context).prepareAuthEvent();
      await authManager.signOut();
      GoRouter.of(context).clearRedirectLocation();

      if (_shouldSetState) safeSetState(() {});
      return;
    }
    logFirebaseEvent('Button_custom_action');
    model.tokenGoogle = await actions.getFCMToken();
    _shouldSetState = true;
    if (loggedIn) {
      // Quiz data validation for Google sign-in
      if (model.forSignUp && !isQuizDataComplete()) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEnglish
                  ? 'Please complete all required information before creating an account.'
                  : 'אנא השלם את כל המידע הנדרש לפני יצירת החשבון.',
              style: FlutterFlowTheme.of(context).titleSmall.override(
                    fontFamily: 'SFHebrew',
                    color: FlutterFlowTheme.of(context).alternate,
                    letterSpacing: 0.0,
                  ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: Color(0xFF000000),
          ),
        );
        model.pageViewController?.jumpToPage(0);
        await authManager.signOut();
        GoRouter.of(context).clearRedirectLocation();
        if (_shouldSetState) safeSetState(() {});
        return;
      }
      FFAppState().updateSavedUserDataStruct(
        (e) => e
          ..email = currentUserEmail
          ..signupBy = 'google'
          ..firebaseId = currentUserUid
          ..deviceId = model.deviceId
          ..googleId = currentUserUid
          ..fcmToken = model.tokenGoogle
          ..username = functions.getUsernameFromEmail(currentUserEmail),
      );
      logFirebaseEvent('Button_backend_call');
      model.apiResultGoogleUser = await SignUpCall.call(
        jsonJson: functions.userDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      logFirebaseEvent('Button_backend_call');
      model.apiResGoogleOnboarding = await OnboardingCall.call(
        jsonJson: functions.userDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      if ((model.apiResultGoogleUser?.succeeded ?? true)) {
        logFirebaseEvent('Button_update_app_state');
        FFAppState().savedUserData = SignUpCall.data(
          (model.apiResultGoogleUser?.jsonBody ?? ''),
        )!;
        FFAppState().authToken = SignUpCall.accessToken(
          (model.apiResultGoogleUser?.jsonBody ?? ''),
        )!;
        logFirebaseEvent('Button_backend_call');

        String languageCode = 'he';
        // TODO: change language code
        // if ((model.userDataGoogle?.languageCode != null &&
        //         model.userDataGoogle?.languageCode != '') &&
        //     (model.userDataGoogle?.languageCode == 'he')) {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'he';
        // } else {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'en';
        // }

        await currentUserReference!.update({
          ...createUsersRecordData(
            hasCompletedOnboarding: true,
            displayName: functions.getUsernameFromEmail(currentUserEmail),
            languageCode: languageCode,
          ),
          ...mapToFirestore(
            {
              'created_time': FieldValue.serverTimestamp(),
            },
          ),
        });
        logFirebaseEvent('Button_wait__delay');
        await Future.delayed(const Duration(milliseconds: 100));

        setAppLanguage(context, languageCode);

        logFirebaseEvent('Button_navigate_to');

        context.pushNamedAuth('dashboard', context.mounted);
      } else {
        logFirebaseEvent('Button_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              (model.apiResultGoogleUser?.exceptionMessage ?? ''),
              style: TextStyle(
                color: FlutterFlowTheme.of(context).error,
                fontFamily: 'SFHebrew',
              ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: FlutterFlowTheme.of(context).primary,
          ),
        );
      }
    } else {
      if (_shouldSetState) safeSetState(() {});
      return;
    }

    if (_shouldSetState) safeSetState(() {});
  }

  Widget forgotPasswordWidget(BuildContext context) {
    return Align(
      alignment: AlignmentDirectional(0.0, 0.0),
      child: Padding(
        padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
        child: FFButtonWidget(
          onPressed: () async {
            logFirebaseEvent('Button_navigate_to');

            context.pushNamed('forgot');
          },
          text: FFLocalizations.of(context).getText(
            '6x52bpsk' /* Forgot Password */,
          ),
          options: FFButtonOptions(
            width: 230.0,
            height: 35.0,
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
            iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
            color: FlutterFlowTheme.of(context).secondaryBackground,
            textStyle: FlutterFlowTheme.of(context).bodyMedium.override(
                  fontFamily: 'SFHebrew',
                  letterSpacing: 0.0,
                ),
            elevation: 0.0,
            borderSide: BorderSide(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              width: 2.0,
            ),
            borderRadius: BorderRadius.circular(12.0),
          ),
        ),
      ),
    );
  }

  Widget loginSignUpButtonWidget(bool isEnglish) {
    return Align(
      alignment: AlignmentDirectional(0.0, 0.0),
      child: Builder(
        builder: (context) {
          if (model.forSignUp) {
            return Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
              child: FFButtonWidget(
                onPressed: () async {
                  await signUpCall(context, isEnglish);
                },
                text: FFLocalizations.of(context).getText(
                  'e1wxpgge' /* Sign Up */,
                ),
                options: FFButtonOptions(
                  width: 230.0,
                  height: 50.0,
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  color: FlutterFlowTheme.of(context).primary,
                  textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                        fontFamily: 'SFHebrew',
                        color: Colors.white,
                        letterSpacing: 0.0,
                      ),
                  elevation: 3.0,
                  borderSide: BorderSide(
                    color: Colors.transparent,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(12.0),
                ),
              ),
            );
          } else {
            return Padding(
              padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 16.0),
              child: FFButtonWidget(
                onPressed: () async {
                  await loginCall(context);
                },
                text: FFLocalizations.of(context).getText(
                  'nkyxyohr' /* Login */,
                ),
                options: FFButtonOptions(
                  width: 230.0,
                  height: 50.0,
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
                  color: FlutterFlowTheme.of(context).primary,
                  textStyle: FlutterFlowTheme.of(context).titleSmall.override(
                        fontFamily: 'SFHebrew',
                        color: Colors.white,
                        letterSpacing: 0.0,
                      ),
                  elevation: 3.0,
                  borderSide: BorderSide(
                    color: Colors.transparent,
                    width: 1.0,
                  ),
                  borderRadius: BorderRadius.circular(12.0),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  Future<void> signUpCall(BuildContext context, bool isEnglish) async {
    var _shouldSetState = false;
    // Validate quiz data before proceeding
    if (!isQuizDataComplete()) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish
                ? 'Please complete all required information in the quiz before creating an account.'
                : 'אנא השלם את כל המידע הנדרש בחידון לפני יצירת חשבון.',
            style: FlutterFlowTheme.of(context).titleSmall.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).alternate,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).primary,
        ),
      );
      model.pageViewController?.jumpToPage(0);
      return;
    }

    if ((model.emailAddressTextController.text != null && model.emailAddressTextController.text != '') &&
        (model.passwordTextController.text != null && model.passwordTextController.text != '')) {
      logFirebaseEvent('Button_wait__delay');
      await Future.delayed(const Duration(milliseconds: 0));
      if (model.passwordTextController.text != model.confirmPasswordTextController.text) {
        logFirebaseEvent('Button_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEnglish ? 'Password and confirm password must be the same.' : 'הסיסמה ואישור הסיסמה חייבים להיות זהים.',
              style: FlutterFlowTheme.of(context).titleSmall.override(
                    fontFamily: 'SFHebrew',
                    color: FlutterFlowTheme.of(context).alternate,
                    letterSpacing: 0.0,
                  ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: FlutterFlowTheme.of(context).primary,
          ),
        );
        return;
      }
    } else {
      return;
    }

    logFirebaseEvent('Button_auth');
    GoRouter.of(context).prepareAuthEvent();
    if (model.passwordTextController.text != model.confirmPasswordTextController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish ? 'Passwords don\'t match!' : 'הסיסמאות אינן תואמות!',
          ),
        ),
      );
      return;
    }

    final user = await authManager.createAccountWithEmail(
      context,
      model.emailAddressTextController.text,
      model.passwordTextController.text,
    );
    if (user == null) {
      return;
    }

    await UsersRecord.collection.doc(user.uid).update({
      ...mapToFirestore(
        {
          'created_time': FieldValue.serverTimestamp(),
        },
      ),
    });

    logFirebaseEvent('Button_custom_action');
    model.tokenSU = await actions.getFCMToken();
    _shouldSetState = true;
    if (loggedIn) {
      logFirebaseEvent('Button_update_app_state');
      FFAppState().updateSavedUserDataStruct(
        (e) => e
          ..email = model.emailAddressTextController.text
          ..signupBy = 'email'
          ..firebaseId = currentUserUid
          ..deviceId = model.deviceId
          ..fcmToken = model.tokenSU
          ..username = functions.getUsernameFromEmail(model.emailAddressTextController.text),
      );
      logFirebaseEvent('Button_backend_call');
      model.newUserEmailSignUp = await SignUpCall.call(
        jsonJson: functions.createuserDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      logFirebaseEvent('Button_backend_call');
      model.newUserEmailSignUpOnboarding = await OnboardingCall.call(
        jsonJson: functions.onBoardDataToAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      if ((model.newUserEmailSignUpOnboarding?.succeeded ?? true)) {
        logFirebaseEvent('Button_backend_call');

        // TODO: change language code
        setAppLanguage(context, 'he');
        // context, FFLocalizations.getStoredLocale()?.languageCode ?? 'he');
        await currentUserReference!.update(createUsersRecordData(
          hasCompletedOnboarding: true,
          languageCode: 'he',
          displayName: functions.getUsernameFromEmail(currentUserEmail),
        ));
        logFirebaseEvent('Button_update_app_state');
        FFAppState().authToken = SignUpCall.accessToken(
          (model.newUserEmailSignUp?.jsonBody ?? ''),
        )!;
        FFAppState().savedUserData = OnboardingCall.data(
          (model.newUserEmailSignUpOnboarding?.jsonBody ?? ''),
        )!;
        logFirebaseEvent('Button_wait__delay');
        await Future.delayed(const Duration(milliseconds: 100));
        logFirebaseEvent('Button_navigate_to');

        context.goNamedAuth(
          'dashboard',
          context.mounted,
          extra: <String, dynamic>{
            kTransitionInfoKey: TransitionInfo(
              hasTransition: true,
              transitionType: PageTransitionType.rightToLeft,
            ),
          },
        );
      } else {
        logFirebaseEvent('Button_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              (model.newUserEmailSignUp?.exceptionMessage ?? ''),
              style: TextStyle(
                color: FlutterFlowTheme.of(context).error,
                fontFamily: 'SFHebrew',
              ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: FlutterFlowTheme.of(context).primary,
          ),
        );
      }
    } else {
      logFirebaseEvent('Button_show_snack_bar');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish ? 'Is logged-in user getting false?' : 'האם המשתמש המחובר מקבל ערך False?',
            style: TextStyle(
              color: FlutterFlowTheme.of(context).alternate,
              fontFamily: 'SFHebrew',
            ),
          ),
          duration: Duration(milliseconds: 4000),
          backgroundColor: FlutterFlowTheme.of(context).tertiary,
        ),
      );
    }

    if (_shouldSetState) safeSetState(() {});
  }

  Future<void> loginCall(BuildContext context) async {
    var _shouldSetState = false;
    if ((model.emailAddressTextController.text != null && model.emailAddressTextController.text != '') &&
        (model.passwordTextController.text != null && model.passwordTextController.text != '')) {
      logFirebaseEvent('Button_wait__delay');
      await Future.delayed(const Duration(milliseconds: 0));
    } else {
      return;
    }

    logFirebaseEvent('Button_auth');
    GoRouter.of(context).prepareAuthEvent();

    final user = await authManager.signInWithEmail(
      context,
      model.emailAddressTextController.text,
      model.passwordTextController.text,
    );
    if (user == null) {
      return;
    }

    logFirebaseEvent('Button_custom_action');
    model.token = await actions.getFCMToken();
    _shouldSetState = true;
    if (loggedIn) {
      logFirebaseEvent('Button_update_app_state');
      FFAppState().updateSavedUserDataStruct(
        (e) => e
          ..email = model.emailAddressTextController.text
          ..signupBy = 'email'
          ..firebaseId = currentUserUid
          ..deviceId = model.deviceId
          ..fcmToken = model.token
          ..username = functions.getUsernameFromEmail(model.emailAddressTextController.text),
      );
      logFirebaseEvent('Button_backend_call');
      model.apiResultUserEmailLogin = await SignUpCall.call(
        jsonJson: functions.createuserDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      if ((model.apiResultUserEmailLogin?.succeeded ?? true)) {
        logFirebaseEvent('Button_update_app_state');
        FFAppState().authToken = SignUpCall.accessToken(
          (model.apiResultUserEmailLogin?.jsonBody ?? ''),
        )!;
        FFAppState().savedUserData = SignUpCall.data(
          (model.apiResultUserEmailLogin?.jsonBody ?? ''),
        )!;
        // Defensive check: If height or weight is 0, fetch latest profile from backend
        if (FFAppState().savedUserData.heightValue == 0 || FFAppState().savedUserData.weightValue == 0) {
          final userInfoResponse = await UserInfoCall.call(
            accessToken: FFAppState().authToken,
          );
          final userInfo = UserInfoCall.data(userInfoResponse.jsonBody);
          if (userInfo != null) {
            FFAppState().savedUserData = userInfo;
            print("Height : ${FFAppState().savedUserData.heightValue}");
            print("Weight : ${FFAppState().savedUserData.weightValue}");
          }
        }
        logFirebaseEvent('Button_wait__delay');
        await Future.delayed(const Duration(milliseconds: 100));
        logFirebaseEvent('Button_backend_call');
        model.loginUserD = await UsersRecord.getDocumentOnce(currentUserReference!);
        _shouldSetState = true;
        String languageCode = 'he';
        // TODO: change language code
        // if ((model.loginUserD?.languageCode != null &&
        //         model.loginUserD?.languageCode != '') &&
        //     (model.loginUserD?.languageCode == 'he')) {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'he';
        // } else {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'en';
        // }

        await currentUserReference!.update(createUsersRecordData(
          languageCode: languageCode,
        ));

        logFirebaseEvent('Button_navigate_to');
        setAppLanguage(context, languageCode);
        context.goNamedAuth(
          'dashboard',
          context.mounted,
          extra: <String, dynamic>{
            kTransitionInfoKey: TransitionInfo(
              hasTransition: true,
              transitionType: PageTransitionType.rightToLeft,
            ),
          },
        );
      } else {
        logFirebaseEvent('Button_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              (model.apiResultUserEmailLogin?.exceptionMessage ?? ''),
              style: TextStyle(
                color: FlutterFlowTheme.of(context).error,
                fontFamily: 'SFHebrew',
              ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: FlutterFlowTheme.of(context).primary,
          ),
        );
      }
    } else {
      if (_shouldSetState) safeSetState(() {});
      return;
    }

    if (_shouldSetState) safeSetState(() {});
  }

  Future<void> appleSignInCall(BuildContext context, bool isEnglish) async {
    var _shouldSetState = false;
    logFirebaseEvent('Button_auth');
    GoRouter.of(context).prepareAuthEvent();
    final user = await authManager.signInWithApple(context);
    if (user == null) {
      return;
    }
    // Add quiz data completeness check for Apple sign-up (not login)
    if (model.forSignUp && !isQuizDataComplete()) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish
                ? 'Please complete all required information before creating an account.'
                : 'אנא השלם את כל המידע הנדרש לפני יצירת החשבון.',
            style: FlutterFlowTheme.of(context).titleSmall.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).alternate,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 3000),
          backgroundColor: FlutterFlowTheme.of(context).primary,
        ),
      );
      model.pageViewController?.jumpToPage(0);
      await authManager.signOut();
      GoRouter.of(context).clearRedirectLocation();
      return;
    }
    logFirebaseEvent('Button_backend_call');
    model.userDataApple = await UsersRecord.getDocumentOnce(currentUserReference!);
    _shouldSetState = true;
    if (valueOrDefault<bool>(currentUserDocument?.hasCompletedOnboarding, false) && model.forSignUp) {
      logFirebaseEvent('Button_show_snack_bar');
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isEnglish
                ? 'Please complete all required information in the quiz before creating an account.'
                : 'אנא השלם את כל המידע הנדרש בחידון לפני יצירת חשבון.',
            style: FlutterFlowTheme.of(context).labelMedium.override(
                  fontFamily: 'SFHebrew',
                  color: FlutterFlowTheme.of(context).error,
                  letterSpacing: 0.0,
                ),
          ),
          duration: Duration(milliseconds: 4000),
          backgroundColor: FlutterFlowTheme.of(context).primary,
        ),
      );
      logFirebaseEvent('Button_auth');
      GoRouter.of(context).prepareAuthEvent();
      await authManager.signOut();
      GoRouter.of(context).clearRedirectLocation();

      if (_shouldSetState) safeSetState(() {});
      return;
    }
    logFirebaseEvent('Button_custom_action');
    model.tokenApple = await actions.getFCMToken();
    _shouldSetState = true;
    if (loggedIn) {
      logFirebaseEvent('Button_update_app_state');
      FFAppState().updateSavedUserDataStruct(
        (e) => e
          ..email = currentUserEmail
          ..signupBy = 'apple'
          ..firebaseId = currentUserUid
          ..deviceId = model.deviceId
          ..appleId = currentUserUid
          ..fcmToken = model.tokenApple
          ..username = functions.getUsernameFromEmail(currentUserEmail),
      );
      logFirebaseEvent('Button_backend_call');
      model.apiResultNewUserApple = await SignUpCall.call(
        jsonJson: functions.userDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      logFirebaseEvent('Button_backend_call');
      model.apiResOnboardingApple = await OnboardingCall.call(
        jsonJson: functions.userDatatoAPIJson(FFAppState().savedUserData),
      );

      _shouldSetState = true;
      if ((model.apiResultNewUserApple?.succeeded ?? true)) {
        logFirebaseEvent('Button_update_app_state');
        FFAppState().savedUserData = SignUpCall.data(
          (model.apiResultNewUserApple?.jsonBody ?? ''),
        )!;
        FFAppState().authToken = SignUpCall.accessToken(
          (model.apiResultNewUserApple?.jsonBody ?? ''),
        )!;
        logFirebaseEvent('Button_backend_call');

        await currentUserReference!.update({
          ...createUsersRecordData(
            hasCompletedOnboarding: true,
            displayName: functions.getUsernameFromEmail(currentUserEmail),
          ),
          ...mapToFirestore(
            {
              'created_time': FieldValue.serverTimestamp(),
            },
          ),
        });
        logFirebaseEvent('Button_wait__delay');
        await Future.delayed(const Duration(milliseconds: 100));
        String languageCode = 'he';
        // TODO: change language code
        // if ((model.userDataApple?.languageCode != null &&
        //         model.userDataApple?.languageCode != '') &&
        //     (model.userDataApple?.languageCode == 'he')) {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'he';
        // } else {
        //   logFirebaseEvent('Button_set_app_language');
        //   languageCode = 'en';
        // }

        logFirebaseEvent('Button_navigate_to');
        await currentUserReference!.update(createUsersRecordData(
          languageCode: languageCode,
        ));
        setAppLanguage(context, languageCode);
        context.pushNamedAuth('dashboard', context.mounted);
      } else {
        logFirebaseEvent('Button_show_snack_bar');
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              (model.apiResultNewUserApple?.exceptionMessage ?? ''),
              style: TextStyle(
                color: FlutterFlowTheme.of(context).error,
                fontFamily: 'SFHebrew',
              ),
            ),
            duration: Duration(milliseconds: 3000),
            backgroundColor: FlutterFlowTheme.of(context).primary,
          ),
        );
      }
    } else {
      if (_shouldSetState) safeSetState(() {});
      return;
    }

    if (_shouldSetState) safeSetState(() {});
  }
}

// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';
import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dart:developer' as developer;

class MealItemsStruct extends FFFirebaseStruct {
  MealItemsStruct({
    String? id,
    String? name,
    String? quantity,
    int? calories,
    int? fats,
    int? proteins,
    int? carbs,
    List<String>? spices,
    bool? expanded,
    bool? isEnable,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _id = id,
        _name = name,
        _quantity = quantity,
        _calories = calories,
        _fats = fats,
        _proteins = proteins,
        _carbs = carbs,
        _spices = spices,
        _expanded = expanded,
        _isEnable = isEnable,
        super(firestoreUtilData);

  // "id" field.
  String? _id;

  String get id => _id ?? '';

  set id(String? val) => _id = val;

  bool hasId() => _id != null;

  // "name" field.
  String? _name;

  String get name => _name ?? '';

  set name(String? val) => _name = val;

  bool hasName() => _name != null;

  // "quantity" field.
  String? _quantity;

  String get quantity => _quantity ?? '';
  set quantity(String? val) => _quantity = val;
  bool hasQuantity() => _quantity != null;

  // "calories" field.
  int? _calories;
  int get calories => _calories ?? 0;
  set calories(int? val) => _calories = val;

  bool hasCalories() => _calories != null;

  // "fats" field.
  int? _fats;
  int get fats => _fats ?? 0;
  set fats(int? val) => _fats = val;

  bool hasFats() => _fats != null;

  // "proteins" field.
  int? _proteins;
  int get proteins => _proteins ?? 0;
  set proteins(int? val) => _proteins = val;

  bool hasProteins() => _proteins != null;

  // "carbs" field.
  int? _carbs;
  int get carbs => _carbs ?? 0;
  set carbs(int? val) => _carbs = val;

  bool hasCarbs() => _carbs != null;

  // "spices" field.
  List<String>? _spices;
  List<String> get spices => _spices ?? const [];
  set spices(List<String>? val) => _spices = val;

  void updateSpices(Function(List<String>) updateFn) {
    updateFn(_spices ??= []);
  }

  bool hasSpices() => _spices != null;

  // "expanded" field.
  bool? _expanded;

  bool get expanded => _expanded ?? false;

  set expanded(bool? val) => _expanded = val;

  bool hasExpanded() => _expanded != null;

  // "is_enable" field.
  bool? _isEnable;

  bool get isEnable => _isEnable ?? false;

  set isEnable(bool? val) => _isEnable = val;

  bool hasIsEnable() => _isEnable != null;

  static MealItemsStruct fromMap(Map<String, dynamic> data) {
    developer.log('MealItemsStruct.fromMap input: $data',
        name: 'MealItemsStruct');
    final id =
        data['id']?.toString() ?? data['meal_item_id']?.toString() ?? '0';
    final name =
        (data['name'] as String?) ?? (data['name_he'] as String?) ?? 'Unknown';
    developer.log('Parsed id: $id, name: $name', name: 'MealItemsStruct');
    return MealItemsStruct(
      id: id,
      name: name,
      quantity: castToType<String?>(data['quantity']),
      calories: castToType<int?>(data['calories']),
      fats: castToType<int?>(data['fats']),
      proteins: castToType<int?>(data['proteins']),
      carbs: castToType<int?>(data['carbs']),
      spices: getDataList(data['spices']) ?? [],
      expanded: data['expanded'] as bool? ?? false,
      isEnable: castToType<bool?>(data['is_enable']),
    );
  }

  static MealItemsStruct? maybeFromMap(dynamic data) => data is Map
      ? MealItemsStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'id': _id,
        'name': _name,
        'quantity': _quantity,
        'calories': _calories,
        'fats': _fats,
        'proteins': _proteins,
        'carbs': _carbs,
        'spices': _spices,
        'expanded': _expanded,
        'is_enable': _isEnable,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'id': serializeParam(_id, ParamType.String),
        'name': serializeParam(_name, ParamType.String),
        'quantity': serializeParam(_quantity, ParamType.String),
        'calories': serializeParam(_calories, ParamType.int),
        'fats': serializeParam(_fats, ParamType.int),
        'proteins': serializeParam(_proteins, ParamType.int),
        'carbs': serializeParam(_carbs, ParamType.int),
        'spices': serializeParam(_spices, ParamType.String, isList: true),
        'expanded': serializeParam(_expanded, ParamType.bool),
        'is_enable': serializeParam(_isEnable, ParamType.bool),
      }.withoutNulls;

  static MealItemsStruct fromSerializableMap(Map<String, dynamic> data) =>
      MealItemsStruct(
        id: deserializeParam(data['id'], ParamType.String, false),
        name: deserializeParam(data['name'], ParamType.String, false),
        quantity: deserializeParam(data['quantity'], ParamType.String, false),
        calories: deserializeParam(data['calories'], ParamType.int, false),
        fats: deserializeParam(data['fats'], ParamType.int, false),
        proteins: deserializeParam(data['proteins'], ParamType.int, false),
        carbs: deserializeParam(data['carbs'], ParamType.int, false),
        spices:
            deserializeParam<String>(data['spices'], ParamType.String, true),
        expanded: deserializeParam(data['expanded'], ParamType.bool, false),
        isEnable: deserializeParam(data['is_enable'], ParamType.bool, false),
      );

  @override
  String toString() => 'MealItemsStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is MealItemsStruct &&
        id == other.id &&
        name == other.name &&
        quantity == other.quantity &&
        calories == other.calories &&
        fats == other.fats &&
        proteins == other.proteins &&
        carbs == other.carbs &&
        listEquality.equals(spices, other.spices) &&
        expanded == other.expanded &&
        isEnable == other.isEnable;
  }

  @override
  int get hashCode => const ListEquality().hash([
        id,
        name,
        quantity,
        calories,
        fats,
        proteins,
        carbs,
        spices,
        expanded,
        isEnable
      ]);

  // copyWith
  MealItemsStruct copyWith({
    String? id,
    String? name,
    String? quantity,
    int? calories,
    int? fats,
    int? proteins,
    int? carbs,
    bool? expanded,
    bool? isEnable,
  }) {
    return MealItemsStruct(
      id: id ?? this.id,
      name: name ?? this.name,
      quantity: quantity ?? this.quantity,
      calories: calories ?? this.calories,
      fats: fats ?? this.fats,
      proteins: proteins ?? this.proteins,
      carbs: carbs ?? this.carbs,
      expanded: expanded ?? this.expanded,
      isEnable: isEnable ?? this.isEnable,
    );
  }
}

MealItemsStruct createMealItemsStruct({
  String? id,
  String? name,
  String? quantity,
  int? calories,
  int? fats,
  int? proteins,
  int? carbs,
  bool? expanded,
  bool? isEnable,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    MealItemsStruct(
      id: id,
      name: name,
      quantity: quantity,
      calories: calories,
      fats: fats,
      proteins: proteins,
      carbs: carbs,
      expanded: expanded,
      isEnable: isEnable,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

MealItemsStruct? updateMealItemsStruct(
  MealItemsStruct? mealItems, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    mealItems
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addMealItemsStructData(
  Map<String, dynamic> firestoreData,
  MealItemsStruct? mealItems,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (mealItems == null) {
    return;
  }
  if (mealItems.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && mealItems.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final mealItemsData = getMealItemsFirestoreData(mealItems, forFieldValue);
  final nestedData = mealItemsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = mealItems.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getMealItemsFirestoreData(
  MealItemsStruct? mealItems, [
  bool forFieldValue = false,
]) {
  if (mealItems == null) {
    return {};
  }
  final firestoreData = mapToFirestore(mealItems.toMap());

  // Add any Firestore field values
  mealItems.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getMealItemsListFirestoreData(
  List<MealItemsStruct>? mealItemss,
) =>
    mealItemss?.map((e) => getMealItemsFirestoreData(e, true)).toList() ?? [];

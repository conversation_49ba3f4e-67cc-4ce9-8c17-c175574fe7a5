<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>55847766910-2ignrsi5saooirobiot1pv3h574oldse.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.55847766910-2ignrsi5saooirobiot1pv3h574oldse</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>55847766910-jlgtgraulakedqjjqae9vh8qav050aoa.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyB5ficL8B9xlLZuW-CBadDibRbvpiR9jzY</string>
	<key>GCM_SENDER_ID</key>
	<string>55847766910</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.calcountai.app</string>
	<key>PROJECT_ID</key>
	<string>cal-count-a-i-gzhesm</string>
	<key>STORAGE_BUCKET</key>
	<string>cal-count-a-i-gzhesm.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:55847766910:ios:9915b2343cdeacd2e5812f</string>
</dict>
</plist>
import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/auth/firebase_auth/auth_util.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:developer' as developer;
import 'package:cal_counti_a_i/error_service.dart';

class BugReportScreen extends StatefulWidget {
  const BugReportScreen({super.key});

  @override
  _BugReportScreenState createState() => _BugReportScreenState();
}

class _BugReportScreenState extends State<BugReportScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _actualResultsController =
      TextEditingController();
  final TextEditingController _expectedResultsController =
      TextEditingController();

  void _submitReport(BuildContext context) async {
    final email = _emailController.text;
    final description = _descriptionController.text;
    final actualResults = _actualResultsController.text;
    final expectedResults = _expectedResultsController.text;

    if (email.isEmpty || description.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(languageWiseText(context, 'fillAllFields')),
        ),
      );
      return;
    }

    ErrorService.setupUser(currentUserUid, currentUserEmail, currentUserDisplayName);

    // Store suggestion in Firestore
    try {
      await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUserUid)
          .collection('suggestions')
          .add({
        'email': email,
        'description': description,
        'actualResults': actualResults,
        'expectedResults': expectedResults,
        'timestamp': FieldValue.serverTimestamp(),
      });
      developer.log('Suggestion logged to Firestore at suggestions/$currentUserUid/user_suggestions', name: 'BugReportScreen');
    } catch (e) {
      developer.log('Error logging suggestion to Firestore: $e', name: 'BugReportScreen');
    }

    final results = await Future.wait([
      ErrorService.reportUserSuggestion(
        email,
        description,
        additionalData: {
          'actualResults': actualResults,
          'expectedResults': expectedResults,
        },
        category: 'info',
      ),
      ErrorService.reportToBugsnag(
        email,
        description,
        null,
        actualResults: actualResults,
        expectedResults: expectedResults,
      ),
      ErrorService.reportToPostHog(
        email,
        description,
        null,
        actualResults: actualResults,
        expectedResults: expectedResults,
      ),
    ]);

    if (results.every((success) => success)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(languageWiseText(context, 'reportSubmitted')),
        ),
      );
      _emailController.clear();
      _descriptionController.clear();
      _actualResultsController.clear();
      _expectedResultsController.clear();
      Navigator.pop(context);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to submit report. Please try again.'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEnglish = Localizations.localeOf(context).languageCode == 'en';
    return MeasuredWidget(
      name: 'BugReportScreen',
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: Text(
            languageWiseText(context, 'reportAProblem'),
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.black,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: Directionality(
          textDirection: isEnglish ? TextDirection.ltr : TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: _emailController,
                    decoration: InputDecoration(
                      labelText: languageWiseText(context, 'enterYourEmail'),
                      labelStyle: const TextStyle(color: Colors.grey),
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: languageWiseText(context, 'whatWentWrong'),
                      labelStyle: const TextStyle(color: Colors.grey),
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _actualResultsController,
                    decoration: InputDecoration(
                      labelText: languageWiseText(context, 'actualResults'),
                      labelStyle: const TextStyle(color: Colors.grey),
                      border: const OutlineInputBorder(),
                      focusedBorder:  OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _expectedResultsController,
                    decoration: InputDecoration(
                      labelText: languageWiseText(context, 'expectedResults'),
                      labelStyle: const TextStyle(color: Colors.grey),
                      border: const OutlineInputBorder(),
                      focusedBorder:  OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _submitReport(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      minimumSize: const Size(double.infinity, 50),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      languageWiseText(context, 'submit'),
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String languageWiseText(BuildContext context, String key) {
    Map<String, String> _enValues = {
      'reportAProblem': 'Report a Problem',
      'enterYourEmail': 'Enter your email',
      'whatWentWrong': 'What went wrong? *',
      'actualResults': 'Actual results *',
      'expectedResults': 'Expected results *',
      'takeAScreenshot': 'Take a screenshot',
      'takeAScreenRecording': 'Take a screen recording',
      'selectFromGallery': 'Select image from gallery',
      'submit': 'Submit',
      'fillAllFields': 'Please fill all required fields',
      'reportSubmitted': 'Report submitted successfully',
    };

    Map<String, String> _heValues = {
      'reportAProblem': 'דווח על בעיה',
      'enterYourEmail': 'הזן את המייל שלך',
      'whatWentWrong': 'מה השתבש? *',
      'actualResults': 'תוצאות בפועל *',
      'expectedResults': 'תוצאות צפויות *',
      'takeAScreenshot': 'צלם צילום מסך',
      'takeAScreenRecording': 'צלם הקלטת מסך',
      'selectFromGallery': 'בחר תמונה מהגלריה',
      'submit': 'שלח',
      'fillAllFields': 'אנא מלא את כל השדות הנדרשים',
      'reportSubmitted': 'הדיווח נשלח בהצלחה',
    };

    final isEnglish = Localizations.localeOf(context).languageCode == 'en';
    return isEnglish ? _enValues[key]! : _heValues[key]!;
  }
}

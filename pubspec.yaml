name: cal_counti_a_i
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.9+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
#  mobile_scanner: ^3.5.2
  flutter_localizations:
    sdk: flutter
  auto_size_text: 3.0.0
  cached_network_image: 3.4.1
  camera: #^0.11.0+2
  google_mlkit_barcode_scanning:
  connectivity_plus:
  cloud_firestore: 5.5.0
  cloud_functions: 5.1.5
  collection: #1.18.0
  device_info_plus: #^11.2.0
  easy_debounce: 2.0.1
  equatable: 2.0.5
  firebase_analytics: 11.3.5
  firebase_auth: 5.3.3
  firebase_core: 3.8.0
  firebase_messaging: 15.1.5
  fl_chart: ^0.69.2
  flutter_animate: 4.5.0
  flutter_secure_storage: 9.2.2
  flutter_svg: 2.0.10+1
  font_awesome_flutter: 10.7.0
  go_router: 12.1.3
  google_fonts: 6.1.0
  google_sign_in: 6.2.1
  http: 1.2.2
  image_picker: ^1.1.2
  intl: 0.19.0
  json_path: 0.7.2
  lottie: 2.7.0
  mime_type: 1.0.0
  package_info_plus: ^8.1.2
  page_transition: 2.1.0
  path_provider: 2.1.4
  percent_indicator: 4.2.2
  permission_handler: 11.3.1
  provider: 6.1.2
  rxdart: 0.27.7
  shared_preferences: 2.3.2
  sign_in_with_apple: 6.1.2
  stream_transform: 2.1.0
  synchronized: 3.2.0
  timeago: 3.6.1
  url_launcher: 6.3.0
  webview_flutter: 4.9.0
  webview_flutter_android: 3.16.7
  webviewx_plus: 0.5.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0
  flutter_image_compress: ^2.4.0
  dio: ^5.7.0
  vertical_weight_slider: ^2.6.0
  get_storage: ^2.1.1
  purchases_flutter: ^8.9.0
  flutter_switch: ^0.3.2
  http_parser: ^4.0.2
  image: ^4.2.0
  posthog_flutter: ^5.0.0
  bugsnag_flutter: ^4.1.2
  flutter_timezone: ^4.1.1
  mobile_scanner: ^6.0.10
  pdfx: ^2.8.0
  bugsnag_flutter_performance: ^1.4.1

dependency_overrides:
  http: 1.2.2
#  pointer_interceptor: 0.10.1+2
#  pointer_interceptor_ios: 0.10.1
#  pointer_interceptor_platform_interface: 0.10.0+1
#  pointer_interceptor_web: 0.10.2+1
#  uuid: ^4.0.0
#  win32: 5.5.1

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_launcher_icon.png"
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/images/adaptive_foreground_icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/
    - assets/privacy_policy.pdf
    - assets/terms_of_service.pdf

  fonts:
    - family: SFHebrew
      fonts:
        - asset: assets/fonts/sf_hebrew_bold.ttf

import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

Future<String> getDeviceId() async {
  // Add your function code here!
  final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    // Android-specific implementation
    final androidInfo = await deviceInfo.androidInfo;
    return androidInfo.id;
  } else if (Platform.isIOS) {
    // iOS-specific implementation
    final iosInfo = await deviceInfo.iosInfo;
    return iosInfo.identifierForVendor ?? "Unknown iOS ID";
  } else {
    return "Unsupported platform";
  }
}

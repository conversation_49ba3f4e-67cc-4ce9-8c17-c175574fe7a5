{"name": "cloud-functions", "description": "Firebase Cloud Functions", "dependencies": {"firebase-admin": "^11.11.0", "firebase-functions": "^4.4.1", "braintree": "^3.6.0", "@mux/mux-node": "^7.3.3", "stripe": "^8.0.1", "axios": "1.7.4", "razorpay": "^2.8.4", "qs": "^6.7.0", "@onesignal/node-onesignal": "^2.0.1-beta2"}, "devDependencies": {"eslint": "^6.8.0", "eslint-plugin-promise": "^4.2.1"}, "scripts": {"lint": "./node_modules/.bin/eslint --max-warnings=0 .", "serve": "firebase -P cal-count-a-i-gzhesm emulators:start --only functions", "shell": "firebase -P cal-count-a-i-gzhesm functions:shell", "start": "npm run shell", "logs": "firebase -P cal-count-a-i-gzhesm functions:log", "compile": "cp ../../tsconfig.template.json ./tsconfig-compile.json && tsc --project tsconfig-compile.json"}, "engines": {"node": "18"}, "private": true}
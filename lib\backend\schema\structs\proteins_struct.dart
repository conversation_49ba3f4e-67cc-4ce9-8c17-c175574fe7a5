// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class ProteinsStruct extends FFFirebaseStruct {
  ProteinsStruct({
    String? proteinsRequired,
    String? proteinsContained,
    String? proteinsPercentage,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _proteinsRequired = proteinsRequired,
        _proteinsContained = proteinsContained,
        _proteinsPercentage = proteinsPercentage,
        super(firestoreUtilData);

  // "proteins_required" field.
  String? _proteinsRequired;
  String get proteinsRequired => _proteinsRequired ?? '';
  set proteinsRequired(String? val) => _proteinsRequired = val;

  bool hasProteinsRequired() => _proteinsRequired != null;

  // "proteins_contained" field.
  String? _proteinsContained;
  String get proteinsContained => _proteinsContained ?? '';
  set proteinsContained(String? val) => _proteinsContained = val;

  bool hasProteinsContained() => _proteinsContained != null;

  // "proteins_percentage" field.
  String? _proteinsPercentage;
  String get proteinsPercentage => _proteinsPercentage ?? '';
  set proteinsPercentage(String? val) => _proteinsPercentage = val;

  bool hasProteinsPercentage() => _proteinsPercentage != null;

  static ProteinsStruct fromMap(Map<String, dynamic> data) => ProteinsStruct(
        proteinsRequired: castToType<String?>(data['proteins_required']),
        proteinsContained: castToType<String?>(data['proteins_contained']),
        proteinsPercentage: castToType<String?>(data['proteins_percentage']),
      );

  static ProteinsStruct? maybeFromMap(dynamic data) =>
      data is Map ? ProteinsStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'proteins_required': _proteinsRequired,
        'proteins_contained': _proteinsContained,
        'proteins_percentage': _proteinsPercentage,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'proteins_required': serializeParam(
          _proteinsRequired,
          ParamType.String,
        ),
        'proteins_contained': serializeParam(
          _proteinsContained,
          ParamType.String,
        ),
        'proteins_percentage': serializeParam(
          _proteinsPercentage,
          ParamType.String,
        ),
      }.withoutNulls;

  static ProteinsStruct fromSerializableMap(Map<String, dynamic> data) =>
      ProteinsStruct(
        proteinsRequired: deserializeParam(
          data['proteins_required'],
          ParamType.String,
          false,
        ),
        proteinsContained: deserializeParam(
          data['proteins_contained'],
          ParamType.String,
          false,
        ),
        proteinsPercentage: deserializeParam(
          data['proteins_percentage'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'ProteinsStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is ProteinsStruct &&
        proteinsRequired == other.proteinsRequired &&
        proteinsContained == other.proteinsContained &&
        proteinsPercentage == other.proteinsPercentage;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([proteinsRequired, proteinsContained, proteinsPercentage]);
}

ProteinsStruct createProteinsStruct({
  String? proteinsRequired,
  String? proteinsContained,
  String? proteinsPercentage,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    ProteinsStruct(
      proteinsRequired: proteinsRequired,
      proteinsContained: proteinsContained,
      proteinsPercentage: proteinsPercentage,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

ProteinsStruct? updateProteinsStruct(
  ProteinsStruct? proteins, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    proteins
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addProteinsStructData(
  Map<String, dynamic> firestoreData,
  ProteinsStruct? proteins,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (proteins == null) {
    return;
  }
  if (proteins.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && proteins.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final proteinsData = getProteinsFirestoreData(proteins, forFieldValue);
  final nestedData = proteinsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = proteins.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getProteinsFirestoreData(
  ProteinsStruct? proteins, [
  bool forFieldValue = false,
]) {
  if (proteins == null) {
    return {};
  }
  final firestoreData = mapToFirestore(proteins.toMap());

  // Add any Firestore field values
  proteins.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getProteinsListFirestoreData(
  List<ProteinsStruct>? proteinss,
) =>
    proteinss?.map((e) => getProteinsFirestoreData(e, true)).toList() ?? [];

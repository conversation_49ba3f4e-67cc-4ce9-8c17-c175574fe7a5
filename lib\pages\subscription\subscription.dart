// import 'package:flutter/material.dart';
// import 'package:cal_counti_a_i/flutter_flow/revenue_cat_util.dart'
//     as revenue_cat;
// import 'package:purchases_flutter/purchases_flutter.dart';
// import 'package:url_launcher/url_launcher.dart';
//
// class SubscriptionScreenNew extends StatefulWidget {
//   @override
//   _SubscriptionScreenNewState createState() => _SubscriptionScreenNewState();
// }
//
// class _SubscriptionScreenNewState extends State<SubscriptionScreenNew> {
//   Offerings? offerings;
//   Package? selectedPackage;
//   CustomerInfo? purchaserInfo;
//
//   @override
//   void initState() {
//     super.initState();
//     _loadRevenueCatData();
//   }
//
//   Future<void> _loadRevenueCatData() async {
//     try {
//       final offerings = await Purchases.getOfferings();
//       final customerInfo = await Purchases.getCustomerInfo();
//
//       setState(() {
//         this.offerings = offerings;
//         this.purchaserInfo = customerInfo;
//       });
//
//       if (offerings.current?.availablePackages.isNotEmpty == true) {
//         selectedPackage = offerings.current?.monthly ??
//             offerings.current!.availablePackages.first;
//       }
//     } catch (e) {
//       print('Error loading RevenueCat data: $e');
//     }
//   }
//
//   bool _isPackagePurchased(Package package) {
//     return purchaserInfo?.entitlements.active.values.any(
//           (entitlement) =>
//               entitlement.productIdentifier ==
//                   package.storeProduct.identifier ||
//               entitlement.productPlanIdentifier ==
//                   package.storeProduct.identifier,
//         ) ??
//         false;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (offerings == null || offerings!.current == null) {
//       return Center(child: CircularProgressIndicator());
//     }
//
//     if (offerings!.current!.availablePackages.isEmpty) {
//       return Center(child: Text('No subscription plans available.'));
//     }
//
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: SingleChildScrollView(
//           padding: const EdgeInsets.symmetric(horizontal: 16),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               // Top Section
//               _buildTopSection(),
//               const SizedBox(height: 24),
//
//               // Content Section
//               _buildContentSection(),
//               const SizedBox(height: 32),
//
//               // Plan Cards
//               _buildPlanCards(),
//               const SizedBox(height: 32),
//
//               // Bottom CTA Section
//               _buildBottomCTA(context),
//               const SizedBox(height: 24),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   // Top Section: Logo, Title, and Subtitle
//   Widget _buildTopSection() {
//     return Column(
//       children: [
//         Image.asset(
//           'assets/images/parrot_logo.png',
//           width: 100,
//           height: 100,
//         ),
//         const SizedBox(height: 16),
//         Text(
//           'Your Free Trial',
//           style: TextStyle(
//               fontSize: 24, fontWeight: FontWeight.bold, color: Colors.black),
//         ),
//         const SizedBox(height: 8),
//         Text(
//           'How Does it Work?',
//           style: TextStyle(fontSize: 16, color: Colors.grey[700]),
//         ),
//       ],
//     );
//   }
//
//   // Content Section: Features
//   // Content Section: Features
//   Widget _buildContentSection() {
//     DateTime trialEndDate =
//         DateTime.now().add(Duration(days: 7)); // Example for 7-day trial
//
//     return Column(
//       children: [
//         _buildFeature(Icons.lock, 'Today: Get instant access',
//             'Unlock Parrot Pal and start logging your calories quickly with voice note, text, or scan.'),
//         const SizedBox(height: 24),
//         _buildFeature(Icons.thumb_up, 'No Surprises: Easily cancel anytime',
//             'You’re always in control—cancel your trial anytime before it ends with just a few taps.'),
//         const SizedBox(height: 24),
//         _buildFeature(Icons.star, 'Day 7: Trial Ends',
//             'You’ll be charged on ${trialEndDate.day} ${_getMonthName(trialEndDate.month)} ${trialEndDate.year}. You can cancel anytime before.'),
//       ],
//     );
//   }
//
//   String _getMonthName(int month) {
//     switch (month) {
//       case 1:
//         return 'Jan';
//       case 2:
//         return 'Feb';
//       case 3:
//         return 'Mar';
//       case 4:
//         return 'Apr';
//       case 5:
//         return 'May';
//       case 6:
//         return 'Jun';
//       case 7:
//         return 'Jul';
//       case 8:
//         return 'Aug';
//       case 9:
//         return 'Sep';
//       case 10:
//         return 'Oct';
//       case 11:
//         return 'Nov';
//       case 12:
//         return 'Dec';
//       default:
//         return '';
//     }
//   }
//
//   // Helper Method to Build Feature Items
//   Widget _buildFeature(IconData icon, String title, String description) {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Icon(icon, size: 32, color: Colors.green),
//         const SizedBox(width: 16),
//         Expanded(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 title,
//                 style: TextStyle(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                     color: Colors.black),
//               ),
//               const SizedBox(height: 8),
//               Text(
//                 description,
//                 style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   // Plan Cards
//   Widget _buildPlanCards() {
//     return SingleChildScrollView(
//       scrollDirection: Axis.horizontal,
//       child: Row(
//         children: offerings!.current!.availablePackages.map((package) {
//           final isSelected = selectedPackage?.identifier == package.identifier;
//           final isPurchased = _isPackagePurchased(package);
//
//           return Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 8),
//             child: SubscriptionPlanCard(
//               package: package,
//               isSelected: isSelected,
//               isPurchased: isPurchased,
//               onTap: () {
//                 setState(() {
//                   selectedPackage = package;
//                 });
//               },
//             ),
//           );
//         }).toList(),
//       ),
//     );
//   }
//
//   // Bottom CTA Section
//   Widget _buildBottomCTA(BuildContext context) {
//     String trialDuration = '7-day free trial'; // Example for 7-day trial
//     String priceAfterTrial = '\$39.99/year (\$3.33/month)'; // Example price
//
//     String subscriptionTitle =
//         selectedPackage?.storeProduct.title ?? 'Premium Subscription';
//     String subscriptionLength =
//         selectedPackage?.packageType == PackageType.annual
//             ? 'Annual'
//             : 'Monthly';
//
//     if (selectedPackage != null &&
//         selectedPackage!.storeProduct.introductoryPrice != null) {
//       trialDuration =
//           '${selectedPackage!.storeProduct.introductoryPrice!.periodNumberOfUnits} ${_getTrialUnit(selectedPackage!)} free trial';
//       priceAfterTrial =
//           '${selectedPackage!.storeProduct.priceString} after trial';
//     }
//
//     return Card(
//       margin: const EdgeInsets.symmetric(horizontal: 16),
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       elevation: 4,
//       child: Padding(
//         padding: const EdgeInsets.all(16),
//         child: Column(
//           children: [
//             Text(
//               subscriptionTitle,
//               style: TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.black,
//               ),
//             ),
//             const SizedBox(height: 8),
//
//             // Subscription Length - Required by Apple
//             Text(
//               'Subscription Length: $subscriptionLength',
//               style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//             ),
//             const SizedBox(height: 8),
//             // Trial Information
//             Text(
//               '$trialDuration, then $priceAfterTrial',
//               style: TextStyle(
//                 fontSize: 16,
//                 fontWeight: FontWeight.bold,
//                 color: Colors.black,
//               ),
//             ),
//             const SizedBox(height: 8),
//             Text(
//               'No Payment Due Now',
//               style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//             ),
//             const SizedBox(height: 16),
//             // Try for Free Button
//             ElevatedButton.icon(
//               onPressed: () async {
//                 if (selectedPackage != null) {
//                   bool success = await revenue_cat
//                       .purchasePackage(selectedPackage!.identifier);
//                   if (success) {
//                     await _loadRevenueCatData();
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       SnackBar(content: Text('Free Trial Started!')),
//                     );
//                   }
//                 }
//               },
//               icon: Icon(Icons.arrow_forward, color: Colors.white),
//               label: Text(
//                 'Try for free 🙌',
//                 style: TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.bold,
//                   color: Colors.white,
//                 ),
//               ),
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.green,
//                 padding:
//                     const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
//                 shape: RoundedRectangleBorder(
//                     borderRadius: BorderRadius.circular(8)),
//               ),
//             ),
//             const SizedBox(height: 16),
//             // Links
//             Column(
//               children: [
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     TextButton(
//                       onPressed: () async {
//                         try {
//                           await Purchases.restorePurchases();
//                           ScaffoldMessenger.of(context).showSnackBar(
//                             SnackBar(
//                                 content:
//                                     Text('Purchases restored successfully!')),
//                           );
//                         } catch (e) {
//                           print('Error restoring purchases: $e');
//                           ScaffoldMessenger.of(context).showSnackBar(
//                             SnackBar(
//                                 content: Text('Failed to restore purchases.')),
//                           );
//                         }
//                       },
//                       child: Text(
//                         'Restore',
//                         style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//                       ),
//                     ),
//                     const SizedBox(width: 16),
//                     // Privacy Policy link - Required by Apple
//                     TextButton(
//                       onPressed: () async {
//                         String url =
//                             'https://calcountai.wixsite.com/calcountai/terms-conditions/privacypolicy';
//                         await launchUrl(
//                           Uri.parse(url),
//                           mode: LaunchMode.externalApplication,
//                         );
//                       },
//                       child: Text(
//                         'Privacy Policy',
//                         style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//                       ),
//                     ),
//                   ],
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     // Terms of Use/EULA link - Required by Apple with specific name
//                     TextButton(
//                       onPressed: () async {
//                         String url =
//                             'https://calcountai.wixsite.com/calcountai/terms-conditions';
//                         await launchUrl(
//                           Uri.parse(url),
//                           mode: LaunchMode.externalApplication,
//                         );
//                       },
//                       child: Text(
//                         'Terms of Use (EULA)',
//                         style: TextStyle(fontSize: 14, color: Colors.grey[700]),
//                       ),
//                     ),
//                     const SizedBox(width: 16),
//                     TextButton(
//                       onPressed: () {
//                         // Handle "See more plans" tap
//                       },
//                       child: Text(
//                         'See more plans',
//                         style: TextStyle(fontSize: 14, color: Colors.green),
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }
//
//   String _getTrialUnit(Package package) {
//     final unit = package.storeProduct.introductoryPrice?.periodUnit;
//     switch (unit) {
//       case PeriodUnit.day:
//         return 'days';
//       case PeriodUnit.week:
//         return 'weeks';
//       case PeriodUnit.month:
//         return 'months';
//       default:
//         return 'days'; // Default to "days" if the unit is unknown
//     }
//   }
// }
//
// // Reusable Subscription Plan Card Widget
// class SubscriptionPlanCard extends StatelessWidget {
//   final Package package;
//   final bool isSelected;
//   final bool isPurchased;
//   final VoidCallback onTap;
//
//   const SubscriptionPlanCard({
//     Key? key,
//     required this.package,
//     required this.isSelected,
//     required this.isPurchased,
//     required this.onTap,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Stack(
//         children: [
//           Container(
//             width: 200,
//             margin: const EdgeInsets.symmetric(vertical: 10),
//             padding: const EdgeInsets.all(16),
//             decoration: BoxDecoration(
//               color: _getBackgroundColor(),
//               borderRadius: BorderRadius.circular(8),
//               border: Border.all(color: _getBorderColor()),
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // Trial Period Label
//                 if (package.storeProduct.introductoryPrice != null)
//                   Container(
//                     padding:
//                         const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
//                     decoration: BoxDecoration(
//                       color: Colors.black,
//                       borderRadius: BorderRadius.circular(4),
//                     ),
//                     child: Text(
//                       '${package.storeProduct.introductoryPrice?.periodNumberOfUnits} ${_getTrialUnit()}',
//                       style: const TextStyle(color: Colors.white, fontSize: 12),
//                     ),
//                   ),
//                 const SizedBox(height: 10),
//
//                 // Plan Title
//                 Text(
//                   package.storeProduct.title,
//                   style: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.bold,
//                       color: Colors.black),
//                 ),
//                 const SizedBox(height: 5),
//
//                 // Plan Price
//                 Text(
//                   package.storeProduct.priceString,
//                   style: const TextStyle(color: Colors.black, fontSize: 14),
//                 ),
//               ],
//             ),
//           ),
//
//           // Purchased Badge
//           if (isPurchased)
//             Positioned.fill(
//               child: Align(
//                 alignment: Alignment.topRight,
//                 child: Container(
//                   padding:
//                       const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
//                   margin: const EdgeInsets.symmetric(horizontal: 10),
//                   decoration: BoxDecoration(
//                     color: Colors.green,
//                     borderRadius: BorderRadius.circular(50),
//                   ),
//                   child: Text(
//                     'Purchased',
//                     style: const TextStyle(color: Colors.white, fontSize: 12),
//                   ),
//                 ),
//               ),
//             ),
//         ],
//       ),
//     );
//   }
//
//   Color _getBackgroundColor() {
//     if (isPurchased) return Colors.green.shade200;
//     if (isSelected) return Colors.black12;
//     return Colors.white;
//   }
//
//   Color _getBorderColor() {
//     if (isPurchased) return Colors.green;
//     if (isSelected) return Colors.black;
//     return Colors.transparent;
//   }
//
//   String _getTrialUnit() {
//     final unit = package.storeProduct.introductoryPrice?.periodUnit;
//     switch (unit) {
//       case PeriodUnit.day:
//         return 'days';
//       case PeriodUnit.week:
//         return 'weeks';
//       case PeriodUnit.month:
//         return 'months';
//       default:
//         return 'days';
//     }
//   }
// }

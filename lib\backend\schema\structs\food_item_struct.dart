class FoodItem {
  final num id;
  final String enName;
  final String heName;
  final String? created_at;
  final num? relevance;
  final FoodDetails data;

  FoodItem({
    required this.id,
    required this.enName,
    required this.heName,
    required this.data,
    this.created_at,
    this.relevance,
  });

  // Convert JSON to FoodItem
  factory FoodItem.fromJson(Map<String, dynamic> json) {
    return FoodItem(
      id: json['id'] is num
          ? json['id']
          : num.tryParse(json['id'].toString()) ?? 0,
      enName: json['en_name'] ?? '',
      heName: json['he_name'] ?? '',
      data: FoodDetails.fromJson(json['data'] ?? {}),
      created_at: json['created_at'],
      relevance: json['relevance'],
    );
  }

  // Convert FoodItem to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'en_name': enName,
      'he_name': heName,
      'data': data.toJson(),
      'created_at': created_at,
      'relevance': relevance,
    };
  }
}

class FoodDetails {
  final String id;
  final String? name;
  final String? brand;
  final num fats;
  final num carbs;
  final num protein;
  final num calories;
  final num servings;
  final String servingTypeId;
  final List<ServingType> servingTypes;

  FoodDetails({
    required this.id,
    required this.name,
    this.brand,
    this.fats = 0,
    this.carbs = 0,
    this.protein = 0,
    this.calories = 0,
    this.servings = 1,
    required this.servingTypeId,
    this.servingTypes = const [],
  });

  // Convert JSON to FoodDetails
  factory FoodDetails.fromJson(Map<String, dynamic> json) {
    return FoodDetails(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      brand: json['brand'] ?? '',
      fats: json['fats'] ?? 0,
      carbs: json['carbs'] ?? 0,
      protein: json['protein'] ?? 0,
      calories: json['calories'] ?? 0,
      servings: json['servings'] ?? 1,
      servingTypeId: json['servingTypeId'] ?? '',
      servingTypes: List<ServingType>.from(
        json['servingTypes']?.map((item) => ServingType.fromJson(item)) ?? [],
      ),
    );
  }

  // Convert FoodDetails to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'fats': fats,
      'carbs': carbs,
      'protein': protein,
      'calories': calories,
      'servings': servings,
      'servingTypeId': servingTypeId,
      'servingTypes': servingTypes.map((item) => item.toJson()).toList(),
    };
  }
}

class ServingType {
  final String id;
  final num? fat;
  final num? iron;
  final num? carbs;
  final num? fiber;
  final String label;
  final num? sugar;
  final num? sodium;
  final num? calcium;
  final num? protein;
  final num? calories;
  final num? vitaminA;
  final num? vitaminC;
  final num? potassium;
  final String? formalName;
  final String? metricUnit;
  final num? cholesterol;
  final num? metricAmount;
  final num? saturatedFat;
  final num? numberOfUnits;
  final num? monoUnsaturatedFat;
  final num? polyUnsaturatedFat;

  ServingType({
    required this.id,
    required this.label,
    this.fat,
    this.iron,
    this.carbs,
    this.fiber,
    this.sugar,
    this.sodium,
    this.calcium,
    this.protein,
    this.calories,
    this.vitaminA,
    this.vitaminC,
    this.potassium,
    this.formalName,
    this.metricUnit,
    this.cholesterol,
    this.metricAmount,
    this.saturatedFat,
    this.numberOfUnits,
    this.monoUnsaturatedFat,
    this.polyUnsaturatedFat,
  });

  // Convert JSON to ServingType
  factory ServingType.fromJson(Map<String, dynamic> json) {
    return ServingType(
      id: json['id'] ?? '',
      label: json['label'] ?? '',
      fat: json['fat'] ?? 0,
      iron: json['iron'] ?? 0,
      carbs: json['carbs'] ?? 0,
      fiber: json['fiber'] ?? 0,
      sugar: json['sugar'] ?? 0,
      sodium: json['sodium'] ?? 0,
      calcium: json['calcium'] ?? 0,
      protein: json['protein'] ?? 0,
      calories: json['calories'] ?? 0,
      vitaminA: json['vitaminA'] ?? 0,
      vitaminC: json['vitaminC'] ?? 0,
      potassium: json['potassium'] ?? 0,
      formalName: json['formalName'] ?? '',
      metricUnit: json['metricUnit'] ?? '',
      cholesterol: json['cholesterol'] ?? 0,
      metricAmount: json['metricAmount'] ?? 0,
      saturatedFat: json['saturatedFat'] ?? 0,
      numberOfUnits: json['numberOfUnits'] ?? 0,
      monoUnsaturatedFat: json['monoUnsaturatedFat'] ?? 0,
      polyUnsaturatedFat: json['polyUnsaturatedFat'] ?? 0,
    );
  }

  // Convert ServingType to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'fat': fat,
      'iron': iron,
      'carbs': carbs,
      'fiber': fiber,
      'sugar': sugar,
      'sodium': sodium,
      'calcium': calcium,
      'protein': protein,
      'calories': calories,
      'vitaminA': vitaminA,
      'vitaminC': vitaminC,
      'potassium': potassium,
      'formalName': formalName,
      'metricUnit': metricUnit,
      'cholesterol': cholesterol,
      'metricAmount': metricAmount,
      'saturatedFat': saturatedFat,
      'numberOfUnits': numberOfUnits,
      'monoUnsaturatedFat': monoUnsaturatedFat,
      'polyUnsaturatedFat': polyUnsaturatedFat,
    };
  }
}
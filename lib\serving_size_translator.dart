// lib/serving_size_translator.dart

class ServingSizeTranslator {
  static const Map<String, String> _hebrewTranslations = {
    // Standard Units & General Terms
    "serving": "מנה",
    "portion": "מנה",
    "unit": "יחידה",
    "item": "פריט",
    "cup": "כוס",
    "glass": "כוס",
    "oz": "אונקיה",
    "ounce": "אונקיה",
    "fl oz": "אונקיית נוזל",
    "fluid ounce": "אונקיית נוזל",
    "g": "גרם",
    "gram": "גרם",
    "kg": "קילוגרם",
    "kilogram": "קילוגרם",
    "lb": "ליטרה",
    "pound": "ליטרה",
    "ml": "מ\"ל",
    "milliliter": "מיליליטר",
    "l": "ליטר",
    "liter": "ליטר",
    "tbsp": "כף",
    "tablespoon": "כף",
    "tsp": "כפית",
    "teaspoon": "כפית",
    "piece": "חתיכה",
    "slice": "פרוסה",
    "small": "קטן",
    "medium": "בינוני",
    "large": "גדול",
    "extra small": "קטן מאוד",
    "xs": "קטן מאוד",
    "extra large": "גדול מאוד",
    "xl": "גדול מאוד",
    "jumbo": "ג'מבו",
    "regular": "רגיל",
    "standard": "סטנדרטי",
    "single": "בודד",
    "double": "כפול",
    "triple": "משולש",
    "whole": "שלם",
    "half": "חצי",
    "quarter": "רבע",

    // Food Forms & Specific Items
    "scoop": "כדור",
    "sprig": "ענף",
    "clove": "שן",
    "head": "ראש",
    "bunch": "צרור",
    "handful": "חופן",
    "pinch": "קורט",
    "dash": "קמצוץ",
    "drop": "טיפה",
    "can": "פחית",
    "bottle": "בקבוק",
    "jar": "צנצנת",
    "pack": "חבילה",
    "package": "אריזה",
    "bar": "חטיף",
    "square": "ריבוע",
    "wedge": "פלח",
    "fillet": "פילה",
    "steak": "סטייק",
    "breast": "חזה",
    "wing": "כנף",
    "thigh": "ירך",
    "drumstick": "שוק",
    "leg": "רגל",
    "loaf": "כיכר",
    "roll": "לחמניה",
    "bun": "לחמניה",
    "patty": "קציצה",
    "link": "נקניקיה",
    "strip": "רצועה",
    "cube": "קוביה",
    "bowl": "קערה",
    "plate": "צלחת",
    "shot": "שוט",
    "cookie": "עוגיה",
    "biscuit": "ביסקוויט",
    "cracker": "קרקר",
    "muffin": "מאפין",
    "bagel": "בייגל",
    "croissant": "קרואסון",
    "donut": "סופגניה",
    "cake slice": "פרוסת עוגה",
    "pie slice": "פרוסת פאי",
    "large pizza": "פיצה גדולה",
    "pizza": "פיצה",
    "miniature pizza": "פיצה מיניאטורית",
    "small pizza": "פיצה קטנה",
    "tube cake": "עוגת קוגלהוף", // As previously added
    "cubic inch": "אינץ' מעוקב", // As previously added
    "container": "מיכל",
    "tub": "גביע",
    "carton": "קרטון",
    "packet": "שקית קטנה",
    "sachet": "שקית",
    "kernel": "גרעין",
    "ear": "קלח",
    "spear": "גבעול",
    "stalk": "גבעול",
    "leaf": "עלה",
    "berry": "פרי יער",
    "segment": "פלח",
    "rasher": "פרוסת בייקון",
    "stick": "מקל",
    "sheet": "דף",
    "slab": "לוח",
    "pat": "גוש קטן",
    "bite": "ביס",
    "individual": "אישי",
    "family size": "גודל משפחתי",
    "king size": "קינג סייז"
  };

  static String translate(String englishTerm, String languageCode) {
    if (languageCode.toLowerCase() == 'he') {
      String normalizedTerm = englishTerm.toLowerCase();
      return _hebrewTranslations[normalizedTerm] ??
          englishTerm; // Default to English if no translation found
    }
    return englishTerm; // Default to English for other languages
  }
}

import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/internationalization.dart';

class SubscriptionTrialScreen extends StatelessWidget {
  const SubscriptionTrialScreen({super.key});

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 30,
                ),
                Image.asset('assets/images/subscription-active.png',
                    height: 70, width: 70),
                const SizedBox(height: 16),
                Text(
                  isEnglish ? 'Your Free trial' : 'הניסיון החינמי שלך',
                  style: FlutterFlowTheme.of(context).headlineLarge.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 28,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 6),
                Text(
                  isEnglish ? 'How Does it work?' : 'איך זה עובד?',
                  style: FlutterFlowTheme.of(context).headlineMedium.copyWith(
                        fontWeight: FontWeight.normal,
                        fontSize: 26,
                        color: FlutterFlowTheme.of(context).primaryText,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 15),
                // Steps
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 18.0, vertical: 12),
                  child: Container(
                    width: double.infinity,
                    // decoration: BoxDecoration(
                    //   color: FlutterFlowTheme.of(context).grey,
                    //   borderRadius: BorderRadius.circular(28),
                    // ),
                    padding:
                        const EdgeInsets.symmetric(vertical: 24, horizontal: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _TrialStep(
                          icon: Icon(Icons.lock_open,
                              color: Colors.white, size: 28),
                          title: isEnglish
                              ? 'Today: Get instant access'
                              : 'היום: קבל גישה מיידית',
                          description: isEnglish
                              ? 'Unlock Parrot Pal and start logging your calories quickly with voice note, text or scan.'
                              : 'פתח את Parrot Pal והתחל לרשום קלוריות במהירות עם קול, טקסט או סריקה.',
                          highlight: true,
                        ),
                        _TrialStep(
                          icon: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Image(
                              fit: BoxFit.contain,
                              image: AssetImage(
                                'assets/images/thumbs-up.png',
                              ),
                              color: Colors.white,
                              height: 20,
                            ),
                          ),
                          title: isEnglish
                              ? 'No Surprises: Easily cancel anytime'
                              : 'אין הפתעות: בטל בכל עת',
                          description: isEnglish
                              ? 'You’re always in control — cancel your trial anytime before it ends with just a few taps.'
                              : 'אתה תמיד בשליטה — תוכל לבטל את הניסיון בכל עת לפני שהוא מסתיים בלחיצות בודדות.',
                          highlight: true,
                        ),
                        _TrialStep(
                          icon: Icon(
                            Icons.star,
                            color: Colors.white,
                          ),
                          title: isEnglish
                              ? 'Day 7: Trial Ends'
                              : 'יום 7: הניסיון מסתיים',
                          description: isEnglish
                              ? 'You’ll be charged on 20 May 2025. You can cancel anytime before'
                              : 'תחויב ב-20 במאי 2025. תוכל לבטל בכל עת לפני כן.',
                          highlight: false,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 5),
                // Offer box
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.zero,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(25),
                        topRight: Radius.circular(25)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  padding:
                      const EdgeInsets.symmetric(vertical: 18, horizontal: 12),
                  child: Column(
                    children: [
                      Text(
                        isEnglish
                            ? '7-day free trial, then\n\$39.99/year (\$3.33/month)'
                            : 'ניסיון חינם ל-7 ימים, ואז\n39.99\$ לשנה (3.33\$ לחודש)',
                        style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                            fontWeight: FontWeight.normal,
                            fontSize: 15,
                            color: FlutterFlowTheme.of(context).secondaryGrey),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isEnglish
                            ? 'No Payment Due Now'
                            : 'אין צורך בתשלום כעת',
                        style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                              fontWeight: FontWeight.w600,
                              color: FlutterFlowTheme.of(context).primaryText,
                              fontSize: 15,
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF14756F),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 14),
                          ),
                          child: Text(
                            isEnglish ? 'Try for free 🙌' : 'נסה בחינם 🙌',
                            style: FlutterFlowTheme.of(context)
                                .titleMedium
                                .copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {},
                        child: Text(
                          isEnglish ? 'See more plans' : 'ראה תוכניות נוספות',
                          style: FlutterFlowTheme.of(context)
                              .bodyLarge
                              .copyWith(
                                color:
                                    FlutterFlowTheme.of(context).secondaryGrey,
                                fontWeight: FontWeight.normal,
                                decoration: TextDecoration.underline,
                              ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {},
                            child: Text(
                              isEnglish ? 'Restore' : 'שחזר',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .copyWith(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryGrey,
                                    fontWeight: FontWeight.normal,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ),
                          Text('  |  ',
                              style: FlutterFlowTheme.of(context).bodyMedium),
                          GestureDetector(
                            onTap: () {},
                            child: Text(
                              isEnglish
                                  ? 'Terms & conditions'
                                  : 'תנאים והגבלות',
                              style: FlutterFlowTheme.of(context)
                                  .bodyMedium
                                  .copyWith(
                                    color: FlutterFlowTheme.of(context)
                                        .secondaryGrey,
                                    fontWeight: FontWeight.normal,
                                    decoration: TextDecoration.underline,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _TrialStep extends StatelessWidget {
  final Widget icon;
  final String title;
  final String description;
  final bool highlight;

  const _TrialStep({
    required this.icon,
    required this.title,
    required this.description,
    this.highlight = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 38,
            height: 80,
            decoration: BoxDecoration(
              color: highlight ? Colors.teal.shade700 : Colors.teal.shade200,
              borderRadius: BorderRadius.circular(24),
            ),
            child: icon,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: FlutterFlowTheme.of(context).bodyLarge.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: FlutterFlowTheme.of(context).bodyMedium.copyWith(
                          fontWeight: FontWeight.normal,
                          fontSize: 15,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

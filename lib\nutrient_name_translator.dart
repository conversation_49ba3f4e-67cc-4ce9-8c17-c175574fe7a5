class NutrientNameTranslator {
  static const Map<String, String> _hebrewTranslations = {
    // Macronutrients (often primary, but can appear in 'other')
    "protein": "חלבון",
    "carbohydrates": "פחמימות",
    "fat": "שומן",
    "calories": "קלוריות",

    // Micronutrients & Other Components
    "iron": "ברזל",
    "fiber": "סיבים תזונתיים",
    "sugars": "סוכרים",
    "sugar": "סוכר",
    "added sugars": "תוספת סוכר",
    "sodium": "נתרן",
    "calcium": "סידן",
    "potassium": "אשלגן",
    "cholesterol": "כולסטרול",
    "saturated fat": "שומן רווי",
    "monounsaturated fat": "שומן חד בלתי רווי",
    "polyunsaturated fat": "שומן רב בלתי רווי",
    "trans fat": "שומן טראנס",
    "vitamin a": "ויטמין A",
    "vitamin c": "ויטמין C",
    "vitamin d": "ויטמין D",
    "vitamin e": "ויטמין E",
    "vitamin k": "ויטמין K",
    "thiamin": "תיאמין (B1)",
    "riboflavin": "ריבופלבין (B2)",
    "niacin": "ניאצין (B3)",
    "vitamin b6": "ויטמין B6",
    "folate": "חומצה פולית", // Or פולאט
    "vitamin b12": "ויטמין B12",
    "phosphorus": "זרחן",
    "magnesium": "מגנזיום",
    "zinc": "אבץ",
    "copper": "נחושת",
    "manganese": "מנגן",
    "selenium": "סלניום",
    "chloride": "כלוריד",
    "iodine": "יוד",
    "chromium": "כרום",
    "molybdenum": "מוליבדן",
    "mono unsaturated fat": "שומן חד בלתי רווי",
    "poly unsaturated fat": "שומן רב בלתי רווי",
    "number of units": "מספר יחידות",
    "metric amount": "כמות מטרית",
    "metric unit": "יחידה מטרית",
    "formal name": "שם רשמי",
  };

  static String translate(String englishTerm, String languageCode) {
    if (languageCode.toLowerCase() == 'he') {
      String normalizedTerm = englishTerm.toLowerCase();
      return _hebrewTranslations[normalizedTerm] ??
          englishTerm; // Default to English if no translation found
    }
    return englishTerm; // Default to English for other languages
  }
}

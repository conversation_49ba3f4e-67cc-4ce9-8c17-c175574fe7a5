import 'dart:convert';
import '../schema/structs/index.dart';

import 'package:flutter/foundation.dart';

import '/flutter_flow/flutter_flow_util.dart';
import 'api_manager.dart';

export 'api_manager.dart' show ApiCallResponse;

const _kPrivateApiFunctionName = 'ffPrivateApiCall';

class SignUpCall {
  static Future<ApiCallResponse> call({
    dynamic jsonJson,
  }) async {
    final json = _serializeJson(jsonJson);
    final ffApiRequestBody = '''
${json}''';
    return ApiManager.instance.makeApiCall(
      callName: 'SignUp',
      apiUrl: '${baseUrl}/auth',
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static UserDataStruct? data(dynamic response) =>
      UserDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? accessToken(dynamic response) =>
      castToType<String>(getJsonField(
        response,
        r'''$.data.access_token''',
      ));
}

class OnboardingCall {
  static Future<ApiCallResponse> call({
    dynamic jsonJson,
  }) async {
    final json = _serializeJson(jsonJson);
    final ffApiRequestBody = '''
${json}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Onboarding',
      apiUrl: '${baseUrl}/user/onboarding',
      callType: ApiCallType.POST,
      headers: {},
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static UserDataStruct? data(dynamic response) =>
      UserDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class UpdateGoalCall {
  static Future<ApiCallResponse> call({
    dynamic jsonJson,
    String? accessToken,
  }) async {
    final json = _serializeJson(jsonJson);
    final ffApiRequestBody = '''
${json}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Update Goal',
      apiUrl: '${baseUrl}/user/update/goal',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static UserDataStruct? data(dynamic response) =>
      UserDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class UserInfoCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'User Info',
      apiUrl: '${baseUrl}/user/info',
      callType: ApiCallType.GET,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static UserDataStruct? data(dynamic response) =>
      UserDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class MealDetailCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
    int? mealId,
  }) async {
    final ffApiRequestBody = '''
{
    "meal_id" : ${mealId}
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Meal Detail',
      apiUrl: '${baseUrl}/meal/detail',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static MealDetailStruct? data(dynamic response) =>
      MealDetailStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class DashboardDataCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
    String? date = '',
    String? timezone,
  }) async {
    final ffApiRequestBody = '''
{
  "date": "${escapeStringForJson(date)}",
  "timezone": "$timezone"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Dashboard Data',
      apiUrl: '${baseUrl}/dashboard',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static DashboardDataStruct? data(dynamic response) =>
      DashboardDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class MealOverviewCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'Meal Overview',
      apiUrl: '${baseUrl}/meal/overview',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      bodyType: BodyType.NONE,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static FoodOverviewDetailStruct? data(dynamic response) =>
      FoodOverviewDetailStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class UpdateUserInfoCall {
  static Future<ApiCallResponse> call({
    dynamic jsonJson,
    String? accessToken = '',
  }) async {
    final json = _serializeJson(jsonJson);
    final ffApiRequestBody = '''
${json}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Update User Info',
      apiUrl: '${baseUrl}/user/update',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static UserDataStruct? userData(dynamic response) =>
      UserDataStruct.maybeFromMap(getJsonField(
        response,
        r'''$.data''',
      ));

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class UserLogoutCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'User Logout',
      apiUrl: '${baseUrl}/user/logout',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      bodyType: BodyType.NONE,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));

  static int? status(dynamic response) => castToType<int>(getJsonField(
        response,
        r'''$.status''',
      ));

  static String? message(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.message''',
      ));
}

class UserDeleteCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'User Delete',
      apiUrl: '${baseUrl}/user/delete/account',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      bodyType: BodyType.NONE,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static int? status(dynamic response) => castToType<int>(getJsonField(
        response,
        r'''$.status''',
      ));

  static String? message(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.message''',
      ));
}

class AddMealCall {
  static Future<ApiCallResponse> call({
    String? accessToken = '',
    String? imageFile = '',
    String? type = '',
    String? barcode = '',
  }) async {
    final url = Uri.parse('${baseUrl}/meal/add');
    final request = http.MultipartRequest('POST', url);

    // Add headers
    request.headers.addAll({
      'Accept': 'application/json',
      'Authorization': 'Bearer ${accessToken}',
      'Connection': 'keep-alive',
    });

    // Add form fields
    if (type != null && type.isNotEmpty) {
      request.fields['type'] = type;
    }
    if (barcode != null && barcode.isNotEmpty) {
      request.fields['barcode'] = barcode;
    }

    // Add image file if provided
    if (imageFile != null && imageFile.isNotEmpty) {
      try {
        final file = await http.MultipartFile.fromPath('image', imageFile);
        request.files.add(file);
      } catch (e) {
        print('Error adding file to request: $e');
      }
    }

    try {
      final streamedResponse = await request.send().timeout(
        const Duration(seconds: 60),
        onTimeout: () {
          throw TimeoutException('Upload timed out');
        },
      );
      final response = await http.Response.fromStream(streamedResponse);

      return ApiCallResponse(
        jsonDecode(response.body),
        response.headers,
        response.statusCode,
      );
    } catch (e) {
      print('AddMealCall error: $e');
      rethrow;
    }
  }
}

class DeleteMealCall {
  static Future<ApiCallResponse> call({
    dynamic jsonJson,
    String? accessToken,
  }) async {
    final json = _serializeJson(jsonJson);
    final ffApiRequestBody = '''
${json}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Delete Meal',
      apiUrl: '${baseUrl}/meal/delete',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }
}

class ApiPagingParams {
  int nextPageNumber = 0;
  int numItems = 0;
  dynamic lastResponse;

  ApiPagingParams({
    required this.nextPageNumber,
    required this.numItems,
    required this.lastResponse,
  });

  @override
  String toString() =>
      'PagingParams(nextPageNumber: $nextPageNumber, numItems: $numItems, lastResponse: $lastResponse,)';
}

String _toEncodable(dynamic item) {
  if (item is DocumentReference) {
    return item.path;
  }
  return item;
}

String _serializeList(List? list) {
  list ??= <String>[];
  try {
    return json.encode(list, toEncodable: _toEncodable);
  } catch (_) {
    if (kDebugMode) {
      print("List serialization failed. Returning empty list.");
    }
    return '[]';
  }
}

String _serializeJson(dynamic jsonVar, [bool isList = false]) {
  jsonVar ??= (isList ? [] : {});
  try {
    return json.encode(jsonVar, toEncodable: _toEncodable);
  } catch (_) {
    if (kDebugMode) {
      print("Json serialization failed. Returning empty json.");
    }
    return isList ? '[]' : '{}';
  }
}

String? escapeStringForJson(String? input) {
  if (input == null) {
    return null;
  }
  return input
      .replaceAll('\\', '\\\\')
      .replaceAll('"', '\\"')
      .replaceAll('\n', '\\n')
      .replaceAll('\t', '\\t');
}

// String get baseUrl => 'https://db79-122-167-85-179.ngrok-free.app';
String get baseUrl => 'https://app.calcounti.com';
// String get baseUrl => 'http://51.17.120.168:3033';

// accessToken = FFAppState().authToken;

// ========================================== NEW API CALLS =========================================================

class UpdateMealCall {
  static Future<ApiCallResponse> call({
    required String accessToken,
    required Map<String, dynamic> jsonJson,
  }) async {
    final response = await ApiManager.instance.call(
      ApiCallOptions(
        callName: 'UpdateMeal',
        callType: ApiCallType.POST,
        apiUrl: '${baseUrl}/meal/update',
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        params: {},
        bodyType: BodyType.JSON,
        body: jsonEncode(jsonJson),
        returnBody: true,
        encodeBodyUtf8: false,
        decodeUtf8: false,
        cache: false,
        alwaysAllowBody: false,
        isStreamingApi: false,
      ),
    );

    return response;
  }

  static dynamic data(dynamic response) =>
      getJsonField(response.jsonBody, r'''$.data''');

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class SearchFoodCall {
  static Future<ApiCallResponse> call({
    required String accessToken,
    required String searchTerm,
  }) async {
    return ApiManager.instance.makeApiCall(
      callName: 'Search Food',
      apiUrl: '${baseUrl}/foodDatabase/search?searchTerm=$searchTerm',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer $accessToken',
      },
      params: {},
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static List<dynamic>? data(dynamic response) =>
      getJsonField(response, r'''$.data''') as List<dynamic>?;

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

class WeightLogCall {
  static Future<ApiCallResponse> call({
    required String accessToken,
    // required int fitnessGoalId,
    required double targetWeightValue,
    // required String targetWeightUnit,
    // required double goalAchievementRate,
    // required int challenges,
    // required int accomplishmentId,
  }) async {
    final Map<String, dynamic> body = {
      "weight": targetWeightValue.toInt(),
    };
    final ffApiRequestBody = json.encode(body);
    return ApiManager.instance.makeApiCall(
      callName: 'Weight Log',
      apiUrl: '${baseUrl}/user/daily_logs',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }
}

class RegenerateMealCall {
  static Future<ApiCallResponse> call({
    required String accessToken,
    required int mealId,
    required String userAdjustmentPrompt,
  }) async {
    final ffApiRequestBody = '''
{
  "meal_id": ${mealId},
  "user_adjustment_prompt": "${escapeStringForJson(userAdjustmentPrompt)}"
}''';
    return ApiManager.instance.makeApiCall(
      callName: 'Regenerate Meal',
      apiUrl: '${baseUrl}/meal/regenerate',
      callType: ApiCallType.POST,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
        'Content-Type': 'application/json',
      },
      params: {},
      body: ffApiRequestBody,
      bodyType: BodyType.JSON,
      returnBody: true,
      encodeBodyUtf8: false,
      decodeUtf8: false,
      cache: false,
      isStreamingApi: false,
      alwaysAllowBody: false,
    );
  }

  static dynamic data(dynamic response) =>
      getJsonField(response, r'''$.data''');

  static String? error(dynamic response) => castToType<String>(getJsonField(
        response,
        r'''$.error''',
      ));
}

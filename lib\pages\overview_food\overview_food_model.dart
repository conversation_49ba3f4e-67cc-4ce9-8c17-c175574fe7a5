import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/enums/enums.dart';
import '/backend/schema/structs/index.dart';
import '/componentes/circular_progress/circular_progress_widget.dart';
import '/componentes/segmented/segmented_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'overview_food_widget.dart' show OverviewFoodWidget;
import 'package:flutter/material.dart';

class OverviewFoodModel extends FlutterFlowModel<OverviewFoodWidget> {
  ///  Local state fields for this page.

  FoodOverviewDetailStruct? foodOverviewData;

  void updateFoodOverviewDataStruct(
      Function(FoodOverviewDetailStruct) updateFn) {
    updateFn(foodOverviewData ??= FoodOverviewDetailStruct());
  }

  GoalDuration? selectedGoalDuration = GoalDuration.oneWeek;

  NutritionDuration? selectedNutritionDuration = NutritionDuration.thisWeek;

  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - API (Meal Overview)] action in overview_food widget.
  ApiCallResponse? apiResultData;

  // Model for segmented component.
  late SegmentedModel segmentedModel1;

  // Model for circular_progress component.
  late CircularProgressModel circularProgressModel1;

  // Model for segmented component.
  late SegmentedModel segmentedModel2;
  // Model for circular_progress component.
  late CircularProgressModel circularProgressModel2;

  @override
  void initState(BuildContext context) {
    segmentedModel1 = createModel(context, () => SegmentedModel());
    circularProgressModel1 =
        createModel(context, () => CircularProgressModel());
    segmentedModel2 = createModel(context, () => SegmentedModel());
    circularProgressModel2 =
        createModel(context, () => CircularProgressModel());
  }

  @override
  void dispose() {
    segmentedModel1.dispose();
    circularProgressModel1.dispose();
    segmentedModel2.dispose();
    circularProgressModel2.dispose();
  }
}

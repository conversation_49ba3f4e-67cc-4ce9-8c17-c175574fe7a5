import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/app_state.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import 'package:cal_counti_a_i/flutter_flow/internationalization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../componentes/ingredient_item/nutrient_type.dart';

class EditIngredients extends StatefulWidget {
  final NutrientType nutrientType;
  final double initialValue;
  final double maxValue;

  const EditIngredients({
    super.key,
    required this.nutrientType,
    required this.initialValue,
    required this.maxValue,
  });

  @override
  State<EditIngredients> createState() => _EditIngredientsState();
}

class _EditIngredientsState extends State<EditIngredients> {
  late TextEditingController _controller;
  late double _value;

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
    _controller = TextEditingController(text: _formatNumber(_value));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatNumber(double value) {
    if (value == value.floorToDouble()) {
      return value.toInt().toString();
    }
    return value.toStringAsFixed(1);
  }

  String _getNutrientName(bool isEnglish) {
    switch (widget.nutrientType) {
      case NutrientType.calories:
        return FFLocalizations.of(context).getText(
          'vpquyai3' /* Calories */,
        );
      case NutrientType.protein:
        return FFLocalizations.of(context).getText(
          'ggf2jdx6' /* Protein */,
        );
      case NutrientType.carbs:
        return FFLocalizations.of(context).getText(
          'yt9jxb1i' /* Carbs */,
        );
      case NutrientType.fats:
        return FFLocalizations.of(context).getText(
          'gwkjxrgf' /* Fats */,
        );
    }
  }

  Color _getProgressColor() {
    switch (widget.nutrientType) {
      case NutrientType.calories:
        return Colors.black;
      case NutrientType.protein:
        return Colors.deepOrangeAccent;
      case NutrientType.carbs:
        return Colors.orange;
      case NutrientType.fats:
        return Colors.blue;
    }
  }

  Widget _getNutrientIcon() {
    switch (widget.nutrientType) {
      case NutrientType.calories:
        return const Icon(Icons.local_fire_department,
            color: Colors.black, size: 25);
      case NutrientType.protein:
        return SvgPicture.asset(
          'assets/images/Frame.svg',
          width: 24.0,
          height: 24.0,
          fit: BoxFit.contain,
        );
      case NutrientType.carbs:
        return SvgPicture.asset(
          'assets/images/wheat-barley_svgrepo.com.svg',
          width: 25.0,
          height: 25.0,
          color: Colors.orange,
          fit: BoxFit.cover,
        );
      case NutrientType.fats:
        return const Icon(Icons.opacity, color: Colors.blue, size: 25);
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isEnglish = FFLocalizations.of(context).languageCode == 'en';
    final nutrientName = _getNutrientName(isEnglish);

    return MeasuredWidget(
        name: 'EditIngredients',
        builder: (context) => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: FlutterFlowTheme.of(context).grey,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: const Icon(Icons.arrow_back),
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
          ),
          backgroundColor: Colors.white,
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isEnglish ? 'Edit $nutrientName' : 'ערוך $nutrientName',
                  style: FlutterFlowTheme.of(context).headlineLarge.override(
                        fontFamily: 'SFHebrew',
                        color: FlutterFlowTheme.of(context).primary,
                        fontSize: 24.0,
                        letterSpacing: 0.0,
                      ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      SizedBox(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              child: CircularProgressIndicator(
                                // value: _value / widget.maxValue,
                                value: 0.5,
                                strokeWidth: 5,
                                backgroundColor: Colors.grey[200],
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    _getProgressColor()),
                              ),
                              height: 60,
                              width: 60,
                            ),
                            _getNutrientIcon(),
                          ],
                        ),
                      ),
                      const SizedBox(width: 24),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _formatNumber(_value),
                            style: FlutterFlowTheme.of(context)
                                .titleMedium
                                .override(fontSize: 20),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            isEnglish
                                ? 'Out of ${_formatNumber(widget.maxValue)} left today'
                                : 'מתוך ${_formatNumber(widget.maxValue)} נשאר להיום',
                            style: TextStyle(
                              fontSize: 15,
                              color: FlutterFlowTheme.of(context).secondaryText,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 36),
                TextField(
                  controller: _controller,
                  cursorColor: FlutterFlowTheme.of(context).primary,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  style: FlutterFlowTheme.of(context).labelLarge,
                  decoration: InputDecoration(
                    labelText: nutrientName,
                    labelStyle: TextStyle(
                      fontSize: 16,
                      color: FlutterFlowTheme.of(context).primary,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    disabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: FlutterFlowTheme.of(context).primary),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
                  ),
                  onChanged: (val) {
                    setState(() {
                      _value = double.tryParse(val) ?? 0.0;
                      _controller.text = _formatNumber(_value);
                    });
                  },
                ),
                const Spacer(),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          setState(() {
                            _value = widget.initialValue;
                            _controller.text = _formatNumber(_value);
                          });
                        },
                        style: OutlinedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          side: const BorderSide(color: Colors.black12),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                        ),
                        child: Text(
                          isEnglish ? 'Revert' : 'החזר',
                          style: const TextStyle(fontSize: 18, color: Colors.black),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop(_value);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                        ),
                        child: Text(
                          isEnglish ? 'Done' : 'סיום',
                          style: const TextStyle(fontSize: 18, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: FFAppState().bottomPadding)
              ],
            ),
          ),
        )
    );
  }
}

import '/flutter_flow/flutter_flow_util.dart';
import 'language_switch_widget.dart' show LanguageSwitchWidget;
import 'package:flutter/material.dart';

class LanguageSwitchModel extends FlutterFlowModel<LanguageSwitchWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for Switch widget.
  bool? switchValue;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}

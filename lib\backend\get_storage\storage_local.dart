import 'package:cal_counti_a_i/backend/schema/structs/food_item_struct.dart';
import 'package:get_storage/get_storage.dart';

class StorageOperations {
  static const String _loggedFoodsKey = 'loggedFoods';
  final GetStorage _storage = GetStorage();

  // Initialize GetStorage (call this in main.dart)
  static Future<void> init() async {
    await GetStorage.init();
  }

  // Save or update a food item
  void saveFoodItem(FoodItem foodItem) {
    List<dynamic> loggedFoods = _storage.read(_loggedFoodsKey) ?? [];
    int index = loggedFoods.indexWhere((item) => item['id'] == foodItem.id);
    if (index != -1) {
      // Update existing item
      loggedFoods[index] = foodItem.toJson();
    } else {
      // Add new item
      loggedFoods.add(foodItem.toJson());
    }
    _storage.write(_loggedFoodsKey, loggedFoods);
  }

  // Retrieve all logged food items
  List<FoodItem> getLoggedFoodItems() {
    List<dynamic> loggedFoods = _storage.read(_loggedFoodsKey) ?? [];
    return loggedFoods.map((json) => FoodItem.fromJson(json)).toList();
  }

  // Clear all logged food items
  void clearLoggedFoods() {
    _storage.remove(_loggedFoodsKey);
  }
}

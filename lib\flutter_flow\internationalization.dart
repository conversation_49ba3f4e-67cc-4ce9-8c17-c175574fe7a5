import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

const _kLocaleStorageKey = '__locale_key__';

class FFLocalizations {
  FFLocalizations(this.locale);

  final Locale locale;

  static FFLocalizations of(BuildContext context) => Localizations.of<FFLocalizations>(context, FFLocalizations)!;

  static List<String> languages() => ['en', 'he'];

  static late SharedPreferences _prefs;

  static Future initialize() async => _prefs = await SharedPreferences.getInstance();

  static Future storeLocale(String locale) => _prefs.setString(_kLocaleStorageKey, locale);

  static Locale? getStoredLocale() {
    final locale = _prefs.getString(_kLocaleStorageKey);
    return locale != null && locale.isNotEmpty ? createLocale(locale) : null;
  }

  String get languageCode => locale.toString();

  String? get languageShortCode =>
      _languagesWithShortCode.contains(locale.toString()) ? '${locale.toString()}_short' : null;

  int get languageIndex => languages().contains(languageCode) ? languages().indexOf(languageCode) : 0;

  String getText(String key) => (kTranslationsMap[key] ?? {})[locale.toString()] ?? '';

  String getVariableText({
    String? enText = '',
    String? heText = '',
  }) =>
      [enText, heText][languageIndex] ?? '';

  static const Set<String> _languagesWithShortCode = {
    'ar',
    'az',
    'ca',
    'cs',
    'da',
    'de',
    'dv',
    'en',
    'es',
    'et',
    'fi',
    'fr',
    'gr',
    'he',
    'hi',
    'hu',
    'it',
    'km',
    'ku',
    'mn',
    'ms',
    'no',
    'pt',
    'ro',
    'ru',
    'rw',
    'sv',
    'th',
    'uk',
    'vi',
  };
}

/// Used if the locale is not supported by GlobalMaterialLocalizations.
class FallbackMaterialLocalizationDelegate extends LocalizationsDelegate<MaterialLocalizations> {
  const FallbackMaterialLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<MaterialLocalizations> load(Locale locale) async => SynchronousFuture<MaterialLocalizations>(
        const DefaultMaterialLocalizations(),
      );

  @override
  bool shouldReload(FallbackMaterialLocalizationDelegate old) => false;
}

/// Used if the locale is not supported by GlobalCupertinoLocalizations.
class FallbackCupertinoLocalizationDelegate extends LocalizationsDelegate<CupertinoLocalizations> {
  const FallbackCupertinoLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<CupertinoLocalizations> load(Locale locale) => SynchronousFuture<CupertinoLocalizations>(
        const DefaultCupertinoLocalizations(),
      );

  @override
  bool shouldReload(FallbackCupertinoLocalizationDelegate old) => false;
}

class FFLocalizationsDelegate extends LocalizationsDelegate<FFLocalizations> {
  const FFLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<FFLocalizations> load(Locale locale) => SynchronousFuture<FFLocalizations>(FFLocalizations(locale));

  @override
  bool shouldReload(FFLocalizationsDelegate old) => false;
}

Locale createLocale(String language) => language.contains('_')
    ? Locale.fromSubtags(
        languageCode: language.split('_').first,
        scriptCode: language.split('_').last,
      )
    : Locale(language);

bool _isSupportedLocale(Locale locale) {
  final language = locale.toString();
  return FFLocalizations.languages().contains(
    language.endsWith('_') ? language.substring(0, language.length - 1) : language,
  );
}

final kTranslationsMap = <Map<String, Map<String, String>>>[
  // entry_screen
  {
    'r06pxq8g': {
      'en': 'Manage your diet easily and pleasantly',
      'he': 'נהל את התזונה שלך בקלות והנאה',
    },
    '637w9ldb': {
      'en': 'Take a picture of your meal, we\'ll count the calories for you.',
      'he': 'צלם את הארוחה שלך, אנחנו נחשב לך את הקלוריות',
    },
    'alq298de': {
      'en': 'Already have an account? ',
      'he': 'יש לך כבר חשבון?',
    },
    'uzb6wap9': {
      'en': 'Sign In',
      'he': 'היכנס',
    },
    'xugyc1dw': {
      'en': 'Terms',
      'he': 'תנאים',
    },
    'o9l4sxw7': {
      'en': ' and ',
      'he': 'ו',
    },
    'cljmrv5e': {
      'en': 'Privacy Policy',
      'he': 'מדיניות פרטיות',
    },
    'cljmrv5a': {
      'en': 'Information sources',
      'he': 'מקורות מידע',
    },
    '8mqlnlp4': {
      'en': 'Let\'s get started.',
      'he': 'בואו',
    },
    'vfa3lz6l': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  {
    'ej7q30y8': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // quiz
  {
    '6a63ld8i': {
      'en': 'What is your date of birth?',
      'he': 'מה תאריך הלידה שלך?',
    },
    'aa0uwcgy': {
      'en': 'We will use this to build a suitable plan for you!',
      'he': 'המידע הזה יעזור לנו להתאים את התוכנית במיוחד בשבילך',
    },
    'duvmrxuy': {
      'en': 'DOB',
      'he': 'תאריך לידה',
    },
    'cebmt0tq': {
      'en': 'Gender',
      'he': 'מין',
    },
    'r13uhl3s': {
      'en': 'Male',
      'he': 'זכר',
    },
    '8qajh77m': {
      'en': 'Female',
      'he': 'נקבה',
    },
    'ul2n0znu': {
      'en': 'Other',
      'he': 'אחר',
    },
    '9tnh0t2g': {
      'en': 'Enter DOB is required',
      'he': 'יש להזין DOB',
    },
    'f1tzt11k': {
      'en': 'Please choose an option from the dropdown',
      'he': 'אנא בחר אפשרות מהתפריט הנפתח',
    },
    '25129kcj': {
      'en': 'Height & Weight',
      'he': 'גובה ומשקל',
    },
    '99fb1t5x': {
      'en': 'This will be used to calibrate your custom plan.',
      'he': 'פרטים אלו יעזרו להתאים את התוכנית במיוחד עבורך.',
    },
    'qp68giry': {
      'en': 'Metric',
      'he': 'מֶטרִי',
    },
    'ee2wqj14': {
      'en': 'Enter DOB is required',
      'he': 'יש להזין DOB',
    },
    'k1cjsn4h': {
      'en': 'Please choose an option from the dropdown',
      'he': 'אנא בחר אפשרות מהתפריט הנפתח',
    },
    'blcn7tgf': {
      'en': 'CalCounty customers preserve feces for the long term!',
      'he': 'לקוחות קלקאונטי משמרים תוצאות בטווח הארוך!',
    },
    'c5fzwo1u': {
      'en': 'How many times a week do you exercise?',
      'he': 'כמה פעמים בשבוע אתה מתאמן?',
    },
    '624odt7n': {
      'en': 'We will use this to optimize your plan.',
      'he': 'נשתמש בזה בכדי לייעל את התוכנית שלכם.',
    },
    'ozdo4o2r': {
      'en': 'What is your main fitness goal?',
      'he': 'מהו מטרת הכושר העיקרית שלך?',
    },
    '2g4dfqia': {
      'en': 'this helps us a plan for your calorie intake',
      'he': 'זה עוזר לנו לתכנן את צריכת הקלוריות שלך',
    },
    '6j9vow13': {
      'en': 'Choose your target weight!',
      'he': 'מהו משקל היעד שלכם?',
    },
    'pik2qq2c': {
      'en': 'Stay informed about your food choices and their nutritional value.',
      'he': 'נשתמש בנתון הזה כדי להתאים את ההמלצות התזונתיות שלך.',
    },
    'wforbk53': {
      'en': 'Weight',
      'he': 'מִשׁקָל',
    },
    'l2429cur': {
      'en': 'Add Weight',
      'he': 'הוסף משקל',
    },
    '2gc353rq': {
      'en': 'Effortlessly Track Protein & Calories: Your 50kg Weight Loss Journey Starts Here!',
      'he': 'עקוב ללא מאמץ אחר חלבונים וקלוריות: מסע הירידה במשקל שלך ב-50 ק\"ג מתחיל כאן!',
    },
    'w85wlp8w': {
      'en':
          '\"90% of Users Report Clear Results with FoodScan AI – Accurate Protein & Calorie Tracking from Images, No Rebound!\"',
      'he':
          '\"90% מהמשתמשים מדווחים על תוצאות ברורות עם FoodScan AI - מעקב מדויק אחר חלבונים וקלוריות מתמונות, ללא ריבאונד!\"',
    },
    'aiq1v98o': {
      'en': 'How Quickly Do You Want to Achieve Your Goal?',
      'he': 'באיזו מהירות אתה רוצה להשיג את המטרה שלך?',
    },
    'favc18vw': {
      'en': ' See How Fast You Can Reach Your Goal',
      'he': 'ראה כמה מהר אתה יכול להגיע ליעד שלך',
    },
    'r5zkg59j': {
      'en': 'CalCounty customers preserve feces for the long term!',
      'he': 'לקוחות קלקאונטי משמרים תוצאות בטווח הארוך!',
    },
    'auyp6w9j': {
      'en': 'What\'s Holding You Back from Achieving Your Goals?',
      'he': 'מה הכי מעכב אותך מלהשיג את המטרות שלך ?',
    },
    'jeiro3lw': {
      'en': 'What would you like to\naccomplish?',
      'he': 'מה היית רוצה\nלהשיג?',
    },
    '0pk6untz': {
      'en': 'What People Are Saying About Us! CalCounti Ai',
      'he': 'מה אנשים אומרים עלינו! קלקאונטי AI',
    },
    'lj7bbojn': {
      'en': 'All done!',
      'he': 'הכל מוכן!',
    },
    '29daqe0x': {
      'en': 'Thank you for\ntrusting us',
      'he': 'תודה על שבחרת בנו!',
    },
    '495opshv': {
      'en': 'We promise to always keep your\npersonal information private and secure.',
      'he': 'אנו מתחייבים לשמור על המידע האישי שלך פרטי ומאובטח תמיד.',
    },
    '098bjphz': {
      'en': 'Continue',
      'he': 'להמשיך',
    },
    'q9n16tk0': {
      'en': 'Email',
      'he': 'כתובת מייל',
    },
    'qdsd58cf': {
      'en': 'Password',
      'he': 'סִיסמָה',
    },
    'so5nufkf': {
      'en': 'Confirm Password',
      'he': 'אשר את הסיסמה',
    },
    '6t0xtyea': {
      'en': 'By clicking “Continue”, you agree to our ',
      'he': 'על ידי לחיצה על "המשך", אתה מסכים שלנו',
    },
    '8ouvs4dg': {
      'en': 'Terms ',
      'he': 'תנאים',
    },
    'kf5xst85': {
      'en': 'and have read our ',
      'he': 'וקראתי את שלנו',
    },
    'ksc28buv': {
      'en': 'Privacy Policy',
      'he': 'מדיניות פרטיות',
    },
    'k1jw3wg1': {
      'en': '.',
      'he': '.',
    },
    'e1wxpgge': {
      'en': 'Sign Up',
      'he': 'הירשם',
    },
    'nkyxyohr': {
      'en': 'Login',
      'he': 'כְּנִיסָה לַמַעֲרֶכֶת',
    },
    '6x52bpsk': {
      'en': 'Forgot Password',
      'he': 'שכחת סיסמא',
    },
    'stf4v9gj': {
      'en': 'Google',
      'he': 'גוגל',
    },
    '8rolxrnc': {
      'en': 'Continue with Apple',
      'he': 'המשך עם חשבון אפל',
    },
    'u9n68xie': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // settings
  {
    'rrqho7ce': {
      'en': 'Personal Details',
      'he': 'פרטים אישיים',
    },
    'hwmiimuz': {
      'en': 'Adjust Goals',
      'he': 'התאם יעדים',
    },
    'wgtiqy8u': {
      'en': 'Privacy Policy',
      'he': 'מדיניות פרטיות',
    },
    'h8qenkp8': {
      'en': 'Terms & Conditions',
      'he': 'תנאים והגבלות',
    },
    '2w3q4e32': {
      'en': 'App Versions',
      'he': 'גרסאות אפליקציה',
    },
    'suggestions_bugs': {
      'en': 'Suggestions and Bugs',
      'he': 'הצעות ותקלות',
    },
    'nug8zy7m': {
      'en': 'Log Out',
      'he': 'התנתק',
    },
    'uddyw1ll': {
      'en': 'Delete Account',
      'he': 'מחק חשבון',
    },
    'ifi2gif7': {
      'en': 'Settings',
      'he': 'הגדרות',
    },
    'dc0ym1hb': {
      'en': 'Settings',
      'he': 'הגדרות',
    },
  },
  // splash
  {
    'azssyy0r': {
      'en': 'CalCounti AI',
      'he': 'CalCounti AI',
    },
  },
  // forgot
  {
    'gyy5blqg': {
      'en': 'Back',
      'he': 'בְּחֲזָרָה',
    },
    '40qmyw92': {
      'en':
          'We will send you an email with a link to reset your password, please enter the email associated with your account below.',
      'he': 'אנו נשלח לך אימייל עם קישור לאיפוס הסיסמה שלך, אנא הזן את האימייל המשויך לחשבון שלך למטה.',
    },
    'hv0da1tw': {
      'en': 'Your email address...',
      'he': 'כתובת המייל שלך...',
    },
    'al0awpgk': {
      'en': 'Enter your email...',
      'he': 'הכנס את המייל שלך...',
    },
    '02szqu88': {
      'en': 'Send Link',
      'he': 'שלח קישור',
    },
    'j8iv8ta5': {
      'en': 'Forgot Password',
      'he': 'שכחת סיסמא',
    },
    'k5eyup6b': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // dashboard
  {
    'fq5blxkt': {
      'en': 'Calories Left',
      'he': 'קלוריות נשארו',
    },
    'oq5blxkt': {
      'en': 'Calories Over',
      'he': 'קלוריות מעל',
    },
    'ipk82ph6': {
      'en': '',
      'he': '',
    },
    '7gyh1k44': {
      'en': '',
      'he': '',
    },
    'xgwqrwip': {
      'en': 'Proteins Left',
      'he': 'חלבונים נשארו',
    },
    'ogwqrwip': {
      'en': 'Proteins Over',
      'he': 'חלבונים מעל',
    },
    'hgp9g1qq': {
      'en': '',
      'he': '',
    },
    '957pxnqr': {
      'en': 'Carbs Left',
      'he': 'פחמימות נשארו',
    },
    'o57pxnqr': {
      'en': 'Carbs Over',
      'he': 'פחמימות מעל',
    },
    'a11o0z6i': {
      'en': '',
      'he': '',
    },
    'tgh5ss9l': {
      'en': 'Fats Left',
      'he': 'שומנים נשארו',
    },
    'ogh5ss9l': {
      'en': 'Fats Over',
      'he': 'שומנים מעל',
    },
    'f902qa66': {
      'en': 'Meals You\'ve Eaten Recently',
      'he': 'ארוחות שאכלת לאחרונה',
    },
    '2xe2fqe6': {
      'en': 'CalCounti AI',
      'he': 'קל-קאונטי',
    },
    'qeeiespb': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // overview_food
  {
    'lasxshks': {
      'en': 'Weight Loss',
      'he': 'ירידה במשקל',
    },
    'hsb1io6l': {
      'en': 'Upgrade your weight',
      'he': 'שדרג את המשקל שלך',
    },
    '7d0m4t0e': {
      'en': '150lbs',
      'he': '150 פאונד',
    },
    '8wajtgpv': {
      'en': 'Present Weight',
      'he': 'משקל נוכחי',
    },
    'ixm31885': {
      'en': '150lbs',
      'he': '150 פאונד',
    },
    'dz8nnfxw': {
      'en': 'Please update once a week so we can adjust your plan and help you reach your goal.',
      'he': 'אנא עדכן פעם בשבוע כדי שנוכל להתאים את התוכנית שלך ולעזור לך להגיע ליעד שלך.',
    },
    '6hjge119': {
      'en': 'Log Weight',
      'he': 'משקל יומן',
    },
    '6zb1nboh': {
      'en': 'Calories',
      'he': 'קלוריות',
    },
    'vnbztfne': {
      'en': '100% ',
      'he': '100%',
    },
    'y0edej1f': {
      'en': 'Goal accomplished',
      'he': 'המטרה הושגה',
    },
    'jx26qss5': {
      'en': 'Nutrition',
      'he': 'תְזוּנָה',
    },
    'rnw07gjo': {
      'en': 'This week vs previous week',
      'he': 'השבוע לעומת השבוע הקודם',
    },
    '2z890ysg': {
      'en': '0',
      'he': '0',
    },
    'xhy6x1if': {
      'en': 'Total calories',
      'he': 'סך הכל קלוריות',
    },
    'prxpd7jx': {
      'en': '0.0',
      'he': '0.0',
    },
    '8rvgdrpv': {
      'en': 'Daily avg.',
      'he': 'ממוצע יומי',
    },
    'klpcgc8v': {
      'en': 'Overview of Food',
      'he': 'סקירה כללית של מזון',
    },
    'xh5t4r1k': {
      'en': 'Data Analysis',
      'he': 'ניתוח נתונים',
    },
  },
  // notifications
  {
    '64cov966': {
      'en': 'Notifications',
      'he': 'התראות',
    },
    'gqprmbgk': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // personal_detail
  {
    'o2kuu6e1': {
      'en': 'Personalized Nutrition Insights',
      'he': 'תובנות תזונה מותאמות אישית',
    },
    'pjqfg7h9': {
      'en': 'Provide a few details to create a plan tailored just for you.',
      'he': 'ספק כמה פרטים כדי ליצור תוכנית המותאמת במיוחד עבורך.',
    },
    '7fg1gn3i': {
      'en': 'DOB',
      'he': 'DOB',
    },
    'cgf4qqwk': {
      'en': 'Gender',
      'he': 'מִין',
    },
    'jpcaqb7c': {
      'en': 'Male',
      'he': 'זָכָר',
    },
    '2poewxi8': {
      'en': 'Female',
      'he': 'נְקֵבָה',
    },
    'h2zijadv': {
      'en': 'Other',
      'he': 'אַחֵר',
    },
    'viedfyeo': {
      'en': 'Your Height & Weight',
      'he': 'הגובה והמשקל שלך',
    },
    'byo28xe7': {
      'en':
          'Choose your preferred unit system and provide your height and weight. This information helps us fine-tune your plan.',
      'he': 'בחר את מערכת היחידה המועדפת עליך וציין את הגובה והמשקל שלך. מידע זה עוזר לנו לכוונן את התוכנית שלך.',
    },
    'gqmouvea': {
      'en': 'Enter DOB is required',
      'he': 'יש להזין DOB',
    },
    'fhanngar': {
      'en': 'Please choose an option from the dropdown',
      'he': 'אנא בחר אפשרות מהתפריט הנפתח',
    },
    'j9rjkex6': {
      'en': 'Save',
      'he': 'לְהַמשִׁיך',
    },
    'qryfmp8h': {
      'en': 'Personal Details',
      'he': 'סקירה כללית של מזון',
    },
    'ge2uxl8i': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // meal_details
  {
    '2toygafq': {
      'en': 'Breakfast',
      'he': 'ארוחת בוקר',
    },
    'ggf2jdx6': {
      'en': 'Protein',
      'he': 'חֶלְבּוֹן',
    },
    'vpquyai3': {
      'en': 'Calories',
      'he': 'קלוריות',
    },
    'yt9jxb1i': {
      'en': 'Carbs',
      'he': 'פחמימות',
    },
    'gwkjxrgf': {
      'en': 'Fats',
      'he': 'שומנים',
    },
    'oi81tg2x': {
      'en': 'Health Score',
      'he': 'ציון בריאות',
    },
    'ss78l6m3': {
      'en': '',
      'he': '',
    },
    'yil1xe7g': {
      'en': 'Ingredients List',
      'he': 'רשימת מרכיבים',
    },
    'lohdryr3': {
      'en': '',
      'he': '',
    },
  },
  // personal_detail_view
  {
    '6ajj0u2y': {
      'en': 'Update',
      'he': 'לְעַדְכֵּן',
    },
    '6ajj0u1y': {
      'en': 'Your details updated successfully!',
      'he': 'הפרטים שלך עודכנו בהצלחה!',
    },
    '886cglx7': {
      'en': 'Personal Details',
      'he': 'פרטים אישיים',
    },
    '40uzqzay': {
      'en': 'Home',
      'he': 'בַּיִת',
    },
  },
  // gemini_integrations
  {
    'ystoe4ug': {
      'en': 'AI Assistant',
      'he': '',
    },
    'tmalfb48': {
      'en': 'Upload Image',
      'he': '',
    },
    '1g5e2u63': {
      'en': 'Tap to upload image',
      'he': '',
    },
    '7qimyqmu': {
      'en': 'Take Photo',
      'he': '',
    },
    'yd6uvlf5': {
      'en': 'Choose from Gallery',
      'he': '',
    },
    'zcyxlas2': {
      'en': 'Text Prompt',
      'he': '',
    },
    '40rnsmfj': {
      'en': 'Enter your prompt here...',
      'he': '',
    },
    'zd61ibgz': {
      'en': 'Generate Response',
      'he': '',
    },
    'o7sa3r9c': {
      'en': 'AI Response',
      'he': '',
    },
    '9wtd4en8': {
      'en': 'Token Count: 0',
      'he': '',
    },
    'spcf39u3': {
      'en': 'AI generated response will appear here...',
      'he': '',
    },
    'qh1sthpg': {
      'en': 'Generated Response',
      'he': '',
    },
    'yar3ixx7': {
      'en':
          'The image shows a modern kitchen interior with sleek stainless steel appliances and white cabinets. The space appears to be well-lit with natural light coming through windows. The kitchen features a large center island with a marble countertop and contemporary pendant lights hanging above. The overall design aesthetic is minimalist and clean, with a neutral color palette dominated by whites and grays. The layout suggests this is a high-end residential kitchen designed for both functionality and visual appeal.',
      'he': '',
    },
    '6698gdap': {
      'en': 'Generated in 2.3s',
      'he': '',
    },
  },
  // no_notifications
  {
    '3wb5q3ox': {
      'en': 'All Caught Up!',
      'he': 'הכל נתפס!',
    },
    'w0o2dnfr': {
      'en':
          'You\'re all set! No new notifications at the moment. Keep up the great work and stay on track with your fitness journey.',
      'he': 'אתה מוכן! אין הודעות חדשות כרגע. המשיכו בעבודה הנהדרת והישארו במסלול עם מסע הכושר שלכם.',
    },
  },
  // notification_item
  {
    'aeyjwo68': {
      'en': 'Check-in evaluated',
      'he': 'צ\'ק-אין מוערך',
    },
    'd11n9wr1': {
      'en': 'Mar 8, 2022',
      'he': '8 במרץ 2022',
    },
  },
  // segmented
  {
    'up9o47lj': {
      'en': '90 Days',
      'he': '90 ימים',
    },
    'ksvl1oi9': {
      'en': '6 Months',
      'he': '6 חודשים',
    },
    'qkc1hi8w': {
      'en': '1 Year',
      'he': '1 שנה',
    },
    'gixt7e07': {
      'en': 'All Time',
      'he': 'כל הזמן',
    },
  },
  // no_meals
  {
    '6p1ptzsm': {
      'en': 'You Haven\'t Uploaded Any Food Yet',
      'he': 'עדיין לא העלית אוכל',
    },
    'xsma20fp': {
      'en': 'Start tracking today\'s meals by taking a quick photo!',
      'he': 'התחל לעקוב אחר הארוחות של היום על ידי צילום מהיר!',
    },
  },
  // ingredient_item
  {
    'ibk5x86d': {
      'en': 'Protein',
      'he': 'חֶלְבּוֹן',
    },
    '2ia5fkbm': {
      'en': 'Carbs',
      'he': 'פחמימות',
    },
    'g65sk472': {
      'en': 'Fats',
      'he': 'שומנים',
    },
  },
  // circular_progress
  {
    'vxwtbgfh': {
      'en': '',
      'he': '',
    },
  },
  // action_dialog
  {
    '5efik18d': {
      'en': 'Cancel',
      'he': 'לְבַטֵל',
    },
    '4efik18d': {
      'en': 'Confirm',
      'he': 'אישור',
    },
    'xmr6u4vg': {
      'en': 'Yes',
      'he': 'כֵּן',
    },
  },
  // no_ingredient
  {
    'lvua8bvi': {
      'en': 'No Ingredients Available',
      'he': 'אין מרכיבים זמינים',
    },
    '6dub7vxg': {
      'en':
          'This recipe currently has no ingredients listed. Please check back later or contact support for assistance.',
      'he': 'למתכון הזה אין כרגע רכיבים רשומים. אנא בדוק שוב מאוחר יותר או צור קשר עם התמיכה לקבלת סיוע.',
    },
  },
  // language_switch
  {
    'e0wa75tn': {
      'en': 'Language',
      'he': 'שָׂפָה',
    },
    'fh8lyi6z': {
      'en': 'English',
      'he': 'אַנגְלִית',
    },
    '6p8s1fbg': {
      'en': 'עברית',
      'he': 'עברית',
    },
  },
  // Miscellaneous
  {
    'd9zpqvzf': {
      'en': 'Continue',
      'he': 'להמשיך',
    },
    '5v5qxgkb': {
      'en': 'TextField',
      'he': '',
    },
    'b1c4jwd4': {
      'en': 'Allow Camera to capture Food!',
      'he': 'אפשר למצלמה לצלם אוכל!',
    },
    'nj3j7018': {
      'en': 'CalCounti Ai needs to send you notifications about meal updates and other important messages.',
      'he': 'קל-קאונטי צריך לשלוח לך התראות על עדכוני ארוחות והודעות חשובות אחרות.',
    },
    'h7bh0xdq': {
      'en': 'Allow Photos to choose Food!',
      'he': 'אפשר לתמונות לבחור אוכל!',
    },
    't5zoa54h': {
      'en': 'NO',
      'he': 'לֹא',
    },
    'o25u87lj': {
      'en': '',
      'he': '',
    },
    'on0h4ovb': {
      'en': '',
      'he': '',
    },
    'qbejqera': {
      'en': '',
      'he': '',
    },
    '6x1l39o0': {
      'en': '',
      'he': '',
    },
    'tlkhnkly': {
      'en': '',
      'he': '',
    },
    'umixjlii': {
      'en': '',
      'he': '',
    },
    '2duhz0b2': {
      'en': '',
      'he': '',
    },
    'hoib37ra': {
      'en': '',
      'he': '',
    },
    '1d5shyp4': {
      'en': '',
      'he': '',
    },
    'k9b78b4j': {
      'en': '',
      'he': '',
    },
    'xijt2fbs': {
      'en': '',
      'he': '',
    },
    'en8oe0jd': {
      'en': '',
      'he': '',
    },
    'ylfv0v9q': {
      'en': '',
      'he': '',
    },
    'kxrzvp1l': {
      'en': '',
      'he': '',
    },
    'w05kpado': {
      'en': '',
      'he': '',
    },
    '1c6vfnlf': {
      'en': '',
      'he': '',
    },
    'bae4wmt1': {
      'en': '',
      'he': '',
    },
    's9ugktvo': {
      'en': '',
      'he': '',
    },
    '1epyanu7': {
      'en': '',
      'he': '',
    },
    'g34yy6l1': {
      'en': '',
      'he': '',
    },
    'pn50xcuq': {
      'en': '',
      'he': '',
    },
    '0wsztr5s': {
      'en': '',
      'he': '',
    },
    'cu9v5tdp': {
      'en': '',
      'he': '',
    },
    'y7wq767s': {
      'en': '',
      'he': '',
    },
    'ihi37135': {
      'en': '',
      'he': '',
    },
  },

  // subscription
  {
    'wgtiay8u': {
      'en': 'Unlock CalCountiAI to reach your goals faster.',
      'he': 'בטל את נעילת קלקאונטי כדי להגיע ליעדים שלך מהר יותר.',
    },
    'wgtiby8u': {
      'en': 'Restore',
      'he': 'שחזור',
    },
    'wgticx8u': {
      'en': 'Easy food scanning',
      'he': "סריקת מזון קלה",
    },
    'wgtid18u': {
      'en': 'Track your calories with just a picture',
      'he': "עקוב אחר הקלוריות שלך עם תמונה בלבד",
    },
    'wgtie28u': {
      'en': 'Get your dream body',
      'he': "השג את הגוף של החלומות שלך",
    },
    'wgtif38u': {
      'en': 'We keep it simple to make getting results easy',
      'he': "אנחנו שומרים על זה פשוט כדי להפוך את השגת התוצאות לקלה",
    },
    'wgtig48u': {
      'en': 'Track your calories',
      'he': "עקוב אחר הקלוריות שלך",
    },
    'wgtih58u': {
      'en': 'Stay on track with personalized insights',
      'he': "הישאר במסלול עם תובנות מותאמות אישית",
    },
    'wgtij68u': {
      'en': 'No Commitment - Cancel Anytime',
      'he': "אין התחייבות - בטל בכל עת",
    },
    'wgtik78u': {
      'en': 'Start My Journey',
      'he': "התחל את המסע שלי",
    },
    'wgtil88u': {
      'en': 'Manage Subscription',
      'he': "נהל מנוי",
    },
    'wgtim98u': {
      'en': 'FREE',
      'he': "חינם",
    },
    'wgtin08u': {
      'en': 'Purchased',
      'he': "נרכש",
    },
    "wgtiod9u": {
      'en': "Days",
      'he': "ימים",
    },
    "wgtioe0u": {
      'en': "Week",
      'he': "שבוע",
    },
    "wgtiof1u": {
      'en': "Month",
      'he': "חודש",
    },
    "wgtiog2u": {
      'en': "3 Days",
      'he': "3 ימים",
    },
    "wgtioh3u": {
      'en': "Just {{price}} per month",
      'he': "רק {{price}} לחודש",
    },
    "wgtioi4u": {
      'en': "Monthly",
      'he': "חודשי",
    },
    "wgtioj5u": {
      'en': "Yearly",
      'he': "שנתי",
    },
    'free_trial_title': {
      'en': 'Scan for Free – Limited Uses',
      'he': 'סריקה בחינם – שימושים מוגבלים',
    },
    'contact_us': {
      'en': 'Contact Us',
      'he': 'צור קשר',
    },
    'email': {
      'en': 'Email',
      'he': 'אימייל',
    },
    'site': {
      'en': 'Website',
      'he': 'אתר אינטרנט',
    },
  }
].reduce((a, b) => a..addAll(b));

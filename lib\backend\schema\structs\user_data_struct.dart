// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class UserDataStruct extends FFFirebaseStruct {
  UserDataStruct({
    String? firebaseId,
    String? appleId,
    String? googleId,
    String? email,
    String? signupBy,
    String? dob,
    String? gender,
    int? heightValue,
    String? heightUnit,
    String? weightUnit,
    int? workoutFrequencyId,
    int? fitnessGoalId,
    int? accomplishmentId,
    int? caloriesRequired,
    int? proteinRequired,
    int? carbsRequired,
    int? fatRequired,
    String? deviceId,
    String? fcmToken,
    int? weightValue,
    int? freeTrialsLeft,
    double? targetWeightValue,
    String? targetWeightUnit,
    double? goalAchievementRate,
    int? id,
    String? username,
    String? createdAt,
    String? updatedAt,
    String? accessToken,
    String? languageCode,
    int? challenges,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _firebaseId = firebaseId,
        _appleId = appleId,
        _googleId = googleId,
        _email = email,
        _signupBy = signupBy,
        _dob = dob,
        _gender = gender,
        _heightValue = heightValue,
        _heightUnit = heightUnit,
        _weightUnit = weightUnit,
        _workoutFrequencyId = workoutFrequencyId,
        _fitnessGoalId = fitnessGoalId,
        _accomplishmentId = accomplishmentId,
        _caloriesRequired = caloriesRequired,
        _proteinsRequired = proteinRequired,
        _carbsRequired = carbsRequired,
        _fatsRequired = fatRequired,
        _deviceId = deviceId,
        _fcmToken = fcmToken,
        _weightValue = weightValue,
        _freeTrialsLeft = freeTrialsLeft,
        _targetWeightValue = targetWeightValue,
        _targetWeightUnit = targetWeightUnit,
        _goalAchievementRate = goalAchievementRate,
        _id = id,
        _username = username,
        _createdAt = createdAt,
        _updatedAt = updatedAt,
        _accessToken = accessToken,
        _languageCode = languageCode,
        _challenges = challenges,
        super(firestoreUtilData);

  // "firebase_id" field.
  String? _firebaseId;
  String get firebaseId => _firebaseId ?? '';
  set firebaseId(String? val) => _firebaseId = val;

  bool hasFirebaseId() => _firebaseId != null;

  // "apple_id" field.
  String? _appleId;
  String get appleId => _appleId ?? '';
  set appleId(String? val) => _appleId = val;

  bool hasAppleId() => _appleId != null;

  // "google_id" field.
  String? _googleId;
  String get googleId => _googleId ?? '';
  set googleId(String? val) => _googleId = val;

  bool hasGoogleId() => _googleId != null;

  // "email" field.
  String? _email;
  String get email => _email ?? '';
  set email(String? val) => _email = val;

  bool hasEmail() => _email != null;

  // "signup_by" field.
  String? _signupBy;
  String get signupBy => _signupBy ?? '';
  set signupBy(String? val) => _signupBy = val;

  bool hasSignupBy() => _signupBy != null;

  // "dob" field.
  String? _dob;
  String get dob => _dob ?? '';
  set dob(String? val) => _dob = val;

  bool hasDob() => _dob != null;

  // "gender" field.
  String? _gender;
  String get gender => _gender ?? '';
  set gender(String? val) => _gender = val;

  bool hasGender() => _gender != null;

  // "height_value" field.
  int? _heightValue;
  int get heightValue => _heightValue ?? 0;
  set heightValue(int? val) => _heightValue = val;

  void incrementHeightValue(int amount) => heightValue = heightValue + amount;

  bool hasHeightValue() => _heightValue != null;

  // "height_unit" field.
  String? _heightUnit;
  String get heightUnit => _heightUnit ?? '';
  set heightUnit(String? val) => _heightUnit = val;

  bool hasHeightUnit() => _heightUnit != null;

  // "weight_unit" field.
  String? _weightUnit;
  String get weightUnit => _weightUnit ?? '';
  set weightUnit(String? val) => _weightUnit = val;

  bool hasWeightUnit() => _weightUnit != null;

  // "workout_frequency_id" field.
  int? _workoutFrequencyId;
  int get workoutFrequencyId => _workoutFrequencyId ?? 0;
  set workoutFrequencyId(int? val) => _workoutFrequencyId = val;

  void incrementWorkoutFrequencyId(int amount) =>
      workoutFrequencyId = workoutFrequencyId + amount;

  bool hasWorkoutFrequencyId() => _workoutFrequencyId != null;

  // "fitness_goal_id" field.
  int? _fitnessGoalId;
  int get fitnessGoalId => _fitnessGoalId ?? 0;
  set fitnessGoalId(int? val) => _fitnessGoalId = val;

  void incrementFitnessGoalId(int amount) =>
      fitnessGoalId = fitnessGoalId + amount;

  bool hasFitnessGoalId() => _fitnessGoalId != null;

  // "accomplishment_id" field.
  int? _accomplishmentId;
  int get accomplishmentId => _accomplishmentId ?? 0;
  set accomplishmentId(int? val) => _accomplishmentId = val;

  // int calories_required ?? 0
  int? _caloriesRequired;
  int get caloriesRequired => _caloriesRequired ?? 0;
  set caloriesRequired(int? val) => _caloriesRequired = val;

  // int fats_required ?? 0
  int? _fatsRequired;
  int get fatsRequired => _fatsRequired ?? 0;
  set fatsRequired(int? val) => _fatsRequired = val;

  // proteins_required ?? 0
  int? _proteinsRequired;
  int get proteinsRequired => _proteinsRequired ?? 0;
  set proteinsRequired(int? val) => _proteinsRequired = val;

  // carbs_required ?? 0
  int? _carbsRequired;
  int get carbsRequired => _carbsRequired ?? 0;
  set carbsRequired(int? val) => _carbsRequired = val;


  void incrementAccomplishmentId(int amount) =>
      accomplishmentId = accomplishmentId + amount;

  bool hasAccomplishmentId() => _accomplishmentId != null;

  // "device_id" field.
  String? _deviceId;
  String get deviceId => _deviceId ?? '';
  set deviceId(String? val) => _deviceId = val;

  bool hasDeviceId() => _deviceId != null;

  // "language_code" field.
  String? _languageCode;
  String get languageCode => _languageCode ?? '';
  set languageCode(String? val) => _languageCode = val;

  bool hasLanguageCode() => _languageCode != null;

  // "fcm_token" field.
  String? _fcmToken;
  String get fcmToken => _fcmToken ?? '';
  set fcmToken(String? val) => _fcmToken = val;

  bool hasFcmToken() => _fcmToken != null;

  // "weight_value" field.
  int? _weightValue;
  int get weightValue => _weightValue ?? 0;
  set weightValue(int? val) => _weightValue = val;

  void incrementWeightValue(int amount) =>
      weightValue = weightValue + amount;

  bool hasWeightValue() => _weightValue != null;

  // "free_trials_left" field.
  int? _freeTrialsLeft;
  int get freeTrialsLeft => _freeTrialsLeft ?? 0;
  set freeTrialsLeft(int? val) => _freeTrialsLeft = val;

  void incrementFreeTrialsLeft(int amount) =>
      freeTrialsLeft = freeTrialsLeft + amount;

  bool hasFreeTrialsLeft() => _freeTrialsLeft != null;

  // "target_weight_value" field.
  double? _targetWeightValue;
  double get targetWeightValue => _targetWeightValue ?? 0.0;
  set targetWeightValue(double? val) => _targetWeightValue = val;

  void incrementTargetWeightValue(double amount) =>
      targetWeightValue = targetWeightValue + amount;

  bool hasTargetWeightValue() => _targetWeightValue != null;

  // "target_weight_unit" field.
  String? _targetWeightUnit;
  String get targetWeightUnit => _targetWeightUnit ?? '';
  set targetWeightUnit(String? val) => _targetWeightUnit = val;

  bool hasTargetWeightUnit() => _targetWeightUnit != null;

  // "goal_achievement_rate" field.
  double? _goalAchievementRate;
  double get goalAchievementRate => _goalAchievementRate ?? 2.0;
  set goalAchievementRate(double? val) => _goalAchievementRate = val;

  void incrementGoalAchievementRate(double amount) =>
      goalAchievementRate = goalAchievementRate + amount;

  bool hasGoalAchievementRate() => _goalAchievementRate != null;

  // "id" field.
  int? _id;
  int get id => _id ?? 0;
  set id(int? val) => _id = val;

  void incrementId(int amount) => id = id + amount;

  bool hasId() => _id != null;

  // "username" field.
  String? _username;
  String get username => _username ?? '';
  set username(String? val) => _username = val;

  bool hasUsername() => _username != null;

  // "created_at" field.
  String? _createdAt;
  String get createdAt => _createdAt ?? '';
  set createdAt(String? val) => _createdAt = val;

  bool hasCreatedAt() => _createdAt != null;

  // "updated_at" field.
  String? _updatedAt;
  String get updatedAt => _updatedAt ?? '';
  set updatedAt(String? val) => _updatedAt = val;

  bool hasUpdatedAt() => _updatedAt != null;

  // "access_token" field.
  String? _accessToken;
  String get accessToken => _accessToken ?? '';
  set accessToken(String? val) => _accessToken = val;

  bool hasAccessToken() => _accessToken != null;

  // "challenges" field.
  int? _challenges;
  int get challenges => _challenges ?? 0;
  set challenges(int? val) => _challenges = val;

  void incrementChallenges(int amount) => challenges = challenges + amount;

  bool hasChallenges() => _challenges != null;

  static UserDataStruct fromMap(Map<String, dynamic> data) => UserDataStruct(
        firebaseId: data['firebase_id'] as String?,
        appleId: data['apple_id'] as String?,
        googleId: data['google_id'] as String?,
        email: data['email'] as String?,
        signupBy: data['signup_by'] as String?,
        dob: data['dob'] as String?,
        gender: data['gender'] as String?,
        heightValue: castToType<int>(data['height_value']),
        heightUnit: data['height_unit'] as String?,
        weightUnit: data['weight_unit'] as String?,
        workoutFrequencyId: castToType<int>(data['workout_frequency_id']),
        fitnessGoalId: castToType<int>(data['fitness_goal_id']),
        accomplishmentId: castToType<int>(data['accomplishment_id']),
        caloriesRequired: castToType<int>(data['calories_required']),
        carbsRequired:  castToType<int>(data['carbs_required']),
        fatRequired: castToType<int>(data['fats_required']),
        proteinRequired: castToType<int>(data['proteins_required']),
        deviceId: data['device_id'] as String?,
        fcmToken: data['fcm_token'] as String?,
        weightValue: data['weight_value']?.toInt(),
        freeTrialsLeft: data['free_trials_left']?.toInt(),
        targetWeightValue: castToType<double>(data['target_weight_value']),
        targetWeightUnit: data['target_weight_unit'] as String?,
        goalAchievementRate: castToType<double>(data['goal_achievement_rate']),
        id: castToType<int>(data['id']),
        username: data['username'] as String?,
        createdAt: data['created_at'] as String?,
        updatedAt: data['updated_at'] as String?,
        accessToken: data['access_token'] as String?,
        languageCode: data['language_code'] as String?,
        challenges: castToType<int>(data['challenges']),
      );

  static UserDataStruct? maybeFromMap(dynamic data) =>
      data is Map ? UserDataStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'firebase_id': _firebaseId,
        'apple_id': _appleId,
        'google_id': _googleId,
        'email': _email,
        'signup_by': _signupBy,
        'dob': _dob,
        'gender': _gender,
        'height_value': _heightValue,
        'height_unit': _heightUnit,
        'weight_unit': _weightUnit,
        'workout_frequency_id': _workoutFrequencyId,
        'fitness_goal_id': _fitnessGoalId,
        'accomplishment_id': _accomplishmentId,
        'calories_required': _caloriesRequired,
        'carbs_required': _carbsRequired,
        'fats_required': _fatsRequired,
        'proteins_required': _proteinsRequired,
        'device_id': _deviceId,
        'fcm_token': _fcmToken,
        'weight_value': _weightValue,
        'free_trials_left': _freeTrialsLeft,
        'target_weight_value': _targetWeightValue,
        'target_weight_unit': _targetWeightUnit,
        'goal_achievement_rate': _goalAchievementRate,
        'id': _id,
        'username': _username,
        'created_at': _createdAt,
        'updated_at': _updatedAt,
        'access_token': _accessToken,
        'language_code': _languageCode,
        'challenges': _challenges,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'firebase_id': serializeParam(
          _firebaseId,
          ParamType.String,
        ),
        'apple_id': serializeParam(
          _appleId,
          ParamType.String,
        ),
        'google_id': serializeParam(
          _googleId,
          ParamType.String,
        ),
        'email': serializeParam(
          _email,
          ParamType.String,
        ),
        'signup_by': serializeParam(
          _signupBy,
          ParamType.String,
        ),
        'dob': serializeParam(
          _dob,
          ParamType.String,
        ),
        'gender': serializeParam(
          _gender,
          ParamType.String,
        ),
        'height_value': serializeParam(
          _heightValue,
          ParamType.int,
        ),
        'height_unit': serializeParam(
          _heightUnit,
          ParamType.String,
        ),
        'weight_unit': serializeParam(
          _weightUnit,
          ParamType.String,
        ),
        'workout_frequency_id': serializeParam(
          _workoutFrequencyId,
          ParamType.int,
        ),
        'fitness_goal_id': serializeParam(
          _fitnessGoalId,
          ParamType.int,
        ),
        'accomplishment_id': serializeParam(
          _accomplishmentId,
          ParamType.int,
        ),
        'calories_required': serializeParam(
          _caloriesRequired,
          ParamType.int,
        ),
        'carbs_required': serializeParam(
          _carbsRequired,
          ParamType.int,
        ),
        'fats_required': serializeParam(
          _fatsRequired,
          ParamType.int,
        ),
        'proteins_required': serializeParam(
          _proteinsRequired,
          ParamType.int,
        ),
        'device_id': serializeParam(
          _deviceId,
          ParamType.String,
        ),
        'fcm_token': serializeParam(
          _fcmToken,
          ParamType.String,
        ),
        'weight_value': serializeParam(
          _weightValue,
          ParamType.int,
        ),
        'free_trials_left': serializeParam(
          _freeTrialsLeft,
          ParamType.int,
        ),
        'target_weight_value': serializeParam(
          _targetWeightValue,
          ParamType.double,
        ),
        'target_weight_unit': serializeParam(
          _targetWeightUnit,
          ParamType.String,
        ),
        'goal_achievement_rate': serializeParam(
          _goalAchievementRate,
          ParamType.double,
        ),
        'id': serializeParam(
          _id,
          ParamType.int,
        ),
        'username': serializeParam(
          _username,
          ParamType.String,
        ),
        'created_at': serializeParam(
          _createdAt,
          ParamType.String,
        ),
        'updated_at': serializeParam(
          _updatedAt,
          ParamType.String,
        ),
        'access_token': serializeParam(
          _accessToken,
          ParamType.String,
        ),
        'language_code': serializeParam(
          _languageCode,
          ParamType.String,
        ),
        'challenges': serializeParam(
          _challenges,
          ParamType.int,
        ),
      }.withoutNulls;

  static UserDataStruct fromSerializableMap(Map<String, dynamic> data) =>
      UserDataStruct(
        firebaseId: deserializeParam(
          data['firebase_id'],
          ParamType.String,
          false,
        ),
        appleId: deserializeParam(
          data['apple_id'],
          ParamType.String,
          false,
        ),
        googleId: deserializeParam(
          data['google_id'],
          ParamType.String,
          false,
        ),
        email: deserializeParam(
          data['email'],
          ParamType.String,
          false,
        ),
        signupBy: deserializeParam(
          data['signup_by'],
          ParamType.String,
          false,
        ),
        dob: deserializeParam(
          data['dob'],
          ParamType.String,
          false,
        ),
        gender: deserializeParam(
          data['gender'],
          ParamType.String,
          false,
        ),
        heightValue: deserializeParam(
          data['height_value'],
          ParamType.int,
          false,
        ),
        heightUnit: deserializeParam(
          data['height_unit'],
          ParamType.String,
          false,
        ),
        weightUnit: deserializeParam(
          data['weight_unit'],
          ParamType.String,
          false,
        ),
        workoutFrequencyId: deserializeParam(
          data['workout_frequency_id'],
          ParamType.int,
          false,
        ),
        fitnessGoalId: deserializeParam(
          data['fitness_goal_id'],
          ParamType.int,
          false,
        ),
        accomplishmentId: deserializeParam(
          data['accomplishment_id'],
          ParamType.int,
          false,
        ),
        caloriesRequired: deserializeParam(
          data['calories_required'],
          ParamType.int,
          false,
        ),
        carbsRequired: deserializeParam(
          data['carbs_required'],
          ParamType.int,
          false,
        ),
        fatRequired: deserializeParam(
          data['fats_required'],
          ParamType.int,
          false,
        ),
        proteinRequired: deserializeParam(
          data['proteins_required'],
          ParamType.int,
          false,
        ),
        deviceId: deserializeParam(
          data['device_id'],
          ParamType.String,
          false,
        ),
        fcmToken: deserializeParam(
          data['fcm_token'],
          ParamType.String,
          false,
        ),
        weightValue: deserializeParam(
          data['weight_value'],
          ParamType.int,
          false,
        ),
        freeTrialsLeft: deserializeParam(
          data['free_trials_left'],
          ParamType.int,
          false,
        ),
        targetWeightValue: deserializeParam(
          data['target_weight_value'],
          ParamType.double,
          false,
        ),
        targetWeightUnit: deserializeParam(
          data['target_weight_unit'],
          ParamType.String,
          false,
        ),
        goalAchievementRate: deserializeParam(
          data['goal_achievement_rate'],
          ParamType.double,
          false,
        ),
        id: deserializeParam(
          data['id'],
          ParamType.int,
          false,
        ),
        username: deserializeParam(
          data['username'],
          ParamType.String,
          false,
        ),
        createdAt: deserializeParam(
          data['created_at'],
          ParamType.String,
          false,
        ),
        updatedAt: deserializeParam(
          data['updated_at'],
          ParamType.String,
          false,
        ),
        accessToken: deserializeParam(
          data['access_token'],
          ParamType.String,
          false,
        ),
        languageCode: deserializeParam(
          data['language_code'],
          ParamType.String,
          false,
        ),
        challenges: deserializeParam(
          data['challenges'],
          ParamType.int,
          false,
        ),
      );

  @override
  String toString() => 'UserDataStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is UserDataStruct &&
        firebaseId == other.firebaseId &&
        appleId == other.appleId &&
        googleId == other.googleId &&
        email == other.email &&
        signupBy == other.signupBy &&
        dob == other.dob &&
        gender == other.gender &&
        heightValue == other.heightValue &&
        heightUnit == other.heightUnit &&
        weightUnit == other.weightUnit &&
        workoutFrequencyId == other.workoutFrequencyId &&
        fitnessGoalId == other.fitnessGoalId &&
        accomplishmentId == other.accomplishmentId &&
        deviceId == other.deviceId &&
        fcmToken == other.fcmToken &&
        weightValue == other.weightValue &&
        targetWeightValue == other.targetWeightValue &&
        targetWeightUnit == other.targetWeightUnit &&
        goalAchievementRate == other.goalAchievementRate &&
        id == other.id &&
        username == other.username &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt &&
        accessToken == other.accessToken &&
        challenges == other.challenges;
  }

  @override
  int get hashCode => const ListEquality().hash([
        firebaseId,
        appleId,
        googleId,
        email,
        signupBy,
        dob,
        gender,
        heightValue,
        heightUnit,
        weightUnit,
        workoutFrequencyId,
        fitnessGoalId,
        accomplishmentId,
        deviceId,
        fcmToken,
        weightValue,
        targetWeightValue,
        targetWeightUnit,
        goalAchievementRate,
        id,
        username,
        createdAt,
        updatedAt,
        accessToken,
        challenges
      ]);
}

UserDataStruct createUserDataStruct({
  String? firebaseId,
  String? appleId,
  String? googleId,
  String? email,
  String? signupBy,
  String? dob,
  String? gender,
  int? heightValue,
  String? heightUnit,
  String? weightUnit,
  int? workoutFrequencyId,
  int? fitnessGoalId,
  int? accomplishmentId,
  String? deviceId,
  String? fcmToken,
  int? weightValue,
  int? freeTrialsLeft,
  double? targetWeightValue,
  String? targetWeightUnit,
  double? goalAchievementRate,
  int? id,
  String? username,
  String? createdAt,
  String? updatedAt,
  String? accessToken,
  String? languageCode,
  int? challenges,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    UserDataStruct(
      firebaseId: firebaseId,
      appleId: appleId,
      googleId: googleId,
      email: email,
      signupBy: signupBy,
      dob: dob,
      gender: gender,
      heightValue: heightValue,
      heightUnit: heightUnit,
      weightUnit: weightUnit,
      workoutFrequencyId: workoutFrequencyId,
      fitnessGoalId: fitnessGoalId,
      accomplishmentId: accomplishmentId,
      deviceId: deviceId,
      fcmToken: fcmToken,
      weightValue: weightValue,
      freeTrialsLeft: freeTrialsLeft,
      targetWeightValue: targetWeightValue,
      targetWeightUnit: targetWeightUnit,
      goalAchievementRate: goalAchievementRate,
      id: id,
      username: username,
      createdAt: createdAt,
      updatedAt: updatedAt,
      accessToken: accessToken,
      languageCode: languageCode,
      challenges: challenges,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

UserDataStruct? updateUserDataStruct(
  UserDataStruct? userData, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    userData
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addUserDataStructData(
  Map<String, dynamic> firestoreData,
  UserDataStruct? userData,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (userData == null) {
    return;
  }
  if (userData.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields =
      !forFieldValue && userData.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final userDataData = getUserDataFirestoreData(userData, forFieldValue);
  final nestedData = userDataData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = userData.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getUserDataFirestoreData(
  UserDataStruct? userData, [
  bool forFieldValue = false,
]) {
  if (userData == null) {
    return {};
  }
  final firestoreData = mapToFirestore(userData.toMap());

  // Add any Firestore field values
  userData.firestoreUtilData.fieldValues
      .forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getUserDataListFirestoreData(
  List<UserDataStruct>? userDatas,
) =>
    userDatas?.map((e) => getUserDataFirestoreData(e, true)).toList() ?? [];

import 'package:bugsnag_flutter_performance/bugsnag_flutter_performance.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_util.dart';
import 'package:cal_counti_a_i/pages/Ingredients/food_nutrition_details.dart';
import 'package:cal_counti_a_i/pages/Ingredients/log_empty_meal.dart';
import 'package:flutter/material.dart';
import 'package:cal_counti_a_i/flutter_flow/flutter_flow_theme.dart';
import '../../backend/api_requests/api_calls.dart';
import '../../backend/get_storage/storage_local.dart';
import '../../backend/schema/structs/food_item_struct.dart';
import 'food_database_log_item.dart';
import 'dart:async';

class FoodDatabaseLog extends StatefulWidget {
  const FoodDatabaseLog({super.key});

  @override
  State<FoodDatabaseLog> createState() => _FoodDatabaseLogState();
}

class _FoodDatabaseLogState extends State<FoodDatabaseLog> {
  final TextEditingController _controller = TextEditingController();
  List<FoodItem> searchResults = [];
  List<FoodItem> recentlyLogged = [];
  bool isLoading = false;
  bool hasSearched = false;
  String? accessToken;
  late StorageOperations storageOps;
  Timer? _debounceTimer;
  bool isEnglish = false;

  String searchKeyword = '';

  @override
  void initState() {
    super.initState();
    accessToken = FFAppState().authToken;
    storageOps = StorageOperations();
    _loadLoggedItems();

    _controller.addListener(() {
      _debounceTimer?.cancel();
      _debounceTimer =
          Timer(const Duration(milliseconds: 1000), _onSearchTextChanged);
    });
  }

  Future<void> _loadLoggedItems({BuildContext? context}) async {
    setState(() {
      recentlyLogged = storageOps.getLoggedFoodItems();
      searchResults = [];
      _controller.clear();
      hasSearched = false;
    });
    if (context != null) {
      await Future.delayed(Duration(milliseconds: 100));
      context.pop(true);
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onSearchTextChanged);
    _controller.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  Future<void> _onSearchTextChanged() async {
    final searchTerm = _controller.text.trim();
    if (searchKeyword.trim() == searchTerm) {
      return;
    }
    searchKeyword = searchTerm;
    if (searchTerm.isEmpty) {
      setState(() {
        searchResults = [];
        isLoading = false;
        hasSearched = false;
      });
      return;
    }

    setState(() {
      isLoading = true;
      hasSearched = true;
    });

    try {
      final response = await SearchFoodCall.call(
        accessToken: accessToken!,
        searchTerm: searchTerm,
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('The search request timed out');
        },
      );

      final results = SearchFoodCall.data(response.jsonBody) ?? [];
      searchResults = results
          .map((item) => FoodItem.fromJson(item as Map<String, dynamic>))
          .toList();
      isLoading = false;
      setState(() {});
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      final isEnglish = FFLocalizations.of(context).languageCode == 'en';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            e is TimeoutException
                ? (isEnglish
                    ? 'Search timed out. Please try again.'
                    : 'החיפוש חרג מהזמן. נסה שוב.')
                : (isEnglish
                    ? 'An error occurred while fetching search results. Please try again.'
                    : 'אירעה שגיאה בעת קבלת תוצאות החיפוש. נסה שוב.'),
          ),
        ),
      );
    }
  }

  String _getServingTypeLabel(FoodItem item) {
    // Safely access servingTypeId and servingTypes
    final servingTypeId = item.data.servingTypeId;
    final servingTypes = item.data.servingTypes;

    // If servingTypeId or servingTypes is null/empty, return a default label
    if (servingTypeId.isEmpty || servingTypes.isEmpty) {
      return isEnglish ? 'Serving' : 'מנה';
    }

    // Cast servingTypes to a list of maps
    // final typedServingTypes = servingTypes.cast<ServingType>();

    // Find the serving type with matching id
    final servingType = servingTypes.firstWhere(
      (type) => type.id == servingTypeId,
      orElse: () => ServingType(id: "", label: "No Serving Found"),
    );

    // Safely access the label
    final label = servingType.label;
    return label;
    // return label ?? (isEnglish ? 'Serving' : 'מנה');
  }

  String _formatTitle(FoodItem item, bool isEnglish) {
    final name = isEnglish ? item.enName : item.heName;
    final brand = item.data.brand;
    if (brand != null && brand.isNotEmpty) {
      return '$name • $brand';
    }
    return name;
  }

  @override
  Widget build(BuildContext context) {
    isEnglish = FFLocalizations.of(context).languageCode == 'en';
    return MeasuredWidget(
      name: 'FoodDatabase',
      builder: (context) => Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black),
              onPressed: () => Navigator.of(context).pop(true),
            ),
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            titleSpacing: 0,
            title: Text(
              isEnglish ? 'Food Database' : 'מאגר מזון',
              style: FlutterFlowTheme.of(context)
                  .headlineMedium
                  .copyWith(color: Colors.black, fontSize: 18),
            ),
            centerTitle: false,
          ),
          body: Padding(
            padding: EdgeInsets.only(
              bottom: FFAppState().bottomPadding,
              top: FFAppState().topPadding,
              right: 16,
              left: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        TextField(
                          controller: _controller,
                          autofocus: false,
                          decoration: InputDecoration(
                            hintText:
                                isEnglish ? 'Describe what you ate' : 'תאר מה אכלת',
                            filled: true,
                            fillColor: const Color(0xFFF7F7F7),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 14),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(16),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                    color: FlutterFlowTheme.of(context).primary)),
                          ),
                          style: FlutterFlowTheme.of(context).bodyLarge,
                        ),
                        const SizedBox(height: 12),
                        if (hasSearched) ...[
                          Text(
                            isEnglish ? 'Suggested Results' : 'תוצאות חיפוש',
                            style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: FlutterFlowTheme.of(context).primaryText,
                                ),
                          ),
                          const SizedBox(height: 12),
                          isLoading
                              ? const Center(
                                  child: CircularProgressIndicator(
                                  color: Colors.black,
                                ))
                              : searchResults.isEmpty
                                  ? Text(
                                      isEnglish
                                          ? 'No results found'
                                          : 'לא נמצאו תוצאות',
                                      style:
                                          FlutterFlowTheme.of(context).bodyMedium,
                                    )
                                  : SizedBox(
                                      child: ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: searchResults.length,
                                        itemBuilder: (context, index) {
                                          final FoodItem item =
                                              searchResults[index];
                                          final title =
                                              _formatTitle(item, isEnglish);
                                          final calories =
                                              item.data.calories.toString();
                                          final servingType =
                                              _getServingTypeLabel(item);
                                          final subtitle =
                                              '$calories cal • $servingType';
                                          return FoodDatabaseLogItem(
                                            title: title,
                                            subtitle: subtitle,
                                            calories: '$calories cal',
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      FoodNutritionDetails(
                                                    foodItem: item,
                                                  ),
                                                ),
                                              ).then((_) => _loadLoggedItems(
                                                  context: context));
                                            },
                                          );
                                        },
                                      ),
                                    ),
                          const SizedBox(height: 12),
                        ],
                        if (!hasSearched) ...[
                          Text(
                            isEnglish ? 'Recently logged' : 'נרשם לאחרונה',
                            style: FlutterFlowTheme.of(context).labelLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: FlutterFlowTheme.of(context).primaryText,
                                ),
                          ),
                          const SizedBox(height: 12),
                          recentlyLogged.isEmpty
                              ? Text(
                                  isEnglish ? 'No items found' : 'לא נמצאו פריטים',
                                  style: FlutterFlowTheme.of(context).bodyMedium,
                                )
                              : SizedBox(
                                  child: ListView.builder(
                                    shrinkWrap: true,
                                    physics: const NeverScrollableScrollPhysics(),
                                    itemCount: recentlyLogged.length,
                                    itemBuilder: (context, index) {
                                      final FoodItem item = recentlyLogged[index];
                                      final title = _formatTitle(item, isEnglish);
                                      final calories =
                                          item.data.calories.toString();
                                      final servingType =
                                          _getServingTypeLabel(item);
                                      final subtitle =
                                          '$calories cal • $servingType';
                                      return FoodDatabaseLogItem(
                                        title: title,
                                        subtitle: subtitle,
                                        calories: '$calories cal',
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  FoodNutritionDetails(
                                                foodItem: item,
                                              ),
                                            ),
                                          ).then((_) =>
                                              _loadLoggedItems(context: context));
                                        },
                                      );
                                    },
                                  ),
                                ),
                        ],
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const LogEmptyMeal(),
                        ),
                      ).then((_) => _loadLoggedItems(context: context));
                    },
                    icon: const Icon(Icons.edit, color: Colors.black),
                    label: Text(
                      isEnglish ? 'Log empty meal' : 'רשום ארוחה ריקה',
                      style: FlutterFlowTheme.of(context).titleMedium.copyWith(
                            color: Colors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.normal,
                          ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      side: BorderSide(
                          color: FlutterFlowTheme.of(context).grey, width: 1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(32),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      elevation: 0,
                    ),
                  ),
                ),
                SizedBox(height: FFAppState().bottomPadding),
              ],
            ),
          ),
        )
    );
  }
}

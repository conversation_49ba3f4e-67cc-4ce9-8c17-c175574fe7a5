// ignore_for_file: unnecessary_getters_setters

import 'package:cloud_firestore/cloud_firestore.dart';

import '/backend/schema/util/firestore_util.dart';

import '/flutter_flow/flutter_flow_util.dart';

class FatsStruct extends FFFirebaseStruct {
  FatsStruct({
    String? fatsRequired,
    String? fatsContained,
    String? fatsPercentage,
    FirestoreUtilData firestoreUtilData = const FirestoreUtilData(),
  })  : _fatsRequired = fatsRequired,
        _fatsContained = fatsContained,
        _fatsPercentage = fatsPercentage,
        super(firestoreUtilData);

  // "fats_required" field.
  String? _fatsRequired;
  String get fatsRequired => _fatsRequired ?? '';
  set fatsRequired(String? val) => _fatsRequired = val;

  bool hasFatsRequired() => _fatsRequired != null;

  // "fats_contained" field.
  String? _fatsContained;
  String get fatsContained => _fatsContained ?? '';
  set fatsContained(String? val) => _fatsContained = val;

  bool hasFatsContained() => _fatsContained != null;

  // "fats_percentage" field.
  String? _fatsPercentage;
  String get fatsPercentage => _fatsPercentage ?? '';
  set fatsPercentage(String? val) => _fatsPercentage = val;

  bool hasFatsPercentage() => _fatsPercentage != null;

  static FatsStruct fromMap(Map<String, dynamic> data) => FatsStruct(
        fatsRequired: castToType<String?>(data['fats_required']),
        fatsContained: castToType<String?>(data['fats_contained']),
        fatsPercentage: castToType<String?>(data['fats_percentage']),
      );

  static FatsStruct? maybeFromMap(dynamic data) =>
      data is Map ? FatsStruct.fromMap(data.cast<String, dynamic>()) : null;

  Map<String, dynamic> toMap() => {
        'fats_required': _fatsRequired,
        'fats_contained': _fatsContained,
        'fats_percentage': _fatsPercentage,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'fats_required': serializeParam(
          _fatsRequired,
          ParamType.String,
        ),
        'fats_contained': serializeParam(
          _fatsContained,
          ParamType.String,
        ),
        'fats_percentage': serializeParam(
          _fatsPercentage,
          ParamType.String,
        ),
      }.withoutNulls;

  static FatsStruct fromSerializableMap(Map<String, dynamic> data) =>
      FatsStruct(
        fatsRequired: deserializeParam(
          data['fats_required'],
          ParamType.String,
          false,
        ),
        fatsContained: deserializeParam(
          data['fats_contained'],
          ParamType.String,
          false,
        ),
        fatsPercentage: deserializeParam(
          data['fats_percentage'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'FatsStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is FatsStruct &&
        fatsRequired == other.fatsRequired &&
        fatsContained == other.fatsContained &&
        fatsPercentage == other.fatsPercentage;
  }

  @override
  int get hashCode =>
      const ListEquality().hash([fatsRequired, fatsContained, fatsPercentage]);
}

FatsStruct createFatsStruct({
  String? fatsRequired,
  String? fatsContained,
  String? fatsPercentage,
  Map<String, dynamic> fieldValues = const {},
  bool clearUnsetFields = true,
  bool create = false,
  bool delete = false,
}) =>
    FatsStruct(
      fatsRequired: fatsRequired,
      fatsContained: fatsContained,
      fatsPercentage: fatsPercentage,
      firestoreUtilData: FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
        delete: delete,
        fieldValues: fieldValues,
      ),
    );

FatsStruct? updateFatsStruct(
  FatsStruct? fats, {
  bool clearUnsetFields = true,
  bool create = false,
}) =>
    fats
      ?..firestoreUtilData = FirestoreUtilData(
        clearUnsetFields: clearUnsetFields,
        create: create,
      );

void addFatsStructData(
  Map<String, dynamic> firestoreData,
  FatsStruct? fats,
  String fieldName, [
  bool forFieldValue = false,
]) {
  firestoreData.remove(fieldName);
  if (fats == null) {
    return;
  }
  if (fats.firestoreUtilData.delete) {
    firestoreData[fieldName] = FieldValue.delete();
    return;
  }
  final clearFields = !forFieldValue && fats.firestoreUtilData.clearUnsetFields;
  if (clearFields) {
    firestoreData[fieldName] = <String, dynamic>{};
  }
  final fatsData = getFatsFirestoreData(fats, forFieldValue);
  final nestedData = fatsData.map((k, v) => MapEntry('$fieldName.$k', v));

  final mergeFields = fats.firestoreUtilData.create || clearFields;
  firestoreData
      .addAll(mergeFields ? mergeNestedFields(nestedData) : nestedData);
}

Map<String, dynamic> getFatsFirestoreData(
  FatsStruct? fats, [
  bool forFieldValue = false,
]) {
  if (fats == null) {
    return {};
  }
  final firestoreData = mapToFirestore(fats.toMap());

  // Add any Firestore field values
  fats.firestoreUtilData.fieldValues.forEach((k, v) => firestoreData[k] = v);

  return forFieldValue ? mergeNestedFields(firestoreData) : firestoreData;
}

List<Map<String, dynamic>> getFatsListFirestoreData(
  List<FatsStruct>? fatss,
) =>
    fatss?.map((e) => getFatsFirestoreData(e, true)).toList() ?? [];

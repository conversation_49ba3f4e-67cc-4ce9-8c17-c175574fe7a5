import '/flutter_flow/flutter_flow_util.dart';
import 'gemini_integrations_widget.dart' show GeminiIntegrationsWidget;
import 'package:flutter/material.dart';

class GeminiIntegrationsModel
    extends FlutterFlowModel<GeminiIntegrationsWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for TextField widget.
  FocusNode? textFieldFocusNode;
  TextEditingController? textController;
  String? Function(BuildContext, String?)? textControllerValidator;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    textFieldFocusNode?.dispose();
    textController?.dispose();
  }
}

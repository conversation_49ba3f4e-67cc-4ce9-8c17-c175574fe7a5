import '/backend/api_requests/api_calls.dart';
import '/backend/backend.dart';
import '/backend/schema/structs/index.dart';
import '/componentes/ingredient_item/ingredient_item_widget.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'meal_details_widget.dart' show MealDetailsWidget;
import 'package:flutter/material.dart';

class MealDetailsModel extends FlutterFlowModel<MealDetailsWidget> {
  ///  Local state fields for this page.

  bool isInProgress = false;

  MealDetailStruct? mealDetail;
  void updateMealDetailStruct(Function(MealDetailStruct) updateFn) {
    updateFn(mealDetail ??= MealDetailStruct());
  }

  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - API (Meal Detail)] action in meal_details widget.
  ApiCallResponse? apiResultMealDetail;
  // Models for ingredient_item dynamic component.
  late FlutterFlowDynamicModels<IngredientItemModel> ingredientItemModels;

  ApiCallResponse? deleteMealResponse;

  @override
  void initState(BuildContext context) {
    ingredientItemModels =
        FlutterFlowDynamicModels(() => IngredientItemModel());
  }

  @override
  void dispose() {
    ingredientItemModels.dispose();
  }
}
